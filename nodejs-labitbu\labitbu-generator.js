const { createRngSeedFromPubkey, DeterministicRng } = require('./utils/crypto');
const { applyHueShift, compositeImages, encodeToWebP, loadImageFromHex } = require('./utils/image');
const { LABITBU_CONFIG, IMAGE_CONFIG } = require('./config');

/**
 * 生成 Labitbu 图像字节数据
 * @param {string} pubkeyHex - 十六进制公钥
 * @param {Array<string>} baseImages - 基础图像十六进制数组
 * @param {Array<string>} accessories - 配件图像十六进制数组
 * @returns {Promise<Buffer>} 生成的图像字节数据 (4096 字节)
 */
async function generateLabitbuBytes(pubkeyHex, baseImages, accessories) {
  try {
    if (!baseImages || baseImages.length === 0) {
      throw new Error('No base images provided');
    }

    // 从公钥创建确定性随机数生成器
    const seed = createRngSeedFromPubkey(pubkeyHex);
    const rng = new DeterministicRng(seed);

    // 选择基础图像
    const baseIdx = rng.nextU32() % baseImages.length;
    const baseImageHex = baseImages[baseIdx];
    let baseImageBuffer = await loadImageFromHex(baseImageHex);

    // 选择配件 (可能没有配件)
    let accessoryIdx = null;
    if (accessories && accessories.length > 0) {
      const roll = rng.nextU32() % (accessories.length + 1);
      if (roll < accessories.length) {
        accessoryIdx = roll;
      }
    }

    // 生成色调偏移
    const hueShift = rng.nextU32() % IMAGE_CONFIG.HUE_SHIFT_RANGE;

    // 应用色调偏移
    baseImageBuffer = await applyHueShift(baseImageBuffer, hueShift);

    // 如果有配件，进行合成
    if (accessoryIdx !== null) {
      const accessoryHex = accessories[accessoryIdx];
      const accessoryBuffer = await loadImageFromHex(accessoryHex);
      baseImageBuffer = await compositeImages(baseImageBuffer, accessoryBuffer);
    }

    // 编码为 WebP
    const webpData = await encodeToWebP(baseImageBuffer);

    // 填充到目标大小
    const paddedData = Buffer.alloc(LABITBU_CONFIG.TARGET_SIZE);
    webpData.copy(paddedData, 0, 0, Math.min(webpData.length, LABITBU_CONFIG.TARGET_SIZE));

    return paddedData;
  } catch (error) {
    throw new Error(`生成 Labitbu 字节数据失败: ${error.message}`);
  }
}

/**
 * 从字节数据构建 Merkle 路径
 * @param {Buffer} bytes - 字节数据
 * @returns {Array<Buffer>} Merkle 路径哈希数组
 */
function buildMerklePathFromBytes(bytes) {
  const crypto = require('crypto');
  const chunks = [];
  
  // 将字节数据分成 32 字节的块
  for (let i = 0; i < bytes.length; i += 32) {
    const chunk = Buffer.alloc(32);
    const remaining = Math.min(32, bytes.length - i);
    bytes.copy(chunk, 0, i, i + remaining);
    chunks.push(chunk);
  }

  return chunks;
}

/**
 * 预览生成的 Labitbu
 * @param {string} pubkeyHex - 十六进制公钥
 * @param {Array<string>} baseImages - 基础图像十六进制数组
 * @param {Array<string>} accessories - 配件图像十六进制数组
 * @returns {Promise<Object>} 预览信息
 */
async function previewLabitbu(pubkeyHex, baseImages, accessories) {
  try {
    const seed = createRngSeedFromPubkey(pubkeyHex);
    const rng = new DeterministicRng(seed);

    // 确定选择的组合
    const baseIdx = rng.nextU32() % baseImages.length;
    
    let accessoryIdx = null;
    if (accessories && accessories.length > 0) {
      const roll = rng.nextU32() % (accessories.length + 1);
      if (roll < accessories.length) {
        accessoryIdx = roll;
      }
    }

    const hueShift = rng.nextU32() % IMAGE_CONFIG.HUE_SHIFT_RANGE;

    // 生成实际图像
    const imageBytes = await generateLabitbuBytes(pubkeyHex, baseImages, accessories);

    return {
      baseImageIndex: baseIdx,
      accessoryIndex: accessoryIdx,
      hueShift,
      imageBytes,
      hasAccessory: accessoryIdx !== null,
      totalCombinations: calculateTotalCombinations(baseImages.length, accessories ? accessories.length : 0)
    };
  } catch (error) {
    throw new Error(`预览 Labitbu 失败: ${error.message}`);
  }
}

/**
 * 计算总组合数
 * @param {number} baseCount - 基础图像数量
 * @param {number} accessoryCount - 配件数量
 * @returns {number} 总组合数
 */
function calculateTotalCombinations(baseCount, accessoryCount) {
  const accessoryOptions = accessoryCount + 1; // 包括无配件选项
  const hueOptions = IMAGE_CONFIG.HUE_SHIFT_RANGE;
  return baseCount * accessoryOptions * hueOptions;
}

module.exports = {
  generateLabitbuBytes,
  buildMerklePathFromBytes,
  previewLabitbu,
  calculateTotalCombinations
};

const express = require('express');
const cors = require('cors');
const path = require('path');
const api = require('./api');
const bodyParser = require('body-parser');

const app = express();

// 配置 CORS
app.use(cors());

// 配置请求体大小限制 - 使用 body-parser
app.use(bodyParser.json({limit: '50mb'}));
app.use(bodyParser.urlencoded({limit: '50mb', extended: true}));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// API 路由
app.use('/api', api);

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ error: err.message || 'Internal server error' });
});

// 启动服务器
const PORT = process.env.PORT || 81;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
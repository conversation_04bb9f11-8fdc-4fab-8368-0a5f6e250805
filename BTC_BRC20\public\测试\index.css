/* ========================================
   10. 铭文信息模块样式 (Inscription Info Styles)
   ======================================== */

.inscription-info-container {
  border: 1px solid #dee2e6;
  padding: 20px;
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: .375rem;
}

.dark-mode .inscription-info-container {
  background-color: #333;
  border-color: #444;
}

.inscription-info-left {
  padding-right: 15px;
}

.inscription-title {
  font-size: 2rem;
  font-weight: bold;
}

.dark-mode .inscription-title {
  color: #eee;
}

.social-icons a {
  margin-left: 8px;
  color: #6c757d;
  transition: color 0.2s;
}

.social-icons a:hover {
  color: #212529;
}

.dark-mode .social-icons a {
  color: #adb5bd;
}

.dark-mode .social-icons a:hover {
  color: #fff;
}

.social-icons svg {
  width: 20px;
  height: 20px;
}

.inscription-progress-bar {
  height: 14px;
  border-radius: 7px;
  background-color: #e9ecef;
}

.dark-mode .inscription-progress-bar {
  background-color: #495057;
}

.progress-percentage {
  font-size: 0.8rem;
  font-weight: bold;
  color: #6c757d;
  white-space: nowrap;
}

.dark-mode .progress-percentage {
  color: #adb5bd;
}

.progress-text {
  font-size: 0.8rem;
  color: #6c757d;
  margin: 0;
  padding-top: 4px;
}

.dark-mode .progress-text {
  color: #adb5bd;
}

.inscription-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 20px;
  font-size: 0.9rem;
  margin-top: 15px;
}

.inscription-details-grid .detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f1f3f5;
  padding-bottom: 8px;
}

.dark-mode .inscription-details-grid .detail-item {
  border-bottom-color: #373b3e;
}

.inscription-details-grid .detail-item span {
  color: #6c757d;
}

.dark-mode .inscription-details-grid .detail-item span {
  color: #adb5bd;
}

.inscription-details-grid .detail-item strong,
.inscription-details-grid .detail-item a {
  font-weight: 600;
  color: #212529;
  text-decoration: none;
}

.dark-mode .inscription-details-grid .detail-item strong,
.dark-mode .inscription-details-grid .detail-item a {
  color: #eee;
}

.inscription-details-grid .detail-item a:hover {
  text-decoration: underline;
}

/* ========================================
   11. 持有人列表模块样式 (Holders List Styles)
   ======================================== */

.holders-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.dark-mode .holders-list {
  border-left-color: #444;
}

.holders-table-container {
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: .375rem;
  padding: 0 8px;
  flex-grow: 1; /* 新增：让此容器填充剩余空间 */
  min-height: 0; /* 新增：防止flex溢出的健壮性设置 */
}

.holders-table th,
.holders-table td {
  padding: 6px 8px;
  vertical-align: middle;
  font-size: 0.85rem;
  white-space: nowrap;
}

.holders-table th {
  color: #6c757d;
  font-weight: normal;
  border: none;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 1;
}

.dark-mode .holders-table th {
  color: #adb5bd;
}

.holders-table td {
  border: none;
  font-weight: 500;
}

.holders-table .wallet-link {
  color: #0d6efd;
  text-decoration: none;
  font-weight: normal;
}

.holders-table .wallet-link:hover {
  text-decoration: underline;
}

.dark-mode .holders-table .wallet-link {
  color: #4dabf7;
}

.holders-table tbody tr:hover {
  background-color: #f8f9fa;
}

.dark-mode .holders-table tbody tr:hover {
  background-color: #3a3a3a;
}

.dark-mode .holders-table th {
  background-color: #333;
}

.holders-table-container::-webkit-scrollbar {
  width: 5px;
}

.holders-table-container::-webkit-scrollbar-track {
  background: transparent;
}

.holders-table-container::-webkit-scrollbar-thumb {
  background: #ced4da;
  border-radius: 5px;
}

.dark-mode .holders-table-container::-webkit-scrollbar-thumb {
  background: #495057;
}

/* ========================================
   12. 响应式设计 (Responsive Design)
   ======================================== */

@media (max-width: 991px) {
  .inscription-info-left {
    padding-right: 0;
  }
  .holders-list {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    height: auto; /* 新增：重置高度 */
  }
  .dark-mode .holders-list {
    border-top-color: #444;
  }
  .inscription-details-grid {
    grid-template-columns: 1fr;
  }
  .holders-table-container {
    max-height: 300px; /* 新增：在移动端限制最大高度 */
  }
}

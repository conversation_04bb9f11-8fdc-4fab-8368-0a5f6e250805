# BRC20 铸造工具前端开发文档

## 目录
1. [项目概述](#项目概述)
2. [技术栈](#技术栈)
3. [项目结构](#项目结构)
4. [核心功能模块](#核心功能模块)
5. [组件文档](#组件文档)
6. [工具函数](#工具函数)
7. [数据管理](#数据管理)
8. [安全机制](#安全机制)
9. [性能优化](#性能优化)
10. [开发指南](#开发指南)

## 项目概述

### 简介
本项目是BRC20代币铸造系统的前端实现，提供用户友好的界面进行BRC20代币的铸造、管理和交易加速等操作。

### 特点
- 实时数据更新
- 响应式设计
- 多主题支持（深色/浅色模式）
- 高性能渲染
- 安全数据传输
- 交易状态追踪
- 批量铸造处理

## 技术栈

- HTML5
- CSS3
- JavaScript (ES6+)
- jQuery 3.6.0
- Bootstrap 5
- Chart.js
- CryptoJS

## 项目结构

```
public/
├── index.html          # 主页面HTML
├── index.css           # 主样式文件
├── dark-mode.css       # 暗黑模式样式
├── bootstrap.min.css   # Bootstrap样式库
├── js/                 # JavaScript模块
│   ├── core.js         # 核心配置和基础工具函数
│   ├── wallet.js       # 钱包管理相关功能
│   ├── mint.js         # 铭文铸造相关功能
│   ├── transaction.js  # 交易处理和管理功能
│   └── ui.js           # 用户界面交互和事件处理
├── c.js                # 加密库 (CryptoJS)
├── p.js                # 图表库
├── u.js                # 工具函数库
├── env.js              # 环境配置
└── 各种图标和资源文件
```

## 核心功能模块

### 1. core.js - 核心配置和工具函数

```javascript
// 主要功能：提供全局配置、常量和基础工具函数
// 文件大小：约6.4KB

// 核心功能：
- 全局常量和配置管理
- 加密解密功能
- 时间同步机制
- 基础工具函数
- 弹窗提示
- 数据格式化
- 地址验证
- 队列处理机制
- 区块高度检查
```

### 2. wallet.js - 钱包管理模块

```javascript
// 主要功能：处理钱包相关的所有操作
// 文件大小：约17KB

// 核心功能：
- 钱包信息更新和验证
- WIF私钥处理
- 钱包余额查询
- UTXO列表管理
- 参数存储和加载
- OG用户识别
```

### 3. mint.js - 铭文铸造模块

```javascript
// 主要功能：管理铭文铸造过程
// 文件大小：约47KB

// 核心功能：
- 铭文内容生成和验证
- 铸造数量计算
- 费用计算和显示
- 铸造交易创建
- 符文同步铸造支持
- 铸造状态管理
- UTXO利用优化
- 最大铸造量计算
```

### 4. transaction.js - 交易处理模块

```javascript
// 主要功能：管理交易记录和状态
// 文件大小：约32KB

// 核心功能：
- 交易数据本地存储
- 交易列表渲染
- 交易状态更新
- 交易加速功能
- 交易删除功能
- 已确认交易管理
- 区块链交易提交
- 订单状态追踪
```

### 5. ui.js - 用户界面模块

```javascript
// 主要功能：处理所有UI相关的交互和事件
// 文件大小：约51KB

// 核心功能：
- 页面初始化
- 事件绑定
- 表单验证
- 模态框管理
- 深色模式切换
- UTXO拆分界面
- 动态表单更新
- 热门铭文展示
- 实时费率更新
- 多/单地址接收切换
```

## 组件文档

### 1. 铭文信息卡片

```html
功能: 展示铭文基本信息和状态
相关函数: fetchBRC20Info(tick)
更新周期: 手动刷新、区块高度变化时
显示内容:
- 铭文名称
- 限制
- 铸造进度
- 状态信息
```

### 2. 交易记录列表

```html
功能: 展示未确认和已确认的交易
相关函数: renderMintingList(transactions)
更新方式: 
- 页面加载时
- 新交易创建
- 交易状态变化
操作:
- 加速交易
- 删除交易
- 批量操作
```

### 3. 费用计算器

```html
功能: 动态计算铸造费用
相关函数: updateFeeDisplay()
计算内容:
- 矿工费
- 铭文占用费
- 服务费
- 单张成本
- 总成本
```

### 4. UTXO拆分工具

```html
功能: 管理矿工钱包UTXO
相关函数: 
- setupUtxoSplitModal()
- confirmSplitUtxo()
功能:
- UTXO选择
- 拆分数量配置
- 费率选择
- 拆分预览
```

## 工具函数

### 1. 数据加密解密
```javascript
// 加密函数
function en1999_20240924(data, key = STORAGE_ENCRYPTION_KEY) {
    return CryptoJS.AES.encrypt(data, key).toString();
}

// 解密函数
function de1999_20240924(encryptedData, key = STORAGE_ENCRYPTION_KEY) {
    const bytes = CryptoJS.AES.decrypt(encryptedData, key);
    return bytes.toString(CryptoJS.enc.Utf8);
}
```

### 2. 时间同步
```javascript
// 获取同步时间戳
function getSyncedTimestamp() {
    const localTime = Date.now();
    return timeOffset ? localTime + timeOffset : localTime;
}

// 校准时间
function calibrateTime() {
    // 与服务器同步时间
}
```

### 3. 数据格式化
```javascript
// 数字格式化
function formatNumber(num) {
    if (num >= 100000000) { // 1亿及以上
        return (num / 100000000).toFixed(2) + '亿';
    } else if (num >= 100000) { // 10万及以上
        return (num / 10000).toFixed(2) + '万';
    }
    return num.toString();
}

// 地址格式化
function formatAddress(address) {
    return address.length > 42 ? 
        `${address.slice(0, 20)}...${address.slice(-20)}` : 
        address;
}
```

## 数据管理

### 1. 本地存储
```javascript
// 存储数据
function saveParams(key, value) {
    localStorage.setItem(key, en1999_20240924(value));
}

// 读取数据
function loadParams() {
    const params = {};
    // 从不同的存储键加载数据
    const keys = ['tick', 'amount', 'orditext', 'wif', 'feeRate', 'maxFee', 'count', 'inscriptionSize', 'protect'];
    keys.forEach(key => {
        const storedValue = localStorage.getItem(key + istestnet);
        if (storedValue) {
            params[key] = de1999_20240924(storedValue);
        }
    });
    return params;
}
```

### 2. 交易存储管理
```javascript
// 保存交易记录
function updateLocalStorageWithTransactions(transactions) {
    let storedTransactions = localStorage.getItem('TransactionsV1'+istestnet);
    storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
    transactions.forEach(tx => {
        storedTransactions[tx.utxoi] = { ...tx };
    });
    localStorage.setItem('TransactionsV1' + istestnet, JSON.stringify(storedTransactions));
}

// 加载交易记录
function renderTransactionsFromLocalStorage() {
    let storedTransactions = localStorage.getItem('TransactionsV1'+istestnet);
    storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
    const transactions = Object.values(storedTransactions);
    renderMintingList(transactions);
}
```

## 安全机制

### 1. 数据加密
- 使用AES加密算法保护敏感数据
- 私钥加密传输
- 本地存储加密

### 2. 输入验证
- WIF格式验证
- 比特币地址验证
- 数值范围检查
- 参数完整性验证

### 3. 交易确认机制
- 交易提交前确认
- 费用预览和确认
- UTXO使用确认

## 性能优化

### 1. 模块化设计
- 代码按功能拆分为5个主要模块
- 减少全局变量污染
- 提高代码可维护性

### 2. 资源加载优化
- 样式文件分离(普通模式/暗黑模式)
- 按需加载功能

### 3. 事件处理优化
- 防抖节流处理
- 事件委托
- 按需更新UI

## 开发指南

### 1. 添加新功能
1. 确定功能所属的模块(core/wallet/mint/transaction/ui)
2. 在相应模块中实现功能
3. 在模块末尾导出到全局命名空间
4. 在其他模块中引用(如需)

### 2. 调试技巧
- 使用浏览器开发者工具
- 检查localStorage数据
- 监控网络请求
- 断点调试

### 3. 代码规范
- 遵循模块化结构
- 函数命名清晰表明用途
- 注释关键逻辑
- 错误处理完善

## 常见问题

### 1. 性能问题
- 交易列表渲染慢：使用虚拟列表优化
- 本地存储过大：定期清理旧数据
- 加密解密耗时：优化加密算法或减少加密频率

### 2. 兼容性问题
- 移动设备适配：使用响应式设计
- 不同浏览器兼容：使用polyfill
- localStorage限制：分片存储大数据

## 更新日志

### 版本 2.0.0 (2024-02-25)
- 代码模块化重构
- 拆分为5个功能模块
- 优化UI响应性
- 增加暗黑模式支持
- 新增符文铸造功能

### 版本 1.0.0
- 初始版本发布
- 基础功能实现
- 核心模块完成 
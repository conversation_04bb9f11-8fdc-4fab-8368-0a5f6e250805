const rpc = require("./rpc");
const dotenv = require("dotenv");
const CryptoJS = require('crypto-js');
dotenv.config();

const API_ENCRYPTION_KEY = 'ybot_1999eth_x_com_202405101298';

function encryptData(data, key = API_ENCRYPTION_KEY) {
  return CryptoJS.AES.encrypt(data, key).toString();
}

function decryptData(encryptedData, key = API_ENCRYPTION_KEY) {
  const bytes = CryptoJS.AES.decrypt(encryptedData, key);
  return bytes.toString(CryptoJS.enc.Utf8);
}

// 查询tx状态
async function txconfirm1(txDataList) {
  const batchSendRequests = txDataList.map((txData, index) => ({
    id: index,
    jsonrpc: "2.0",
    method: "getmempoolancestors",
    params: [txData.tx, true],
  }));

  try {
    const responses = await rpc.rpc_batch(batchSendRequests);
    const finalTxDataList = responses.reduce((result, resp, index) => {
      if (resp.result) {
        const total = parseInt(Object.keys(resp.result).length) + 1;
        if (total !== txDataList[index].total) {
          result.push({
            tx: txDataList[index].tx,
            total: total,
          });
        }
      }
      return result;
    }, []);
    return finalTxDataList;
  } catch (error) {
    console.error("Error during batch transaction processing:", error);
    throw new Error("txconfirm发生错误!");
  }
}

async function txconfirm(txDataList) {
  const batchSendRequests = txDataList.map((txData, index) => ({
    id: index,
    jsonrpc: "2.0",
    method: "getrawtransaction",
    params: [txData.tx, true],
  }));

  try {
    const responses = await rpc.rpc_batch(batchSendRequests);
    const confirmedTxs = responses.reduce((result, resp, index) => {
      if (resp.result && resp.result.confirmations > 0) {
        result.push({
          tx: txDataList[index].tx,
          confirmations: resp.result.confirmations,
        });
      }
      return result;
    }, []);
    return confirmedTxs;
  } catch (error) {
    console.error("Error during batch transaction processing:", error);
    throw new Error("txconfirm发生错误!");
  }
}

async function txconfirm_runes(txDataList) {
  const batchSendRequests = txDataList.map((txData, index) => ({
    id: index,
    jsonrpc: "2.0",
    method: "getrawtransaction",
    params: [txData.tx, true],
  }));

  try {
    const responses = await rpc.rpc_batch(batchSendRequests);
    const confirmedTxs = responses.reduce((result, resp, index) => {
      if (resp.result && resp.result.confirmations > 0) {
        result.push({
          tx: txDataList[index].tx,
          confirmations: resp.result.confirmations,
        });
      }
      return result;
    }, []);
    return confirmedTxs;
  } catch (error) {
    console.error("Error during batch transaction processing:", error);
    throw new Error("txconfirm发生错误!");
  }
}

const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

module.exports = {
  sleep,
  txconfirm,
  txconfirm_runes,
  encryptData,
  decryptData
};
/* dark-mode.css */

body {
    background-color: #1a1a1a;
    color: #e0e0e0;
}

.bg-white {
    background-color: #2c2c2c !important;
}

.text-center {
    color: #e0e0e0;
}

.btn-primary {
    background-color: #4c94f3;
    border-color: #4c94f3;
}

.btn-primary:hover {
    background-color: #2b4764;
    border-color: #2b4764;
}

.btn-danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.form-control {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
    color: #e0e0e0;
}

.form-control:focus {
    background-color: #3a3a3a;
    border-color: #4c94f3;
    color: #e0e0e0;
}

.dropdown-menu {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
}

.dropdown-item {
    color: #e0e0e0;
}

.dropdown-item:hover {
    background-color: #4c94f3;
    color: #ffffff;
}

.modal-content {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
}

.modal-header, .modal-footer {
    border-color: #4a4a4a;
}

.close {
    color: #e0e0e0;
}

.utxo-group, .transaction-item {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
}

.fee-option {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
    color: #e0e0e0;
}

.fee-option:hover {
    background-color: #4a4a4a;
}

.fee-option.active {
    background-color: #4c94f3;
    border-color: #4c94f3;
}

.rune-info-box {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
}

.price-text {
    color: #b8b8b8;
}

.gray-text {
    color: #a0a0a0;
}

.rune-text {
    color: #4caf50;
}

.rune-name-text {
    color: #e74c3c;
}

.rune-mint-text {
    color: #2ecc71;
}

.tooltip-inner {
    background-color: #4c94f3;
}

.tooltip.bs-tooltip-top::after {
    border-top-color: #4c94f3;
}

#noTransactionsMessage, #noconfirmedMessage {
    color: #a0a0a0;
}

.progress {
    background-color: #3a3a3a;
}

.link {
    color: #3498db;
}

.link:hover {
    color: #2980b9;
}

.hot-springs {
    color: #e74c3c;
}

.rune-item {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
}

.rune-item:hover {
    background-color: #3a3a3a;
}

.rune-card {
    background-color: #2c2c2c;
    border-color: #4a4a4a;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* 调整图表颜色 */
#myChart {
    filter: invert(1) hue-rotate(180deg);
}

/* 调整输入错误提示 */
.input-error {
    border-color: #e74c3c !important;
}

.error-message {
    color: #e74c3c;
}

/* 调整复选框样式 */
.form-check-input {
    background-color: #625f5f;
    border-color: #bab5b5;
}

.form-check-input:checked {
    background-color: #4c94f3;
    border-color: #4c94f3;
}

/* 调整滚动条样式 */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #2c2c2c;
}

::-webkit-scrollbar-thumb {
    background: #4a4a4a;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 符文看板样式 */
.rune-board {
    background-color: #2c2c2c;
    color: #e0e0e0;
}

/* 任务列表样式 */
.task-list {
    background-color: #2c2c2c;
    color: #e0e0e0;
}

/* 内存池透视容器样式 */
.mempool-container {
    background-color: #2c2c2c;
    color: #e0e0e0;
}

/* 查询结果样式 */
.query-result {
    background-color: #2c2c2c;
    color: #e0e0e0;
}

/* 表格样式 */
.table {
    color: #e0e0e0;
}

.table td, .table th {
    border-color: #4a4a4a;
}

/* 图表样式 */
.chart-dark-mode {
    filter: invert(1) hue-rotate(180deg);
}

/* 进度条样式 */
.progress-bar-dark {
    background-color: #4c94f3;
}

/* 任务列表背景 */
#confirmedList .transaction-item {  
    background-color: #2c2c2c;
    border-color: #565758;
}

/* 符文看板背景 */
.left-panel .rune-item {
    background-color: #2c2c2c;
    border-color: #565758;
}

.left-panel .rune-header .rune-name-text {
    color: #e0e0e0;
}

.left-panel .green-percentage {
    color: #e0e0e0;
}

.left-panel .rune-details {
    color: #e0e0e0;
}

.progress {
    background-color: #e0e0e0;
}

/* 内存池背景 */
.rune-progress-container {
    background-color: #2c2c2c;
    border: 1px solid #565758
}


.button-on.active, .button-off.active {
    background-color: #4c94f3;
    border-color: #565758;
}

.button-on, .button-off {
    background-color: #3a3a3a;
    border-color: #565758;
}

#addressQuantity {
    background-color: #3a3a3a;
    border: 1px solid #565758;
}

.form-control:disabled, .form-control:read-only {
    background-color: #3a3a3a;
    border-color: #565758;
}

.btn-secondary {
    background-color: #3a3a3a;
    border-color: #565758;
}

.btn-light {
    color: #e0e0e0;
    background-color: #3a3a3a;
    border-color: #565758;
}

#mintDetailsContainer th {
    background-color: #3a3a3a;
    border-color: #565758;
}

.red-bold {
    color: #e0e0e0;
}

.text-success {
    color: #e0e0e0 !important;
}

.left-panel .rune-item:hover {
    background-color: #3a3a3a;
    cursor: pointer;
}

/* 修复链接和标签在暗黑模式下的颜色问题 */
.tick-details {
    color: #66b3ff !important; /* 暗黑模式下的链接颜色 */
}

.tick-details:hover {
    color: #4da6ff !important;
}

.form-check-label {
    color: #e0e0e0 !important; /* 确保标签文字在暗黑模式下可见 */
}

/* 移除全局链接样式，避免影响其他链接 */

/* 确保其他文本元素的颜色 */
label {
    color: #e0e0e0 !important;
}

/* 内存池透视在暗黑模式下的样式 */
.rune-progress-container {
    background-color: #333 !important;
    border-color: #444 !important;
    color: #eee !important;
}

.rune-progress-container label {
    color: #eee !important;
}

.rune-progress-container .form-label {
    color: #eee !important;
}

.rune-text {
    color: rgb(6, 145, 57) !important;
}

.rune-progress-container b {
    color: #eee !important;
}
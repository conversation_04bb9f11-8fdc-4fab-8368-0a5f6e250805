# BRC20 铸造与管理系统后端开发文档

## 目录
1. [项目概述](#项目概述)
2. [系统架构](#系统架构)
3. [核心功能](#核心功能)
4. [API接口文档](#API接口文档)
5. [安装部署](#安装部署)
6. [开发指南](#开发指南)

## 项目概述

### 项目简介
本项目是一个基于Node.js的BRC20代币铸造与管理系统，提供完整的BRC20代币铸造、UTXO管理、交易加速等功能。系统采用Express框架构建RESTful API，使用工作线程池处理高负载任务，并实现了高效的缓存管理机制。新增了符文（Runes）铸造支持，优化了交易处理和缓存策略。

### 技术栈
- Node.js
- Express.js
- WorkerPool
- Crypto.js
- Axios
- Bitcoin相关库(@cmdcode/tapscript等)
- 缓存管理系统

## 系统架构

### 整体架构
```
├── 前端静态资源 (public/)
├── API服务层 (api.js)
├── 核心业务逻辑 (brc20.js)
├── 符文编码模块 (runes.js)
├── 工作线程池 (worker.js)
└── 服务器配置 (app.js)
```

### 核心模块
1. **API服务层**
   - 请求处理与路由
   - 数据加密解密
   - 高效缓存管理
   - 统一错误处理
   - 工作线程池集成

2. **业务逻辑层**
   - BRC20操作
   - 符文（Runes）处理
   - UTXO管理
   - 交易处理
   - 钱包管理

3. **缓存管理系统**
   - 智能缓存策略
   - 差异化缓存时间
   - 并发请求处理
   - 自动清理机制

## 核心功能

### 1. BRC20代币铸造
- 支持批量铸造
- 自动费用计算
- 交易确认跟踪
- 铭文数据处理
- 符文同步铸造

### 2. UTXO管理
- UTXO智能查询
- UTXO拆分优化
- 余额实时管理
- 交易智能构建
- UTXO保护机制

### 3. 交易处理
- 费用动态调整
- 批量交易处理
- 交易加速优化
- 智能重试机制

### 4. 缓存系统
- 多级缓存策略
- 智能过期处理
- 并发请求优化
- 缓存预热机制

### 5. 安全特性
- 数据加密传输
- 时间戳验证
- 参数严格校验
- 异常智能处理

## API接口文档

### 通用说明
- 基础URL: `/api/v1`
- 请求方式: POST
- 数据格式: JSON
- 加密方式: AES
- 时间戳验证: 5分钟有效期

### 缓存配置
```javascript
const CACHE_CONFIG = {
  fees: 10000,        // 费用信息缓存10秒
  mints_hot: 20000,   // 热门铭文缓存20秒
  getRuneInfo: 20000  // 符文信息缓存20秒
};
```

### 接口列表

#### 1. 服务器时间
```
GET /server_time
响应: {
    code: 0,
    msg: 'ok',
    data: timestamp
}
```

#### 2. 热门铭文查询（优化版）
```
POST /mints_hot
请求: {
    data: "加密的时间戳"
}
响应: {
    total: number,
    results: Array<MintInfo> // 智能排序的热门铭文列表
}
```

#### 3. 动态费用查询
```
POST /getfee
请求: {
    data: "加密的时间戳"
}
响应: {
    fastestFee: number,
    halfHourFee: number,
    hourFee: number
}
```

#### 4. 符文信息查询
```
POST /getRuneInfo
请求: {
    data: "加密的符文数据"
}
响应: {
    code: 0,
    msg: 'success',
    data: {
        limitPerMint: string,
        supply: string,
        minted: string,
        selfMint: boolean
    }
}
```

#### 5. 智能铸造计算
```
POST /mintable
请求: {
    data: "加密的{
        wif,
        feeRate,
        maxFee,
        orditext,
        inscriptionSize,
        protect,
        receiveAddresses,
        addressType,
        count,
        mintHexData
    }"
}
响应: {
    totalUtxoValue: number,
    maxMintCount: number,
    inscriptionSize: number,
    commitFee: number,
    revealFee: number,
    LastOneFee: number,
    utxoList: Array,
    count: number,
    utxocont: number,
    serviceFee: number,
    oguser: boolean
}
```

#### 6. 高级铸造操作
```
POST /mint
请求: {
    data: "加密的{
        wif,
        inscriptionSize,
        protect,
        orditext,
        tick,
        receiveAddresses,
        feeRate,
        maxFee,
        count,
        activeUtxoi,
        addressType,
        RescueMode,
        runes,
        mintHexData
    }"
}
响应: Array<MintResult>
```

#### 7. 钱包信息查询
```
POST /wallet-info
请求: {
    data: "加密的{wif, addressType}"
}
响应: {
    address: string,
    balance: number,
    oguser: boolean
}
```

#### 8. UTXO智能查询
```
POST /wallet-utxo
请求: {
    data: "加密的{time, address}"
}
响应: Array<UTXO>
```

#### 9. UTXO优化拆分
```
POST /split-utxo
请求: {
    data: "加密的{
        wif,
        addressType,
        utxos,
        splitCount,
        feeRate
    }"
}
响应: {
    code: number,
    txid: string
}
```

#### 10. 交易加速优化
```
POST /accelerate
请求: {
    data: "加密的交易数据"
}
响应: Array<{
    tx: TransactionInfo,
    index: number,
    success: boolean,
    error?: string
}>
```

## 性能优化

### 1. 缓存优化
```javascript
const cacheManager = {
  caches: new Map(),
  pendingPromises: new Map(),
  
  async ensureCacheData(cacheKey, cacheDuration, updateFunction) {
    // 智能缓存管理实现
  },
  
  cleanup() {
    // 自动清理过期缓存
  }
};
```

### 2. 并发处理
```javascript
const workerpool = require('workerpool');
const pool = workerpool.pool('./worker.js', {
  minWorkers: 'max'
});
```

### 3. 错误处理
```javascript
try {
  // 业务逻辑
} catch (error) {
  if (error.response?.data?.includes('Too many unspent transaction outputs')) {
    return "limits";
  }
  // 其他错误处理
}
```

## 部署说明

### 环境要求
- Node.js >= 14.x
- NPM >= 6.x
- 内存 >= 4GB
- 支持工作线程

### 配置项
```javascript
// .env 文件配置
TESTNET=false          // 是否使用测试网络
TOLL=true             // 是否开启收费功能
API_ENCRYPTION_KEY=xxx // API加密密钥
```

### 启动命令
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## 安全建议

### 1. 数据安全
- 所有API请求必须加密
- 私钥本地加密存储
- 敏感数据传输加密

### 2. 交易安全
- UTXO保护机制
- 费用预估保护
- 交易签名验证

### 3. 系统安全
- 请求频率限制
- IP访问控制
- 日志监控告警

## 维护指南

### 1. 日常维护
- 监控系统资源
- 检查错误日志
- 清理过期缓存
- 更新依赖包

### 2. 故障处理
- 交易广播失败
- UTXO锁定问题
- 网络连接异常
- 内存溢出处理

### 3. 性能优化
- 缓存命中率优化
- 并发请求控制
- 资源使用监控
- 数据库查询优化 
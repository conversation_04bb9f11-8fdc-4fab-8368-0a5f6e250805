const axios = require('axios');
const { API_URL } = require('../config');

/**
 * UTXO 类
 */
class UTXO {
  constructor(txid, vout, value, confirmed = true) {
    this.txid = txid;
    this.vout = vout;
    this.value = value;
    this.confirmed = confirmed;
  }
}

/**
 * 获取地址的 UTXOs
 * @param {string} address - 比特币地址
 * @param {number} retries - 重试次数
 * @param {boolean} confirmed - 是否只获取已确认的 UTXO
 * @returns {Promise<UTXO[]>} UTXO 数组
 */
async function fetchUTXOs(address, retries = 3, confirmed = true) {
  let lastError;

  for (let i = 0; i < retries; i++) {
    try {
      const url = `${API_URL}api/address/${address}/utxo`;
      const response = await axios.get(url);
      
      // 检查是否有太多 UTXO 的错误
      if (typeof response.data === 'string' && 
          response.data.includes('Too many unspent transaction outputs')) {
        throw new Error('Too many UTXOs');
      }

      const utxos = confirmed 
        ? response.data
            .filter(utxo => utxo.status.confirmed)
            .map(utxo => new UTXO(utxo.txid, utxo.vout, utxo.value, true))
        : response.data
            .map(utxo => new UTXO(utxo.txid, utxo.vout, utxo.value, utxo.status.confirmed));
      
      return utxos;
    } catch (error) {
      lastError = error;
      console.error(`获取 UTXOs 失败 (尝试 ${i + 1}/${retries}):`, error.message);
      
      if (error.response && error.response.data && 
          typeof error.response.data === 'string' && 
          error.response.data.includes('Too many unspent transaction outputs')) {
        throw new Error('Too many UTXOs');
      }
      
      if (i < retries - 1) {
        await sleep(1000); // 等待 1 秒后重试
      }
    }
  }

  throw lastError;
}

/**
 * 过滤和排序 UTXOs
 * @param {UTXO[]} utxos - UTXO 数组
 * @param {number} minValue - 最小值
 * @param {number} protect - 保护值
 * @returns {UTXO[]} 过滤后的 UTXO 数组
 */
function filterAndSortUTXOs(utxos, minValue, protect = 1000) {
  if (!Array.isArray(utxos)) {
    console.warn('Invalid UTXOs input:', utxos);
    return [];
  }

  try {
    let filteredUTXOs = utxos.filter(utxo => {
      if (!utxo || typeof utxo.value !== 'number') {
        return false;
      }
      return utxo.value > Math.max(minValue, protect);
    });

    // 按值降序排序
    filteredUTXOs.sort((a, b) => b.value - a.value);
    return filteredUTXOs;
  } catch (error) {
    console.error('Error in filterAndSortUTXOs:', error);
    return [];
  }
}

/**
 * 广播交易
 * @param {string} txHex - 交易十六进制字符串
 * @param {number} retries - 重试次数
 * @returns {Promise<string>} 交易 ID
 */
async function broadcastTransaction(txHex, retries = 3) {
  let lastError;
  
  for (let i = 0; i < retries; i++) {
    try {
      const url = `${API_URL}api/tx`;
      const response = await axios.post(url, txHex, {
        headers: { 'Content-Type': 'text/plain' }
      });
      
      console.log('广播成功，交易 ID:', response.data);
      return response.data;
    } catch (error) {
      lastError = error;
      console.error(`广播失败 (尝试 ${i + 1}/${retries}):`, 
        error.response ? error.response.data : error.message);
      
      if (i < retries - 1) {
        await sleep(1000);
      }
    }
  }
  
  throw lastError;
}

/**
 * 获取推荐手续费率
 * @returns {Promise<Object>} 手续费率对象
 */
async function getFeeRates() {
  try {
    const url = `${API_URL}api/v1/fees/recommended`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('获取手续费率失败:', error.message);
    // 返回默认值
    return {
      fastestFee: 10,
      halfHourFee: 8,
      hourFee: 6,
      economyFee: 4,
      minimumFee: 1
    };
  }
}

/**
 * 获取交易详情
 * @param {string} txid - 交易 ID
 * @returns {Promise<Object>} 交易详情
 */
async function getTransaction(txid) {
  try {
    const url = `${API_URL}api/tx/${txid}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    throw new Error(`获取交易详情失败: ${error.message}`);
  }
}

/**
 * 睡眠函数
 * @param {number} ms - 毫秒数
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

module.exports = {
  UTXO,
  fetchUTXOs,
  filterAndSortUTXOs,
  broadcastTransaction,
  getFeeRates,
  getTransaction,
  sleep
};

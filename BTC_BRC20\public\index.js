/**
 * BTC BRC20 批量铸造工具 - 前端主文件
 * 重构版本 - 模块化结构，保持原有功能不变
 */

// ==================== 常量定义 ====================
const CONFIG = {
    // 网络配置
    IS_TESTNET: window.env.TESTNET === "true",
    NETWORK_SUFFIX: window.env.TESTNET === "true" ? "_TEST" : "_Main",

    // 加密密钥
    STORAGE_ENCRYPTION_KEY: 'ybot_1999eth_x_com_20240509',
    API_ENCRYPTION_KEY: 'ybot_1999eth_x_com_202405101298',

    // 请求配置
    MAX_REQUESTS_PER_SECOND: 3,
    REQUEST_INTERVAL: 1000 / 3,

    // 默认值
    DEFAULT_INSCRIPTION_SIZE: 294,
    DEFAULT_PROTECT_VALUE: 10000,
    DEFAULT_ADDRESS_TYPE: "P2TR"
};

// ==================== 全局状态管理 ====================
const AppState = {
    // 价格和数据
    btcPrices: 0,
    uprunes: 0,
    runename: null,
    feeData: null,

    // 界面状态
    playsound: true,
    mintchange: false,
    Batchaccele: false,
    loading: false,
    isLoading: true,
    iscountLoading: false,
    iswalletLoading: false,
    isUpdating: false,
    isCustomTextMode: false,

    // 交易相关
    utxoList: [],
    currentBlockHeight: null,
    uniqueRunes: new Set(['all']),
    lastCheckMintableTime: 0,

    // 请求队列
    queue: [],
    processing: false,

    // 界面选择
    currentSelectedId: "hot",

    // 时间同步
    timeOffset: null,

    // 图表相关
    prevMemsum: '',
    prevGassize: '',
    myChart: null,
    dataUpdateInterval: null,

    // 其他状态
    tickRefreshInterval: null,

    // 持有人列表缓存
    holdersCache: new Map(), // 缓存结构: { tick: { data: APIResponse, timestamp: number, blockHeight: number } }
    maxHoldersCacheSize: 20 // 最多缓存20个tick的数据
};

// 兼容性：保留原有全局变量
const istestnet = CONFIG.NETWORK_SUFFIX;
const STORAGE_ENCRYPTION_KEY = CONFIG.STORAGE_ENCRYPTION_KEY;
const API_ENCRYPTION_KEY = CONFIG.API_ENCRYPTION_KEY;
const CONFIRMED_TRANSACTIONS_KEY = 'ConfirmedTransactions' + CONFIG.NETWORK_SUFFIX;

let btcPrices = AppState.btcPrices;
let uprunes = AppState.uprunes;
let runename = AppState.runename;
let feeData = AppState.feeData;
let playsound = AppState.playsound;
let mintchange = AppState.mintchange;
let Batchaccele = AppState.Batchaccele;
let utxoList = AppState.utxoList;
let currentBlockHeight = AppState.currentBlockHeight;
let uniqueRunes = AppState.uniqueRunes;
let loading = AppState.loading;
let lastCheckMintableTime = AppState.lastCheckMintableTime;
const queue = AppState.queue;
let processing = AppState.processing;
const maxRequestsPerSecond = CONFIG.MAX_REQUESTS_PER_SECOND;
const interval = CONFIG.REQUEST_INTERVAL;
let currentSelectedId = AppState.currentSelectedId;
let timeOffset = AppState.timeOffset;
let isLoading = AppState.isLoading;
let iscountLoading = AppState.iscountLoading;
let iswalletLoading = AppState.iswalletLoading;
let isUpdating = AppState.isUpdating;
let tickRefreshInterval = AppState.tickRefreshInterval;
let isCustomTextMode = AppState.isCustomTextMode;

// ==================== 工具函数模块 ====================
const Utils = {
    // DOM缓存
    _domCache: new Map(),

    // 获取缓存的DOM元素
    $(selector) {
        if (!this._domCache.has(selector)) {
            this._domCache.set(selector, $(selector));
        }
        return this._domCache.get(selector);
    },

    // 基础工具函数
    sleep: ms => new Promise(resolve => setTimeout(resolve, ms)),

    getAPIUrl() {
        if (CONFIG.IS_TESTNET) {
            return "https://mempool.space/testnet/";
        } else {
            const urls = ["https://mempool.space/"];
            const randomIndex = Math.floor(Math.random() * urls.length);
            return urls[randomIndex];
        }
    },

    encrypt: (data, key = CONFIG.STORAGE_ENCRYPTION_KEY) => CryptoJS.AES.encrypt(data, key).toString(),

    decrypt(data, key = CONFIG.STORAGE_ENCRYPTION_KEY) {
        if (!data) return "";
        return CryptoJS.AES.decrypt(data, key).toString(CryptoJS.enc.Utf8);
    },

    // 验证函数
    validateBitcoinAddress(address) {
        return /^(bc1|tb1)[a-zA-HJ-NP-Z0-9]{39,59}$/.test(address) &&
               [42, 62].includes(address.length) &&
               ['bc1', 'tb1'].includes(address.slice(0, 3));
    },

    validateWif(wif) {
        return wif && wif.length === 52 && ['5', 'L', 'K', 'c', '9'].some(p => wif.startsWith(p));
    },

    // UI工具函数
    showAlert(message) {
        $('#alertModalBody').html(message);
        $('#alertModal').modal('show');

        const handleKeypress = (e) => {
            if (e.which === 13) {
                $('#alertModal').modal('hide');
                $(document).off('keypress', handleKeypress);
            }
        };

        $(document).on('keypress', handleKeypress);
        $('#alertModal').one('hidden.bs.modal', () => $(document).off('keypress', handleKeypress));
    },

    // 格式化工具
    formatAddress(address) {
        return address && address.length > 42 ?
            `${address.slice(0, 20)}...${address.slice(-20)}` : address;
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// 为了兼容性，保留原函数名
const sleep = Utils.sleep;
const getAPIUrl = Utils.getAPIUrl.bind(Utils);
const en1999_20240924 = Utils.encrypt;
const de1999_20240924 = Utils.decrypt.bind(Utils);
const validateBitcoinAddress = Utils.validateBitcoinAddress.bind(Utils);
const showAlert = Utils.showAlert.bind(Utils);


// ==================== 时间同步模块 ====================
const TimeSync = {
    /**
     * 获取服务器时间并校正本地时间
     */
    async calibrateTime() {
        try {
            const response = await fetch('/api/v1/server_time');
            const data = await response.json();
            if (data.code === 0) {
                const serverTime = data.data;
                const localTime = Date.now();
                AppState.timeOffset = serverTime - localTime;
                timeOffset = AppState.timeOffset;
                localStorage.setItem('timeOffset', AppState.timeOffset);
            }
        } catch (error) {
            console.error('Error fetching server time:', error);
        }
    },

    /**
     * 获取校正后的时间戳
     * @returns {number} 校正后的时间戳
     */
    getSyncedTimestamp() {
        const localTime = Date.now();
        if (AppState.timeOffset !== null) {
            return localTime + AppState.timeOffset;
        } else {
            return localTime;
        }
    },

    /**
     * 初始化时间同步
     */
    initializeTimeSync() {
        const savedOffset = localStorage.getItem('timeOffset');
        if (savedOffset !== null) {
            AppState.timeOffset = parseInt(savedOffset, 10);
            timeOffset = AppState.timeOffset;
        } else {
            this.calibrateTime();
        }
    }
};

// 为了兼容性，保留原函数名
const calibrateTime = TimeSync.calibrateTime.bind(TimeSync);
const getSyncedTimestamp = TimeSync.getSyncedTimestamp.bind(TimeSync);
const initializeTimeSync = TimeSync.initializeTimeSync.bind(TimeSync);

// 初始化时间同步
TimeSync.initializeTimeSync();

// ==================== 持有人列表管理模块 ====================
const HoldersManager = {
    /**
     * 获取持有人数据
     * @param {string} tick - 代币标识符
     * @param {number} currentBlockHeight - 当前最新区块高度（来自getRuneInfo API）
     * @returns {Promise<Object|null>} - 持有人数据或null
     */
    async fetchHoldersData(tick, currentBlockHeight = 0) {
        if (!tick || typeof tick !== 'string') {
            return null;
        }

        const normalizedTick = tick.toLowerCase();

        // 检查缓存
        const cached = AppState.holdersCache.get(normalizedTick);
        let height = 0;

        if (cached) {
            height = cached.blockHeight || 0;

            // 新的缓存逻辑：基于区块高度判断
            if (currentBlockHeight > 0 && height === currentBlockHeight) {
                // 缓存的区块高度与当前最新高度相同，直接使用缓存
                console.log(`持有人缓存命中: tick=${tick}, height=${height}`);
                return cached.data;
            }
            // 如果 height < currentBlockHeight，即使缓存很新也需要更新
        }

        try {
            const requestData = {
                tick: normalizedTick,
                height: height,
                time: getSyncedTimestamp()
            };

            const encryptedData = en1999_20240924(JSON.stringify(requestData), API_ENCRYPTION_KEY);

            const response = await Promise.race([
                fetch('/api/v1/getHolder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ data: encryptedData }),
                }),
                new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), 10000))
            ]);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            // 验证API响应的tick是否与请求的tick一致
            const responseTick = (result.result && result.result.tick || '').toLowerCase();
            const requestTick = normalizedTick;

            if (responseTick !== requestTick) {
                console.log(`getHolder API响应的tick(${responseTick})与请求的tick(${requestTick})不匹配，忽略该响应`);
                // 如果有缓存数据，返回缓存；否则返回null
                if (cached) {
                    return cached.data;
                }
                return null;
            }

            // 检查是否有新数据
            if (result.result && result.result.unused_txes && result.result.unused_txes.length > 0) {
                // 有新数据，更新缓存
                this.updateCache(normalizedTick, result, result.result.block_height || 0);
                return result;
            } else if (cached && result.result && result.result.block_height === cached.blockHeight) {
                // 没有新数据，使用缓存
                return cached.data;
            } else {
                // 返回空数据结构
                return {
                    error: null,
                    result: {
                        unused_txes: [],
                        block_height: result.result?.block_height || 0,
                        total: 0,
                        decimals: 18,
                        limit_per_mint: "1000"
                    }
                };
            }

        } catch (error) {
            console.error('获取持有人数据失败:', error);

            // 如果有缓存数据，返回缓存
            if (cached) {
                return cached.data;
            }

            return null;
        }
    },

    /**
     * 更新缓存
     * @param {string} tick - 代币标识符
     * @param {Object} data - API响应数据
     * @param {number} blockHeight - 区块高度
     */
    updateCache(tick, data, blockHeight) {
        // 如果缓存超过限制，删除最旧的条目
        if (AppState.holdersCache.size >= AppState.maxHoldersCacheSize) {
            const oldestKey = AppState.holdersCache.keys().next().value;
            AppState.holdersCache.delete(oldestKey);
        }

        AppState.holdersCache.set(tick, {
            data: data,
            timestamp: Date.now(),
            blockHeight: blockHeight
        });
    },

    /**
     * 渲染持有人列表
     * @param {Object} holdersData - 持有人数据
     */
    renderHoldersList(holdersData) {
        const tbody = $('#holdersTableBody');

        if (!holdersData || !holdersData.result || !holdersData.result.unused_txes) {
            this.showEmptyState();
            return;
        }

        const holders = holdersData.result.unused_txes;
        const total = holdersData.result.total || 0;
        const limitPerMint = parseFloat(holdersData.result.limit_per_mint || "1000");

        if (holders.length === 0) {
            this.showEmptyState();
            return;
        }

        let html = '';
        holders.forEach((holder, index) => {
            const rank = index + 1;
            const wallet = holder.wallet || '';
            const overallBalance = parseFloat(holder.overall_balance || "0");
            const availableBalance = parseFloat(holder.available_balance || "0");

            // 计算张数（余额除以每张限制）
            const totalSheets = limitPerMint > 0 ? Math.floor(overallBalance / limitPerMint) : 0;

            // 计算可挂单数量：overall_balance - available_balance
            const unavailableBalance = overallBalance - availableBalance;
            const unavailableSheets = limitPerMint > 0 ? Math.floor(unavailableBalance / limitPerMint) : 0;

            // 计算占比（基于总持有人数的估算）
            const percentage = total > 0 ? ((totalSheets / (total * 100)) * 100).toFixed(2) : "0.00";

            // 截断地址显示
            const displayAddress = this.truncateAddress(wallet);

            // 构建mempool链接
            const mempoolUrl = this.getMempoolUrl(wallet);

            html += `
                <tr>
                    <td>${rank}</td>
                    <td><a href="${mempoolUrl}" target="_blank" class="wallet-link" data-full-address="${wallet}">${displayAddress}</a></td>
                    <td class="text-end">${totalSheets.toLocaleString()} 张</td>
                    <td class="text-end">${unavailableSheets > 0 ? unavailableSheets.toLocaleString() + ' 张' : ''}</td>
                    <td class="text-end">${percentage}%</td>
                </tr>
            `;
        });

        tbody.html(html);
    },

    /**
     * 显示空状态
     */
    showEmptyState() {
        const tbody = $('#holdersTableBody');
        tbody.html(`
            <tr>
                <td colspan="5" class="text-center text-muted">
                    暂无持有人数据
                </td>
            </tr>
        `);
    },

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const tbody = $('#holdersTableBody');
        tbody.html(`
            <tr id="holdersLoadingRow">
                <td colspan="5" class="text-center">
                    <span class="spinner-border spinner-border-sm text-primary" role="status"></span>
                    <span class="ms-2">加载持有人数据中...</span>
                </td>
            </tr>
        `);
    },

    /**
     * 截断地址显示
     * @param {string} address - 完整地址
     * @returns {string} - 截断后的地址
     */
    truncateAddress(address) {
        if (!address || address.length <= 12) {
            return address;
        }
        return address.substring(0, 6) + '...' + address.substring(address.length - 4);
    },

    /**
     * 获取mempool网站链接
     * @param {string} address - 钱包地址
     * @returns {string} - mempool链接
     */
    getMempoolUrl(address) {
        const baseUrl = CONFIG.IS_TESTNET ? 'https://mempool.space/testnet' : 'https://mempool.space';
        return `${baseUrl}/address/${address}`;
    },

    /**
     * 更新持有人列表（主入口函数）
     * @param {string} tick - 代币标识符
     * @param {boolean} showLoading - 是否显示加载状态
     * @param {number} currentBlockHeight - 当前最新区块高度（来自getRuneInfo API）
     */
    async updateHoldersList(tick, showLoading = true, currentBlockHeight = 0) {
        if (!tick || tick === '自定义文本') {
            this.showEmptyState();
            return;
        }

        // 对于不同的tick，总是先显示加载状态
        if (showLoading) {
            this.showLoadingState();
        }

        try {
            const holdersData = await this.fetchHoldersData(tick, currentBlockHeight);
            this.renderHoldersList(holdersData);
        } catch (error) {
            console.error('更新持有人列表失败:', error);
            this.showEmptyState();
        }
    }
};

// ==================== UI管理模块 ====================
const UIManager = {
    /**
     * 初始化汉堡菜单功能
     */
    initMobileMenu() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuToggle && mobileMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                const isActive = mobileMenuToggle.classList.contains('active');

                if (isActive) {
                    // 关闭菜单
                    mobileMenuToggle.classList.remove('active');
                    mobileMenu.style.display = 'none';
                } else {
                    // 打开菜单
                    mobileMenuToggle.classList.add('active');
                    mobileMenu.style.display = 'block';
                }
            });

            // 点击菜单外部关闭菜单
            document.addEventListener('click', (e) => {
                if (!mobileMenuToggle.contains(e.target) && !mobileMenu.contains(e.target)) {
                    mobileMenuToggle.classList.remove('active');
                    mobileMenu.style.display = 'none';
                }
            });

            // 点击菜单链接后关闭菜单
            const menuLinks = mobileMenu.querySelectorAll('.mobile-menu-link');
            menuLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenuToggle.classList.remove('active');
                    mobileMenu.style.display = 'none';
                });
            });
        }
    },

    /**
     * 初始化所有UI功能
     */
    init() {
        this.initMobileMenu();
    }
};

// ==================== 存储管理模块 ====================
const StorageManager = {
    // 存储键名缓存
    _keyCache: new Map(),

    getStorageKey(key) {
        if (!this._keyCache.has(key)) {
            this._keyCache.set(key, key + CONFIG.NETWORK_SUFFIX);
        }
        return this._keyCache.get(key);
    },

    // 批量获取存储值
    getStorageValues(keys) {
        const result = {};
        keys.forEach(key => {
            const storageKey = this.getStorageKey(key);
            const value = localStorage.getItem(storageKey);
            result[key] = value ? Utils.decrypt(value) : null;
        });
        return result;
    },

    // 批量设置存储值
    setStorageValues(values) {
        Object.entries(values).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                localStorage.setItem(this.getStorageKey(key), Utils.encrypt(value.toString()));
            }
        });
    },

    loadParams() {
        // 批量获取存储值
        const stored = this.getStorageValues([
            'wif', 'inscriptionSize', 'protect', 'tick', 'amount', 'orditext',
            'receiveAddress', 'singleReceiveAddress', 'feeRate', 'addressType',
            'walletAddress', 'walletBalance', 'btcPrices', 'Lowfee',
            'addrunesmint', 'mintHexData', 'runes', 'advancedSettings'
        ]);

        // 设置默认值
        const params = {
            inscriptionSize: stored.inscriptionSize || CONFIG.DEFAULT_INSCRIPTION_SIZE.toString(),
            protect: stored.protect || CONFIG.DEFAULT_PROTECT_VALUE,
            addressType: stored.addressType || CONFIG.DEFAULT_ADDRESS_TYPE,
            tick: stored.tick || "ordi",
            amount: stored.amount || "1000",
            orditext: stored.orditext || '{"p":"brc-20","op":"mint","tick":"ordi","amt":"1000"}',
            feeRate: stored.feeRate || '',
            maxFee: stored.feeRate || '',
            Lowfee: stored.Lowfee === 'true' || stored.Lowfee === true,
            addrunesmint: stored.addrunesmint === 'true' || stored.addrunesmint === true,
            mintHexData: stored.mintHexData || '',
            runes: stored.runes || 'UNCOMMON•GOODS',
            ...stored  // 恢复这行以加载其他参数
        };
        
        // 重新确保布尔值的正确处理（覆盖 ...stored 可能设置的错误值）
        params.Lowfee = stored.Lowfee === 'true' || stored.Lowfee === true;
        params.addrunesmint = stored.addrunesmint === 'true' || stored.addrunesmint === true;

        // 更新状态
        AppState.btcPrices = parseInt(params.btcPrices) || 0;
        btcPrices = AppState.btcPrices;

        // 批量设置界面值
        const uiElements = {
            '#tick': params.tick,
            '#amount': params.amount,
            '#orditext': params.orditext,
            '#walletAddress': params.walletAddress,
            '#walletBalance': params.walletBalance,
            '#wif': params.wif || '',
            '#inscriptionSize': params.inscriptionSize || 294,
            '#protect': params.protect || 10000,
            '#receiveAddress': params.receiveAddress || '',
            '#singleReceiveAddress': params.singleReceiveAddress || '',
            '#feeRate': params.feeRate || '',
            '#maxFee': params.maxFee,
            '#addressType': params.addressType,
            '#mintHexData': params.mintHexData,
            '#runes': params.runes
        };

        Object.entries(uiElements).forEach(([selector, value]) => {
            const element = $(selector);
            if (element.is('input, select, textarea')) {
                element.val(value);
            } else {
                element.text(value);
            }
        });

        $('#Lowfee').prop('checked', params.Lowfee);
        $('#addrunesmint').prop('checked', params.addrunesmint);

        // 设置高级设置开关状态
        const advancedSettingsEnabled = stored.advancedSettings === 'true' || stored.advancedSettings === true;
        $('#advancedSettings').prop('checked', advancedSettingsEnabled);

        // 根据高级设置开关状态显示或隐藏高级设置区域
        if (advancedSettingsEnabled) {
            $('#inscriptionSizeContainer').show();
        } else {
            $('#inscriptionSizeContainer').hide();
        }

        // 强制确认状态设置正确
        setTimeout(() => {
            const actualChecked = $('#addrunesmint').prop('checked');
            if (actualChecked !== params.addrunesmint) {
                $('#addrunesmint').prop('checked', params.addrunesmint);
            }
        }, 100);
        
        // 调试函数：清除符文相关存储
        window.clearRunesStorage = () => {
            localStorage.removeItem('addrunesmint' + CONFIG.NETWORK_SUFFIX);
            localStorage.removeItem('mintHexData' + CONFIG.NETWORK_SUFFIX);
            localStorage.removeItem('runes' + CONFIG.NETWORK_SUFFIX);
        };
        
        // 根据符文铸造开关状态显示或隐藏符文容器
        if (params.addrunesmint) {
            $('#runesMintContainer').show();
            if (params.runes) {
                // 触发符文信息加载，使用更长的延迟确保函数已定义
                setTimeout(() => {
                    if (typeof fetchRuneInfo2 === 'function') {
                        fetchRuneInfo2(params.runes);
                    } else {
                        console.log('fetchRuneInfo2 函数尚未定义，稍后重试');
                        // 如果函数还未定义，再次延迟尝试
                        setTimeout(() => {
                            if (typeof fetchRuneInfo2 === 'function') {
                                fetchRuneInfo2(params.runes);
                            }
                        }, 1000);
                    }
                }, 500);
            }
        } else {
            // 确保符文容器是隐藏的
            $('#runesMintContainer').hide();
        }

        if (params.tick) {
            $('#runeInfoSpinner').show();
            fetchBRC20Info(params.tick, false); // 初始加载视为手动查询
        }

        if (params.receiveAddress && window.updateAddressCount) {
            window.updateAddressCount();
        }

        AppState.isLoading = false;
        isLoading = AppState.isLoading;
    },

    async saveParams(inputElementOrParamName) {
        if (!inputElementOrParamName) return;

        let id;
        if (typeof inputElementOrParamName === 'string') {
            // 传递的是参数名
            id = inputElementOrParamName;
        } else {
            // 传递的是输入元素
            id = $(inputElementOrParamName).attr('id');
        }

        console.log("保存", id);

        const saveActions = {
            'tick': () => this.setStorageValues({ tick: $('#tick').val() }),
            'amount': () => this.setStorageValues({ amount: $('#amount').val().trim() }),
            'orditext': () => this.setStorageValues({ orditext: $('#orditext').val().trim() }),
            'wif': () => this.setStorageValues({ wif: $('#wif').val().trim() }),
            'inscriptionSize': () => {
                limitInscriptionSize();
                this.setStorageValues({ inscriptionSize: $('#inscriptionSize').val().trim() });
            },
            'protect': () => this.setStorageValues({ protect: $('#protect').val() }),
            'receiveAddress': () => this.setStorageValues({ receiveAddress: $('#receiveAddress').val().trim() }),
            'singleReceiveAddress': () => this.setStorageValues({ singleReceiveAddress: $('#singleReceiveAddress').val().trim() }),
            'feeRate': () => this.setStorageValues({ feeRate: $('#feeRate').val().trim() }),
            'addressType': () => this.setStorageValues({ addressType: $('#addressType').val() }),
            'Lowfee': () => this.setStorageValues({ Lowfee: $('#Lowfee').prop('checked') }),
            'addrunesmint': () => this.setStorageValues({ addrunesmint: $('#addrunesmint').prop('checked') }),
            'mintHexData': () => this.setStorageValues({ mintHexData: $('#mintHexData').val().trim() }),
            'runes': () => this.setStorageValues({ runes: $('#runes').val().trim() }),
            'advancedSettings': () => this.setStorageValues({ advancedSettings: $('#advancedSettings').prop('checked') })
        };

        if (saveActions[id]) {
            saveActions[id]();
        }
    }
};

// 为了兼容性，保留原函数名
const loadParams = StorageManager.loadParams.bind(StorageManager);
const saveParams = StorageManager.saveParams.bind(StorageManager);

// ==================== 钱包管理模块 ====================
const WalletManager = {
    collectActiveTxIds() {
        const activeTxIds = [];
        $('.utxo-group').each(function() {
            const txHashElement = $(this).find('.tx-hash');
            const txid = txHashElement.length ? txHashElement.text().split(': ')[1] : null;
            const oldHash = $(this).find('.accelerate-btn').data('old-hash');
            const startTxid = $(this).find('.accelerate-btn').data('txid');
            const sentcount = $(this).find('.accelerate-btn').data('sent-count');
            if (txid && !activeTxIds.some(tx => tx.txid === txid)) {
                activeTxIds.push({ txid, oldHash, startTxid, sentcount });
            }
        });
        return activeTxIds;
    },

    validateWif(wif) {
        return wif && wif.length === 52 && ['5', 'L', 'K', 'c', '9'].some(p => wif.startsWith(p));
    }
};


$('#tick, #amount').on('input', function() {
  // 如果用户手动修改了tick输入框，且内容不是"自定义文本"，则退出自定义模式
  if ($(this).attr('id') === 'tick' && $(this).val() !== '自定义文本') {
    isCustomTextMode = false;
    // 退出自定义模式时显示amount输入框
    $('#amount').closest('.mb-2').show();
  }
  
  // 如果不是自定义模式且不是正在更新中，则正常更新orditext
  if (!isCustomTextMode && !isUpdating) {
    updateOrditext();
  }
});

// 文本清理函数
function cleanOrditextContent() {
  const $orditext = $('#orditext');
  let content = $orditext.val();

  // 去除前后空格
  content = content.trim();

  // 去除空白行（包括只包含空格、制表符的行）
  const lines = content.split('\n');
  const cleanedLines = lines.filter(line => line.trim() !== '');
  content = cleanedLines.join('\n');

  // 更新文本框内容
  if (content !== $orditext.val()) {
    $orditext.val(content);
  }

  return content;
}

// 重复内容检测函数
function checkDuplicateContent() {
  const content = $('#orditext').val().trim();
  if (!content) return false;

  const lines = content.split('\n').map(line => line.trim()).filter(line => line !== '');

  // 检查是否有重复行
  const lineSet = new Set();
  for (const line of lines) {
    if (lineSet.has(line)) {
      return true; // 发现重复行
    }
    lineSet.add(line);
  }

  return false;
}

// 更新警告提示文案
function updateWarningTip() {
  const $safetip = $('.safetip');
  if (isCustomTextMode && checkDuplicateContent()) {
    $safetip.text('(检测到重复内容，只需要输入一行并在下方设置铸造数量)');
  } else {
    $safetip.text('(请确认铸造的最终文本内容)');
  }
}

// 监听 orditext 的输入事件
$('#orditext').on('change', function() {
  if (!isUpdating) {
    if (isCustomTextMode) {
      // 在自定义文本模式下，检查内容是否包含"brc-20"（不区分大小写）
      const content = $(this).val().toLowerCase();
      if (!content.includes('brc-20')) {
        // 如果不包含"brc-20"，设置tick为"自定义文本"
        $('#tick').val('自定义文本');
      }
      // 检查重复内容并更新警告提示
      updateWarningTip();
      // 如果包含"brc-20"，保持tick的当前值，按照以往逻辑处理
    } else {
      // 在BRC20模式下，执行正常的字段更新逻辑
      updateFieldsFromOrditext();
    }
  }
});

// 监听 orditext 的失去焦点事件（自动清理）
$('#orditext').on('blur', function() {
  if (isCustomTextMode) {
    cleanOrditextContent();
    updateWarningTip();
    // 保存清理后的内容
    if (!isLoading) {
      saveParams('orditext');
    }
  }
});

// 监听 orditext 的输入事件（实时检测重复内容）
$('#orditext').on('input', function() {
  if (isCustomTextMode) {
    // 实时更新警告提示
    updateWarningTip();
  }
});

// 从 tick 和 amount 更新 orditext
function updateOrditext() {
    // 如果是自定义文本模式，不自动更新orditext
    if (isCustomTextMode) {
        return;
    }
    
    // 移除 trim()，保留空格
    const tick = $('#tick').val();
    const amount = $('#amount').val();
    const orditext = `{"p":"brc-20","op":"mint","tick":"${tick}","amt":"${amount}"}`;
    $('#orditext').val(orditext);
    if (!isLoading) {
        saveParams('orditext');
    }
}

// 从 orditext 更新 tick 和 amount
function updateFieldsFromOrditext() {
  // 如果当前是自定义文本模式，不执行自动解析和切换逻辑
  if (isCustomTextMode) {
    return;
  }
  
  // 设置标志，防止触发其他监听器
  isUpdating = true;

  let orditext = $('#orditext').val().trim();
  
  // 尝试清理不规范的JSON字符串
  try {
    // 找到第一个完整的JSON结构的结束位置
    let depth = 0;
    let endPos = -1;
    
    for (let i = 0; i < orditext.length; i++) {
      if (orditext[i] === '{') {
        depth++;
      } else if (orditext[i] === '}') {
        depth--;
        if (depth === 0) {
          endPos = i + 1;
          break;
        }
      }
    }
    
    // 如果找到了完整的JSON结构,则只保留这部分
    if (endPos > 0) {
      orditext = orditext.substring(0, endPos);
    }

    // 尝试解析清理后的JSON
    const parsed = JSON.parse(orditext);

    // 检查协议类型
    if (parsed.p === "brc-20") {
      // 标准BRC-20协议
      // 显示amount输入框及其标签
      $('label[for="amount"]').show();
      $('#amount').show();
      
      // 检查是否包含 tick 和 amt 字段
      if (parsed.tick !== undefined && parsed.amt !== undefined) {
        $('#tick').val(parsed.tick);
        $('#amount').val(parsed.amt);
        $('#tick').trigger('change');
        
        // 更新输入框中的值为清理后的JSON
        if (orditext !== $('#orditext').val().trim()) {
          $('#orditext').val(orditext);
        }
      }
    } else {
      // 非标准协议（如 block-20 等）
      // 在BRC20模式下检测到非标准格式，自动切换到自定义文本模式
      switchToCustomTextMode();

      // 更新输入框中的值为清理后的JSON
      if (orditext !== $('#orditext').val().trim()) {
        $('#orditext').val(orditext);
      }
    }
  } catch (e) {
    // 如果解析失败，切换到自定义模式
    switchToCustomTextMode();
    console.warn('orditext 不是有效的 JSON:', e);
  }

  // 取消标志
  isUpdating = false;
}

// 为了兼容性，保留原函数名
const collectActiveTxIds = WalletManager.collectActiveTxIds.bind(WalletManager);

// 更新钱包信息
async function updateWalletInfo(index = false) {
  if(iswalletLoading){
    return;
  }
  const wif = $('#wif').val();
  const addressType = $('#addressType').val();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间
  const Lowfee = $('#Lowfee').is(':checked');
  iswalletLoading = true;
  if (wif) {
    try {
      // 判断 WIF 私钥是否以 5、L、K、c 或 9 开头
      const validWif = wif.startsWith('5') || wif.startsWith('L') || wif.startsWith('K') || wif.startsWith('c') || wif.startsWith('9');
      
      if (!validWif || wif.length != 52) {
        showAlert('请填写正确的钱包wif私钥！');
        console.error('无效的WIF私钥:', wif);
        iswalletLoading = false;
        return;
      }

      const data = {
        wif,
        addressType,
        Lowfee
      };
      const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);
      $('#walletAddress').text("加载中...");
      $('#walletBalance').text("加载中...");
      const response = await Promise.race([
        fetch('/api/v1/wallet-info?t=' + timestamp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
      ]);

      if (!response.ok) {
        iswalletLoading = false;
        throw new Error('网络响应失败');
      }

      const jsondata = await response.json();
      if(jsondata.balance === -1){
        $('#walletBalance').text("0");
        $('#walletAddress').text(jsondata.address.length > 42 ? 
          `${jsondata.address.slice(0, 20)}...${jsondata.address.slice(-20)}` : 
          jsondata.address);
        $('#walletAddressLink').attr('title', jsondata.address);
        showAlert('该钱包的UTXO数量超过10000个，无法作为矿工钱包使用，请用新钱包作为矿工钱包！');
        iswalletLoading = false;
        return;
      }
      localStorage.setItem('walletAddress'+istestnet, jsondata.address);
      localStorage.setItem('walletBalance'+istestnet, jsondata.balance);
      $('#walletAddress').text(jsondata.address.length > 42 ? 
        `${jsondata.address.slice(0, 20)}...${jsondata.address.slice(-20)}` : 
        jsondata.address);
      $('#walletBalance').text(jsondata.balance);
      $('#walletAddressLink').attr('title', jsondata.address);

      if(jsondata.oguser == true){
        $('#og').show();
      }else{
        $('#og').hide();
      }
      iswalletLoading = false;
    } catch (error) {
      iswalletLoading = false;
      console.error('获取钱包信息失败:', error);
    }
  } else {
    if(index == false){
      localStorage.setItem('walletAddress'+istestnet, "");
      localStorage.setItem('walletBalance'+istestnet, "");
      $('#walletAddress').text('');
      $('#walletBalance').text('');
    }
  }
  iswalletLoading = false;
}


function updateMintableResult(jsondata) {
  const addressModeToggle = $('#addressModeToggle').prop('checked');
  let numshow = false;
  let resultHtml = `
    <table class="table">
      <tbody>
        <tr>
          <td>可用UTXO数量和金额</td>
          <td>${jsondata.utxocont}个 / 总计 ${jsondata.totalUtxoValue} BTC</td>
        </tr>
  `;

  if (jsondata.isMultiAddress) {
    if(addressModeToggle){
      numshow = true;
      // 多地址接收情况
      resultHtml += `
          <tr>
            <td>本次铸造所需UTXO数量</td>
            <td>${jsondata.utxoList.length}个</td>
          </tr>
          <tr>
            <td>每个地址铸造数量</td>
            <td id="everyAddressMintCount">${jsondata.countPerAddress}</td>
          </tr>
          <tr>
            <td>当前设置下最多可添加</td>
            <td><span id="maxAddressCount">${jsondata.maxAddressCount}</span>个接收地址，可铸造:<span id="maxTotalQuantity">${jsondata.countPerAddress * jsondata.maxAddressCount}</span>张</td>
          </tr>
      `;
      resultHtml += `
          </tbody>
        </table>
      `;
    }
  } else {
    // 单地址接收情况
    if(!addressModeToggle){
      numshow = true;
      resultHtml += `
          <tr>
            <td>单区块最大可Mint数量</td>
            <td id="maxMintCount">${jsondata.maxMintCount}</td>
          </tr>
      `;
      resultHtml += `
          </tbody>
        </table>
      `;
    }
  }

  if(numshow){
    $('#mintableResult').html(resultHtml);
    updateAddressWarning();
  }
}


$('#addressModeToggle').on('change', function() {
  if ($(this).prop('checked')) {
    $('#mintableResult').text("");
    $('#mintableResult').html("");
  }
});

// 修改查询可Mint数量的函数
async function checkMintableCount(checkConditions = false) {
  if(iscountLoading){
    return;
  }
  const wif = $('#wif').val();
  const feeRate = $('#feeRate').val();
  const count = $('#count').val();
  const orditext = $('#orditext').val();
  const inscriptionSizeInput = $('#inscriptionSize').val();
  const protect = $('#protect').val();
  const inscriptionSize = parseInt(inscriptionSizeInput) ? parseInt(inscriptionSizeInput) : 294;
  const receiveAddresses = sanitizeReceiveAddresses();
  const addressType = $('#addressType').val();
  const maxFee = $('#maxFee').val();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const addressModeToggle = $('#addressModeToggle').prop('checked');
  const addrunesmint = $('#addrunesmint').prop('checked');  //是否同时铸造符文
  const mintHexData = $('#mintHexData').val();
  const Lowfee = $('#Lowfee').prop('checked');  //是否使用超低费率

  const fetchTimeout = 60000; // 请求超时时间
  iscountLoading = true;

  if (!feeRate || !maxFee) {
    if (checkConditions) showAlert('请填写Gas费和最高Gas费');
    iscountLoading = false;
    return;
  }

  if (!maxFee) {
    maxFee = feeRate * 2;
  }

  if (receiveAddresses.length === 0) {
    if (checkConditions) showAlert('请至少输入一个有效的比特币收货地址');
    iscountLoading = false;
    return;
  }

  if(validateBitcoinAddress(receiveAddresses[0]) == false){
    if (checkConditions) showAlert('请输入一个有效比特币收货地址');
    iscountLoading = false;
    return;
  }

  if (!wif) {
    if (checkConditions) showAlert('请先导入作为付款的矿工钱包！');
    iscountLoading = false;
    return;
  }

  const checkMintableButton = $('#checkMintable');
  checkMintableButton.prop('disabled', true); // 将按钮设置为不可用状态
  checkMintableButton.addClass('disabled'); // 添加'disabled'类
  try {
    const data = {
      wif,
      feeRate,
      maxFee,
      orditext,
      inscriptionSize,
      protect,
      receiveAddresses,
      addressType,
      count,
      Lowfee
    };

    if(addrunesmint && mintHexData){
      data.mintHexData = mintHexData;
    }

    const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);

    const response = await Promise.race([
      fetch('/api/v1/mintable?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      iscountLoading = false;
      throw new Error('网络响应失败');
    }

    const jsondata = await response.json();

    updateMintableResult(jsondata);

    utxoList = jsondata.utxoList;
    const inscriptionCost = parseFloat(jsondata.inscriptionSize * parseInt(jsondata.count)).toFixed(8);
    const inscriptionCost_price = parseFloat(inscriptionCost * btcPrices).toFixed(3);

    const commitFee = parseFloat(jsondata.commitFee);
    const revealFee = parseFloat(jsondata.revealFee);
    const LastOneFee = parseFloat(jsondata.LastOneFee);

    const serviceFee = parseFloat(jsondata.serviceFee).toFixed(8);
    const serviceFee_price = parseFloat(serviceFee * btcPrices).toFixed(3);
    const serviceFee_og = parseFloat(jsondata.serviceFee / 0.8).toFixed(8);
    const serviceFee_og_price = parseFloat(serviceFee_og * btcPrices).toFixed(3);
    let mintcount = 0;

    let totalminerfee = 0;
    for (let index = 0; index < utxoList.length; index++) {
      const utxonum = parseInt(utxoList[index].num);
      mintcount += utxonum;
      if (utxonum === 1) {
        totalminerfee += commitFee + LastOneFee;
      } else if (utxonum > 1) {
        totalminerfee += commitFee + (utxonum - 1) * revealFee + LastOneFee;
      }
    }

    totalminerfee = parseFloat(totalminerfee).toFixed(8);  //矿工费用
    const totalminerfee_price = parseFloat(totalminerfee * btcPrices).toFixed(3);  //矿工费用 换算BTC价格

    const totalCost = parseFloat(parseFloat(totalminerfee) + parseFloat(inscriptionCost) + parseFloat(serviceFee)).toFixed(8);
    const totalCost_price = parseFloat(totalCost * btcPrices).toFixed(3);  //总共费用 换算BTC价格

    const unitCost = totalCost ? parseFloat(totalCost / parseInt(mintcount)).toFixed(8) : 0.00;   //单张价格
    const unitCost_price = unitCost ? parseFloat(parseFloat(unitCost) * btcPrices).toFixed(3) : 0;  //单张价格费用 换算BTC价格

    if(mintcount){
      $('#totalFee').html(totalminerfee_price ? "$" + totalminerfee_price + " <span class='gray-text btc-amount'>(" + totalminerfee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //矿工费
      $('#inscriptionCost').html(inscriptionCost_price ? "$" + inscriptionCost_price + " (<span class='gray-text btc-amount'>" + inscriptionCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //符文占用
      
      if(jsondata.oguser == true && serviceFee > 0){
        $('#ogFee').show();
        $('#ogserviceFee').show();
        $('#serviceFee').html(serviceFee_og_price ? "<span class='gray-text line-through'>$" + serviceFee_og_price + "</span> <span class='gray-text line-through btc-amount'>(" + serviceFee_og + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //服务费
        $('#ogserviceFee').html(serviceFee_price ? "<span class='gray-text'>$" + serviceFee_price + "</span> <span class='gray-text btc-amount'>(" + serviceFee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //OG特权服务费
      }else{
        $('#ogFee').hide();
        $('#ogserviceFee').hide();
        $('#serviceFee').html(serviceFee_price ? "<span class='gray-text'>$" + serviceFee_price + "</span> <span class='gray-text btc-amount'>(" + serviceFee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //服务费
      }
      $('#unitCost').html(unitCost_price ? "$" + unitCost_price + " <span class='gray-text btc-amount'>(" + unitCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //单张成本
      $('#totalCost').html(totalCost_price ? "$" + totalCost_price + " <span class='gray-text btc-amount'>(" + totalCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //总共费用
      $('#mintcount').html(mintcount ? mintcount + "张总共" : "总共");
    }else{
      $('#totalFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //矿工费
      $('#inscriptionCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");  //符文占用
      $('#serviceFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");  //服务费
      $('#unitCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //单张成本
      $('#totalCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //总共费用
      $('#mintcount').html("总共");
    } 

    lastCheckMintableTime = Date.now();

    checkMintableButton.prop('disabled', false); // 将按钮设置为可用状态
    checkMintableButton.removeClass('disabled'); // 移除'disabled'类
    iscountLoading = false;
  } catch (error) {
    checkMintableButton.prop('disabled', false); // 将按钮设置为可用状态
    checkMintableButton.removeClass('disabled'); // 移除'disabled'类
    console.error('查询可Mint数量失败:', error);
    iscountLoading = false;
  }
  if(mintchange = true){
    mintchange = false;
    const startMintingButton = $('#startMinting');
    startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
    startMintingButton.removeClass('disabled'); // 移除'disabled'类
  }
  iscountLoading = false;
}

// 修改启动铸造任务的函数
async function startMinting() {
  const wif = $('#wif').val();
  const inscriptionSizeInput = $('#inscriptionSize').val();
  const inscriptionSize = parseInt(inscriptionSizeInput) ? parseInt(inscriptionSizeInput)  : 294;
  const protect = $('#protect').val();
  const orditext = $('#orditext').val();
  const receiveAddresses = sanitizeReceiveAddresses();
  const feeRate = $('#feeRate').val();
  const count = parseInt($('#count').val());
  const addressType = $('#addressType').val();
  const maxFee = $('#maxFee').val();
  const mintableResult = $('#mintableResult').text();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const uniqueUtxoi = timestamp; // 生成唯一的utxoi
  const fetchTimeout = 600000; // 请求超时时间
  const startMintingButton = $('#startMinting');
  const tick = $('#tick').val();  //代币名称
  const address = $('#walletAddressLink').attr('title');
  const walletBalance = parseFloat($('#walletBalance').text());
  const RescueMode = $('#RescueMode').prop('checked');
  const addrunesmint = $('#addrunesmint').prop('checked');  //是否同时铸造符文
  const mintHexData = $('#mintHexData').val();  //符文id
  const runes = $('#runes').val();  //符文名称
  const limitPerMint = $('#limitPerMint').text();  //代币名称
  const Lowfee = $('#Lowfee').prop('checked');  //是否开启低费率

  //判断limitPerMint是否包含：未知铭文
  if(limitPerMint &&limitPerMint.includes('未知铭文') && !isCustomTextMode){
    showAlert('此铭文信息没有找到，请检查铭文名称是否正确！');
    return;
  }

  if (!feeRate || !maxFee) {
    showAlert('请填写Gas费和预留的最高Gas费！');
    $('#feeRate').focus();
    return;
  }

  if(!Lowfee && feeRate < 1){
    showAlert('低费率模式未开启，最低的gas设置不能低于1，否则可能影响上链！');
    $('#Lowfee').focus();
    return;
  }

  if (!wif) {
    showAlert('请先导入作为付款的矿工钱包！');
    return;
  }

  if (parseInt(count) <= 0) {
    showAlert('请填写提交要铸造的数量！');
    $('#count').focus();
    return;
  }

  if (orditext.length === 0) {
    showAlert('请正确输入需要铸造的铭文文本！');
    $('#orditext').focus();
    return;
  }

  if(walletBalance <= 0 && RescueMode == false){
    showAlert('您的矿工钱包余额为0，无法提交铸造！');
    return;
  }

  if (receiveAddresses.length === 0) {
    showAlert('请至少输入一个有效比特币收货地址！');
    if ($('#singleReceiveAddress').is(':hidden')) {
      $('#receiveAddress').focus();
    }else{
      $('#singleReceiveAddress').focus();
    }
    return;
  }
  
  //判断$('#memSpinner')是否显示
  if($('#runeInfoSpinner').is(':visible')){
    showAlert('正在加载铭文信息，请加载完成后再提交铸造！');
    return;
  }

  // 检查是否有有效的tick值
  const currentTick = $('#tick').val();
  if (!isCustomTextMode && (!currentTick || currentTick.trim() === '' || currentTick === '自定义文本')) {
    showAlert('请先选择要铸造的铭文！');
    return;
  }

  // 基于新的数据结构检查铸造状态
  if (!isCustomTextMode && !RescueMode) {
    // 检查进度百分比是否已达到100%
    const progressPercentage = $('.inscription-info-container .progress-percentage').text();
    if (progressPercentage && progressPercentage.includes('100%')) {
      showAlert('此铭文已经铭刻结束,无法继续铭刻！');
      return;
    }

    // 检查剩余供应量
    const progressNumbers = $('.inscription-info-container .progress-numbers').text();
    if (progressNumbers) {
      const match = progressNumbers.match(/(\d+(?:,\d+)*)\s*\/\s*(\d+(?:,\d+)*)/);
      if (match) {
        const minted = parseInt(match[1].replace(/,/g, ''));
        const total = parseInt(match[2].replace(/,/g, ''));
        if (minted >= total) {
          showAlert('此铭文已经铭刻结束,无法继续铭刻！');
          return;
        }
      }
    }
  }

  if(address.length > 0 && address == receiveAddresses[0]){
    showAlert('请不要使用矿工钱包地址作为收货地址！');
    return;
  }

  if(validateBitcoinAddress(receiveAddresses[0]) == false){
    showAlert('请输入一个有效的比特币收货地址!');
    return;
  }

  if(addrunesmint){
    if(!mintHexData){
      showAlert('您选择了同时铸造符文，需要输入符文名称或ID！');
      $('#mintHexData').focus();
      return;
    }
  }

  if (!maxFee) {
    maxFee = feeRate;
  }


  const currentTime = Date.now();
  if (currentTime - lastCheckMintableTime > 180000) {
    // 显示优雅的提示
    const toast = new bootstrap.Toast($('#refreshToast')[0], { delay: 2000 });
    toast.show();
    startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
    startMintingButton.addClass('disabled'); // 添加'disabled'类
    await checkMintableCount();
    startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
    startMintingButton.removeClass('disabled'); // 移除'disabled'类
    lastCheckMintableTime = currentTime;
  }

  //*
  if (!mintableResult && RescueMode == false && !isCustomTextMode) {
    showAlert('请先点击"查询当前账号可Mint的数量"这个按钮查询数量！');
    $('#checkMintable').focus();
    return;
  }
  
  var maxMintCount = $('#maxMintCount').text().trim() ? parseInt($('#maxMintCount').text()) : 0;
  if ((!maxMintCount || count > maxMintCount) && RescueMode == false && !isCustomTextMode) {
    if(!maxMintCount){
        showAlert(`提交失败，当前可能没有多余可用的UTXO了！\n\n如果是UTXO太小，请打开“高级设置”，修改降低保护聪大小！\n\n如果是因为预留gas太高，请尝试降低预留的最高gas！`);
    }else{
        showAlert(`铸造数量不能超过单区块最大可Mint数量(${maxMintCount})`);
    }
    return;
  }
  //*/

  // 获取模态框的值并显示模态框
  $('#confirmOrditext').text(orditext);
  $('#confirmMintCount').text(count);
  if(addrunesmint){
    $('#confirmRunestext_show').show();
    $('#confirmRunesMintCount_show').show();
    $('#confirmRunestext').text(runes);
    $('#confirmRunesMintCount').text(parseInt(count) + 1);
  }else{
    $('#confirmRunestext_show').hide();
    $('#confirmRunesMintCount_show').hide();
  }
  $('#confirmMintFee').text(feeRate + " sats/vB");
  $('#confirmTotalCost').html($('#totalCost').html());


  // 显示铸造数量和UTXO详情
  $('#mintDetails').empty();

  utxoList.forEach((utxo, index) => {
    $('#mintDetails').append(`
      <tr>
        <td>${index + 1}</td>
        <td>${utxo.value / 1e8} BTC</td>
        <td>${utxo.num}</td>
      </tr>
    `);
  });

  $('#showMintDetails').off('click').on('click', function(e) {
    e.preventDefault();
    $('#mintDetailsContainer').toggle();
  });


  $('#confirmMintModal').modal('show');

  // 在模态框中点击确认按钮时，继续铸造操作
  $('#confirmMintButton').off('click').on('click', async function () {
    $('#confirmMintModal').modal('hide');

    $('#waitingModal').modal('show');
    $('#waitingModal').one('shown.bs.modal', async function() {

      try {
        const data = {
          wif,
          inscriptionSize,
          protect,
          orditext: orditext,
          tick,
          receiveAddresses,
          feeRate,
          maxFee,
          count,
          activeUtxoi: uniqueUtxoi,
          addressType,
          RescueMode: RescueMode,
          Lowfee: Lowfee
        };
        if (addrunesmint && mintHexData) {
          data.runes = runes;
          data.mintHexData = mintHexData;
        }
        const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);

        // 记录API提交的时间
        const apiSubmitTime = Date.now();
        const response = await Promise.race([
          fetch('/api/v1/mint?t=' + timestamp, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ data: encryptedData }),
          }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
        ]);

        $('#waitingModal').modal('hide');

        if (!response.ok) {
          throw new Error('网络响应失败');
        }

        const jsondata = await response.json();
        const currentTimeAfterSubmit = Date.now();
        const elapsedTime = (currentTimeAfterSubmit - apiSubmitTime) / 1000; // 转换为秒

        if (jsondata.length === 0) {
          if (elapsedTime > 20) {
            showAlert('网络超时，已经提交的订单，系统会自动处理请稍后刷新网页查看！');
          } else {
            if(RescueMode == false){
              showAlert('铸造交易失败！可能是网络繁忙或者UTXO还没确认！');
            }else{
              showAlert('救援失败！可能救援钱包已经没有多余卡主的utxo了！');
            }
          }
          //fetchPendingOrders();
          startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
          startMintingButton.removeClass('disabled'); // 移除'disabled'类
        } else {
          // 使用unshift方法将新订单插入到数组的开头
          renderMintingList(jsondata);
          setTimeout(updateWalletInfo, 10000);
          setTimeout(checkMintableCount, 10000);
          const sound = new Audio('cha-ching.mp3');
          sound.volume = 0.5;
          sound.play();
          showAlert('铸造发送成功！');

          lastCheckMintableTime = 0;
          // 自动选择新铸造的铭文
          const newRune = jsondata[0].tick;
          $('#selectedRune').text(newRune);
          filterTransactions(newRune);

          let countDown = 10;
          startMintingButton.text(`正在广播中(${countDown})`);
          const countDownInterval = setInterval(() => {
            countDown--;
            startMintingButton.text(`正在广播中(${countDown})`);
            if (countDown <= 0) {
              clearInterval(countDownInterval);
              startMintingButton.text('开始铸造');
              startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
              startMintingButton.removeClass('disabled'); // 移除'disabled'类
            }
          }, 1000);
        }
      } catch (error) {
        $('#waitingModal').modal('hide');
        sleep(1000);
        showAlert('铸造失败！可能是网络繁忙或者UTXO还没确认！');
        console.error('启动铸造任务失败:', error);
        startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
        startMintingButton.removeClass('disabled'); // 移除'disabled'类
      }
    });
    $('#waitingModal').modal('hide');
  });
}



// 当页面加载时,填充下拉菜单
function populateRuneFilter(runes) {
  const runeFilter = $('#runeFilterDropdown').next('.dropdown-menu');
  runeFilter.empty(); // 首先清空下拉菜单

  runes.forEach(rune => {
    if (rune === 'all') {
      runeFilter.prepend('<li><a class="dropdown-item" href="#" data-value="all">显示全部订单</a></li>');
    } else {
      runeFilter.append(`<li><a class="dropdown-item" href="#" data-value="${rune}">${rune}</a></li>`);
    }
  });
}

function filterTransactions(selectedRune) {
  $('#mintingList, #confirmedList').find('.utxo-group, .transaction-item').each(function() {
    const runeName = $(this).find('p:contains("铭文名称")').find('.rune-text').text();
    
    if (selectedRune === 'all' || runeName === selectedRune) {
      $(this).show();
      $(this).find('.order-checkbox, .confirmed-order-checkbox').prop('disabled', false);
    } else {
      $(this).hide();
      $(this).find('.order-checkbox, .confirmed-order-checkbox').prop('checked', false).prop('disabled', true);
    }
  });

  updateSelectAllCheckbox();
}

// 成本计算函数
function calculateTotalCost(group) {
  let totalSize = group.commitVSize  + (group.sentCount -1) * group.revealVSize + group.LastOneSize;
  let totalCost = (totalSize * parseFloat(group.newfeeRate) + parseInt(group.inssize) * group.sentCount + group.serviceFee) / 1e8;
  totalCost = parseFloat(totalCost).toFixed(8);
  let totalCost_price = parseFloat(totalCost * btcPrices).toFixed(2);
  return { totalCost, totalCost_price };
}

// 渲染铸造交易列表
function renderMintingList(data) {
  const mintingList = $('#mintingList');
  const encryptedWif = en1999_20240924($('#wif').val().trim());
  const addressType = $('#addressType').val();
  //const mintFeeSplit = $('#mintFeeSplit').prop('checked');
  const noTransactionsMessage = $('#noTransactionsMessage');

  if (data.length === 0) {
    noTransactionsMessage.show();
    return;
  } else {
    noTransactionsMessage.hide();
  }

  // 提取铭文名称并添加到uniqueRunes Set中

  console.log(data);
  data.forEach(tx => {
    uniqueRunes.add(tx.tick);
  });

  // 使用uniqueRunes的值更新下拉菜单
  populateRuneFilter(Array.from(uniqueRunes));

  // 在更新DOM后,调用filterTransactions函数
  const selectedRune = $('#selectedRune').text();
  filterTransactions(selectedRune === '显示全部订单' ? 'all' : selectedRune);

  // 按订单创时间降序排
  const updatedData = data.map(tx => {
      if (!tx.hasOwnProperty('wif')) {
          tx.wif = encryptedWif;
      }
      if (!tx.hasOwnProperty('addressType')) {
          tx.addressType = addressType;
      }
      return tx;
    }).sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)); // 这里进行排序

  const groupedData = updatedData.reduce((acc, tx) => {
      const key = `utxoi-${tx.utxoi}`; // 确保使用唯一的 utxoi 作为键
      acc[key] = { ...tx };
      return acc;
  }, {});

  Object.values(groupedData).forEach((group) => {
      const inscriptionSize = parseInt($('#inscriptionSize').val()) ? parseInt($('#inscriptionSize').val()) : 294;
      //const mintHexData = $('#mintHexData').val();
      const groupElement = $('<div>').addClass('utxo-group');

      // 检查该组是否已存在DOM中
      let existingGroup = $(`#group-${group.utxoi}`);
      if (!existingGroup.length) {
          existingGroup = groupElement;
          existingGroup.attr('id', `group-${group.utxoi}`);
          mintingList.prepend(existingGroup); // 使用 prepend 插入到最前面
      } else {
          existingGroup = groupElement;
          existingGroup.attr('id', `group-${group.utxoi}`);
          mintingList.append(existingGroup); // 否则仍然append
      }

      if(!group.serviceFee){
        group.serviceFee = 0;
      }

      let { totalCost, totalCost_price } = calculateTotalCost(group);

      // 更新或设置组内容
      existingGroup.html(`
          <div class="checkbox-container">
              <input type="checkbox" class="form-check-input order-checkbox" id="orderCheckbox${group.utxoi}" data-group-id="${group.utxoi}">
              <label for="orderCheckbox${group.utxoi}" class="form-check-label"></label>
          </div>
          <p>铭文名称: <span class="rune-text"">${group.tick}</span></p>
          <p>钱包地址: <a href="#" class="wallet-address-link" data-receive-address="${group.receiveAddress}">${group.receiveAddress.length > 20 ? 
              `${group.receiveAddress.slice(0, 10)}...${group.receiveAddress.slice(-20)}` : 
              group.receiveAddress}</a></p>
          <p>交易数量: ${group.sentCount}/${group.totalCount}</p>
          <p class="order-cost">订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)</p>
          <p>创建时间: ${new Date(group.createdAt).toLocaleString()}</p>
          <p class="tx-hash">交易哈希: <a href="#" class="tx-hash-link">${group.txid}</a></p>
          <div class="transaction-actions">
              <button class="btn btn-primary btn-sm accelerate-btn"
                  data-wfi="${group.wif}"
                  data-addressType="${group.addressType}"
                  data-tick="${group.tick}"
                  data-orditext="${group.orditext}"
                  data-utxoi="${group.utxoi}"
                  data-runes="${group.runes}"
                  data-minthexdata="${group.mintHexData}"
                  data-inssize="${group.inssize ? group.inssize : inscriptionSize}"
                  data-old-hash="${group.old_hash ? group.old_hash : (group.sentCount > 1 && group.lastTx.txid ? group.lastTx.txid : group.txid)}"
                  data-txid="${group.lastTx.txid}"
                  data-vout="${group.sentCount == 1 ? 0 : group.lastTx.vout}"
                  data-value="${group.lastTx.value}"
                  data-feeRate="${group.feeRate}"
                  data-commitVSize="${group.commitVSize}"
                  data-revealVSize="${group.revealVSize}"
                  data-LastOneSize="${group.LastOneSize}"
                  data-receive-address="${group.receiveAddress}"
                  data-sentcount="${group.sentCount}"
                  data-lowfee="${group.Lowfee || false}"
                  data-total-count="${group.totalCount}">加速</button>
              <span class="order-rate"><span class="gray-text">订单费率: </span><span id="order-rate-${group.utxoi}">${group.newfeeRate}</span> sats/vB</span>
              <div class="transaction-actions">
                  <button class="btn btn-light btn-sm options-btn" id="options-btn-${group.utxoi}">
                     <span class="dots" style="color: gray;">...</span>
                  </button>
                  <button class="btn btn-danger btn-sm close-btn" id="delete-btn-${group.utxoi}" style="display: none;">删除</button>
              </div>
          </div>`);
  });

  // 更新localStorage中的交易记录
  updateLocalStorageWithTransactions(updatedData);
}

// 更新localStorage中的交易记录
function updateLocalStorageWithTransactions(transactions) {
  // 获取当前localStorage中的记录
  let storedTransactions = localStorage.getItem('TransactionsV1'+istestnet);
  storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
  // 将新交易记录添加到localStorage对象中
  transactions.forEach(tx => {
      storedTransactions[tx.utxoi] = { ...tx };
  });
  // 保存更新后的对象回localStorage
  localStorage.setItem('TransactionsV1' + istestnet, JSON.stringify(storedTransactions));
}


// 在 renderMintingList 函数外部绑定事件
$('#mintingList').on('click', '.accelerate-btn', async function() {
  const groupElement = $(this).closest('.utxo-group');
  const utxoi = groupElement.attr('id').split('-')[1]; // 从 id 中提取 utxoi
  const orderRateElement = $(`#order-rate-${utxoi}`);
  const group = {
    wfi: $(this).data('wfi'),
    addressType: $(this).data('addresstype'),
    tick: $(this).data('tick'),
    orditext: $(this).data('orditext'),
    inssize: $(this).data('inssize'),
    utxoi: $(this).data('utxoi'),
    receiveAddress: $(this).data('receive-address'),
    lastTx: {
      txid: $(this).data('txid'),
      vout: $(this).data('vout'),
      value: $(this).data('value')
    },
    feeRate: $(this).data('feerate'),
    sentcount: $(this).data('sentcount'),
    old_hash: $(this).data('old-hash'),
    runes: $(this).data('runes'),
    mintHexData: $(this).data('minthexdata'),
    Lowfee: $(this).data('lowfee')
  };

  $('#newFeeRateInput').val(''); // 清空输入框
  $('#accelerateFeeModal').modal('show');

  $('#confirmNewFeeRate').off('click').on('click', async function() {
    const newFeeRate = parseFloat($('#newFeeRateInput').val());
    const currentFeeRate = parseFloat(orderRateElement.text());
    const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
    /*
    const threshold = fastestFee * 1.8;

    if (newFeeRate > threshold && fastestFee > 0) {
      $('#newFeeRateInput').addClass('input-error');
      if ($('#newFeeRateInput').next('.error-message').length === 0) {
        $('#newFeeRateInput').after('<div class="error-message">输入的费率超过了当前最快费率的80%以上。</div>');
      }
      return;
    } else {
      $('#newFeeRateInput').removeClass('input-error');
      $('#newFeeRateInput').next('.error-message').remove();
    }
    */

    if (newFeeRate && !isNaN(newFeeRate)) {
      //*
      if (newFeeRate <= currentFeeRate) {
        $('#accelerateFeeModal').modal('hide');
        showAlert('输入要替换的费率必须大于当前订单费率。');
        return;
      }
      //*/
      const initialFee = group.feeRate;
      const sentCount = group.sentcount;
      const receiveAddress = group.receiveAddress;
      const txHashElement = groupElement.find('.tx-hash');
      const oldHash = txHashElement.text().split(': ')[1].trim();

      $('#accelerateFeeModal').modal('hide');
      console.log("sentCount",sentCount);
      const result = await accelerateTransaction(de1999_20240924(group.wfi), group.addressType, group.inssize, group.tick, de1999_20240924(group.orditext, API_ENCRYPTION_KEY), receiveAddress, group.lastTx.txid, group.lastTx.vout, group.lastTx.value, newFeeRate, initialFee, sentCount, oldHash, group.utxoi, group.runes, group.mintHexData, group.Lowfee);
      if (result) {
        groupElement.data('currentFee', newFeeRate);
        if (orderRateElement.parent().is('strong')) {
          orderRateElement.unwrap();
        }
        orderRateElement.text(newFeeRate);
        orderRateElement.wrap('<strong style="color: #099209;"></strong>');
        showAlert('订单费率已更新。');
      }
    } else {
      $('#accelerateFeeModal').modal('hide');
      setTimeout(function() {
        showAlert('请输入有效的目标费率进行替换。');
      }, 500);
    }
  });
});


$(document).on('click', '.wallet-address-link', function(e) {
  e.preventDefault();
  const address = $(this).data('receive-address');
  if (address) {
    window.open(`${getAPIUrl()}address/${address}`, '_blank');
  }
});

$(document).on('click', '.tx-hash-link', function(e) {
  e.preventDefault();
  const txHash = $(this).text();
  if (txHash) {
    window.open(`${getAPIUrl()}tx/${txHash}`, '_blank');
  }
});

let selectedOrders = [];

function collectSelectedOrders() {
  selectedOrders = [];
  const selectedRunes = new Set();
  const checkboxes = document.querySelectorAll('.order-checkbox:checked');
  checkboxes.forEach(function(checkbox) {
    const groupId = checkbox.getAttribute('data-group-id');
    const group = $(`#group-${groupId}`);
    const accelerateBtn = group.find('.accelerate-btn');
    const runeName = group.find('p:contains("铭文名称")').find('.rune-text').text();

    const txHashElement = group.find('.tx-hash');
    const oldHash = txHashElement.text().split(': ')[1].trim();

    selectedOrders.push({
      wfi: accelerateBtn.data('wfi'),
      addressType: accelerateBtn.data('addresstype'),
      tick: accelerateBtn.data('tick'),
      orditext: accelerateBtn.data('orditext'),
      utxoi: accelerateBtn.data('utxoi'),
      inssize: accelerateBtn.data('inssize'),
      txid: accelerateBtn.data('txid'),
      vout: accelerateBtn.data('vout'),
      value: accelerateBtn.data('value'),
      initialFee: accelerateBtn.data('feerate'),
      receiveAddress: accelerateBtn.data('receive-address'),
      sentCount: accelerateBtn.data('sentcount'),
      old_hash: oldHash,
      runes: accelerateBtn.data('runes'),
      mintHexData: accelerateBtn.data('minthexdata'),
      Lowfee: accelerateBtn.data('lowfee')
    });

    selectedRunes.add(runeName);
  });

  return selectedRunes;
}

$('#batchAccelerateButton').on('click', function() {
  const selectedRunes = collectSelectedOrders();
  if (selectedOrders.length > 0) {
    if (selectedRunes.size > 1) {
      $('#confirmBatchAccelerateModal').modal('show');
    } else {
      $('#newBatchFeeRateInput').val('');
      $('#batchAccelerateFeeModal').modal('show');
    }
  } else {
    showAlert('请至少选择一个订单进行加速。');
  }

});

$('#confirmNewBatchFeeRate').on('click', async function() {
  const newFeeRate = parseFloat($('#newBatchFeeRateInput').val());
  const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
  const threshold = fastestFee * 30000;

  if (newFeeRate > threshold && fastestFee > 0) {
    $('#newBatchFeeRateInput').addClass('input-error');
    if ($('#newBatchFeeRateInput').next('.error-message').length === 0) {
      $('#newBatchFeeRateInput').after('<div class="error-message">输入的费率超过了当前最快费率的80%以上。</div>');
    }
    return;
  } else {
    $('#newBatchFeeRateInput').removeClass('input-error');
    $('#newBatchFeeRateInput').next('.error-message').remove();
  }

  $('#batchAccelerateFeeModal').modal('hide');
  if (newFeeRate && !isNaN(newFeeRate)) {
    const data = selectedOrders.map(order => ({
      wif: de1999_20240924(order.wfi),
      inscriptionSize: order.inssize,
      tick: order.tick,
      orditext: de1999_20240924(order.orditext, API_ENCRYPTION_KEY),
      receiveAddress: order.receiveAddress,
      txid: order.txid,
      vout: order.vout,
      value: order.value,
      feeRate: newFeeRate,
      initialFee: order.initialFee,
      sentCount: order.sentCount,
      addressType: order.addressType,
      old_hash: order.old_hash,
      utxoi: order.utxoi,
      runes: order.runes,
      mintHexData: order.mintHexData,
      Lowfee: order.Lowfee
    }));

    playsound = false;
    setTimeout(function() {
      playsound = true;
    }, 5000);
    Batchaccele = true;

    const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);
    const timestamp = new Date().getTime();
    
    $('#waitingModal').modal('show');
    $('#waitingModal').one('shown.bs.modal', async function() {
      try {
        const response = await fetch('/api/v1/accelerate?t=' + timestamp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        });

        const jsondata = await response.json();

        let successCount = 0;
        let failCount = 0;

        jsondata.forEach(result => {
          if (result.success) {
            successCount++;
            updateTransactionUI(result.tx);
            updateLocalStorageWithNewTx(result.tx);
            
            const orderRateElement = $(`#order-rate-${result.tx.utxoi}`);
            if (orderRateElement.parent().is('strong')) {
              orderRateElement.unwrap();
            }
            orderRateElement.text(newFeeRate);
            orderRateElement.wrap('<strong style="color: #099209;"></strong>');
          } else {
            failCount++;
            const orderRateElement = $(`#order-rate-${result.tx.utxoi}`);
            if (orderRateElement.parent().is('strong')) {
              orderRateElement.unwrap();
            }
            orderRateElement.wrap('<strong style="color: #ff0000;"></strong>');
          }
        });

        if (failCount) {
          showAlert(`提交数量: ${data.length}, 提速成功: ${successCount}, 提速失败: ${failCount}`);
        } else {
          showAlert(`提交数量: ${data.length}, 提速成功: ${successCount}`);
        }
        Batchaccele = false;
        if (successCount) {
          const sound = new Audio('cha-ching.mp3');
          sound.volume = 0.5;
          sound.play();
        }
      } catch (error) {
        console.error('批量加速失败:', error);
        showAlert('批量加速失败,请稍后再试。');
      } finally {
        $('#waitingModal').modal('hide');
      }
    });
  } else {
    showAlert('请输入有效的加速费率。');
  }
});

async function processQueue() {
  if (queue.length === 0) {
    processing = false;
    return;
  }
  processing = true;
  const task = queue.shift();
  task();
  setTimeout(processQueue, interval);
}

async function enqueueTask(task) {
  queue.push(task);
  if (!processing) {
    processQueue();
  }
}


async function accelerateTransaction(wif, addressType, inscriptionSize, tick, orditext, receiveAddress, txid, vout, value, feeRate, initialFee, sentCount, old_hash, utxoi, runes = "", mintHexData = "", Lowfee = false) {
  try {
    let accelerateButton;
    if (!Batchaccele) {
      accelerateButton = $(`button[data-txid="${txid}"]`);
      accelerateButton.prop('disabled', true);
      accelerateButton.addClass('disabled');
    }
    const timestamp = new Date().getTime();
    const fetchTimeout = 600000;
    
    const data = {
      wif,
      inscriptionSize,
      tick,
      orditext,
      receiveAddress,
      feeRate,
      value,
      txid,
      vout,
      initialFee,
      sentCount,
      addressType,
      old_hash,
      utxoi,
      Lowfee
    };

    if(addrunesmint && mintHexData && mintHexData !== ""){
      data.runes = runes;
      data.mintHexData = mintHexData;
    }

    const encryptedData = en1999_20240924(JSON.stringify([data]), API_ENCRYPTION_KEY); // 注意这里将data包装成数组

    const response = await Promise.race([
      fetch('/api/v1/accelerate?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    const jsondata = await response.json();

    if (jsondata[0].error) {
      throw new Error(jsondata[0].error);
    }

    const result = jsondata[0];
    updateTransactionUI(result.tx);
    updateLocalStorageWithNewTx(result.tx);
    
    if (playsound) {
      playsound = false;
      const sound = new Audio('cha-ching.mp3');
      sound.volume = 0.5;
      sound.play();
      setTimeout(function() {
        playsound = true;
      }, 3000);
    }
    
    if (!Batchaccele) {
      showAlert('加速成功!');
      accelerateButton.prop('disabled', false);
      accelerateButton.removeClass('disabled');
    }
    return 1;
  } catch (error) {
    if (!Batchaccele) {
      console.error('Accelerate transaction failed:', error);
      const accelerateButton = $(`button[data-txid="${txid}"]`);
      accelerateButton.prop('disabled', false);
      accelerateButton.removeClass('disabled');

      if (error.message.includes("insufficient fee") || error.message.includes("mempool min fee not met")) {
        showAlert("加速失败：提供的费用不足以替换现有的交易，请增加费用后再试。");
      } else if (error.message.includes("Insufficient funds")) {
        showAlert("加速失败：该交易的UTXO剩余价值不足以支付加速后的交易费用。");
      } else if (error.message.includes("Fee exceeds maximum")) {
        showAlert("加速失败：总费用超过了节点允许的最大值。");
      } else if (error.message.includes("bad-txns-inputs-missingorspent")) {
        showAlert("加速失败：该交易不存在或可能已经被确认了。");
      } else if (error.message.includes("Insufficient UTXO value") || error.message.includes("Insufficient funds")) {
        showAlert("加速失败：该交易的UTXO剩余价值不足以支付加速后的交易费用。");
      } else {
        showAlert("加速失败：未知错误，请稍后再试。");
      }
    }
    return 0;
  }
}

function updateTransactionUI(data) {
  // 找到包含旧txid的元素
  const txElements = $('.tx-hash');
  txElements.each(function() {
    if ($(this).text().includes(data.old_hash)) { // 使用old_hash查找
      // 更新交易哈希
      $(this).html(`交易哈希: <a href="#" class="tx-hash-link">${data.txid}</a>`);
      // 获取加速按钮并更新data-old-hash属性
      const accelerateButton = $(this).closest('.utxo-group').find('.accelerate-btn');
      accelerateButton.attr('data-old-hash', data.old_hash);

      // 计算新的totalCost和totalCost_price
      const { totalCost, totalCost_price } = calculateTotalCost(data);
      // 更新订单成本
      const orderCostElement = $(this).closest('.utxo-group').find('.order-cost');
      if (orderCostElement.length > 0) {
        orderCostElement.text(""); // 强制刷新内容
        orderCostElement.html(`订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)`);
      }
    }
  });
}

function updateLocalStorageWithNewTx(data) {
  let transactions = localStorage.getItem('TransactionsV1'+istestnet);
  transactions = transactions ? JSON.parse(transactions) : {};
  let updated = false;

  // 检查 data 对象是否包含 utxoi
  if (!data.utxoi) {
    // 遍历 transactions，查找匹配 old_hash 的 utxoi
    for (let utxoi in transactions) {
      if (transactions[utxoi].txid === data.old_hash) {
        data.utxoi = utxoi;
        break;
      }
    }
  }

  // 遍历 transactions 的值, 更新各个 utxoi 的信息
  if (transactions[data.utxoi]) {
      transactions[data.utxoi] = {
          ...transactions[data.utxoi],
          txid: data.txid,
          newfeeRate: data.newfeeRate,
          lastTx: data.lastTx,
          old_hash: data.old_hash,
          serviceFee: data.serviceFee
      };
      updated = true;
  }

  if (updated) {
      localStorage.setItem('TransactionsV1' + istestnet, JSON.stringify(transactions));
  } else {
      console.log("No transaction updated in LocalStorage");
  }
}


function sanitizeReceiveAddresses() {
  const addressModeToggle = $('#addressModeToggle');
  if (addressModeToggle.prop('checked')) {
    const addresses = $('#receiveAddress').val();
    return addresses
      .split('\n')
      .map(address => address.trim())
      .filter(address => address !== '');
  } else {
    const singleAddress = $('#singleReceiveAddress').val().trim();
    return singleAddress ? [singleAddress] : [];
  }
}

$(document).on('click', '[id^="delete-btn-"]', function() {
  var id = $(this).attr('id').split('-')[2];
  removeTransaction(id, 1);
});

function removeTransaction(utxoi, del = 0) {
  // 使用utxoi来构造完整的id
  const transactionElement = $(`#group-${utxoi}`);
  if (transactionElement.length) {
    // 获取wif和addressType
    const accelerateBtn = transactionElement.find('.accelerate-btn');
    const wif = de1999_20240924(accelerateBtn.data('wfi'));
    const addressType = accelerateBtn.data('addresstype');

    // 将utxoi, wif和addressType添加到数组中
    const utxois = [{ utxoi, wif, addressType }];

    // 从DOM中移除这个交易元素
    transactionElement.remove();

    // 从localStorage中移除该记录
    let transactions = localStorage.getItem('TransactionsV1'+istestnet);
    transactions = transactions ? JSON.parse(transactions) : {};
    if (transactions[utxoi]) {
      delete transactions[utxoi]; // 删除对应utxoi的记录
      localStorage.setItem('TransactionsV1'+istestnet, JSON.stringify(transactions)); // 更新localStorage
    }

    // 发送删除状态更新
    updateorderstatus(utxois, del);
  }
}

function findTxidByStartTxid(activeTxIds, startTxid) {
  const stid = activeTxIds.find(tx => tx.startTxid === startTxid);
  const txid = activeTxIds.find(tx => tx.txid === startTxid);
  const matchingTx = stid ? stid : txid;
  return matchingTx ? matchingTx.txid : null;
}

async function TransactionStatus() {
  const activeTxIds = collectActiveTxIds();
  const utxois = [];
  if (activeTxIds.length == 0 || window.env.TESTNET === "true") {
    return;
  }
  const data = activeTxIds.map(tx => ({
    tx: tx.sentcount > 1 ? tx.startTxid : tx.txid
  }));

  const encryptedData = en1999_20240924(JSON.stringify({ data }), API_ENCRYPTION_KEY);
  const fetchTimeout = 10000;
  const timestamp = new Date().getTime();

  try {
    const response = await Promise.race([
      fetch('https://api_tx.ybot.io/api/tx?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }

    const result = await response.json();

    if (Array.isArray(result)) {
      let Block = false;
      result.forEach(transactionData => {
        if (transactionData && transactionData.confirmations > 0) {
          const txid = findTxidByStartTxid(activeTxIds, transactionData.tx);
          if (txid) {
            const { utxoi, wif, addressType } = findUtxoiByTxid(txid);
            utxois.push({ utxoi, wif, addressType });
            const transaction = removeFromLocalStorage(utxoi);
            if (transaction) {
              console.log("确认交易:", transaction);
              addConfirmedTransaction(transaction);
              console.log("已确认交易添加成功");
              Block = true;
            } else {
              console.error("无法找到交易,可能已经被移除:", txid);
            }
            removeTransaction(utxoi);

            if (playsound) {
              playsound = false;
              const sound = new Audio('magic.mp3');
              sound.volume = 0.5;
              sound.play();
              setTimeout(function() {
                playsound = true;
              }, 5000);
              setTimeout(() => {
                $('#refreshRune').trigger('click');
             }, 20000);
            }
          } else {
            console.error("无法找到对应的 txid:", transactionData.tx);
          }
        }
      });
      if(Block){
        setTimeout(checkMintableCount, 5000);
      }
    }
  } catch (error) {
    console.error('更新交易状态失败:', error);
  }
}


function removeFromLocalStorage(utxoi) {
  let transactions = localStorage.getItem('TransactionsV1'+istestnet);
  transactions = transactions ? JSON.parse(transactions) : {};
  if (transactions[utxoi]) {
      const transaction = transactions[utxoi];
      delete transactions[utxoi];
      localStorage.setItem('TransactionsV1'+istestnet, JSON.stringify(transactions));
      return transaction;
  }
  return null;
}

// 新增成功订单的函数
function addConfirmedTransaction(transaction) {
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  transaction.status = "success";
  transaction.confirmTime = new Date().getTime();
  confirmedTransactions[transaction.utxoi] = transaction;
  localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(confirmedTransactions));
}

// 列出未确认订单的函数
function listPendingTransactions() {
  let transactions = localStorage.getItem('TransactionsV1'+istestnet);
  transactions = transactions ? JSON.parse(transactions) : {};
  renderMintingList(Object.values(transactions));
  updateSelectAllCheckbox(); // 更新全选复选框的状态
}

// 列出已确认订单的函数
function listConfirmedTransactions() {
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  const transactions = Object.values(confirmedTransactions);

  // 移除超过7天的旧数据
  const threeDaysAgo = new Date();
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 7);
  const filteredTransactions = transactions.filter(tx => new Date(tx.confirmTime) >= threeDaysAgo);

  renderConfirmedList(filteredTransactions);
  updateConfirmedSelectAllCheckbox(); // 更新全选复选框的状态
  updateSelectAllCheckbox(); // 更新全选复选框的状态

  // 更新localStorage
  const updatedConfirmedTransactions = filteredTransactions.reduce((acc, tx) => {
      acc[tx.utxoi] = tx;
      return acc;
  }, {});
  localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(updatedConfirmedTransactions));
}

function updateConfirmedSelectAllCheckbox() {
  const confirmedOrderCheckboxes = document.querySelectorAll('.confirmed-order-checkbox');
  const allChecked = Array.from(confirmedOrderCheckboxes).every(c => c.checked);
  const allUnchecked = Array.from(confirmedOrderCheckboxes).every(c => !c.checked);

  const selectAllCheckbox = document.getElementById('selectAllCheckbox');
  if (allChecked) {
    selectAllCheckbox.checked = true;
    selectAllCheckbox.indeterminate = false;
  } else if (allUnchecked) {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = false;
  } else {
    selectAllCheckbox.checked = false;
    selectAllCheckbox.indeterminate = true;
  }
}

$(document).on('change', '.confirmed-order-checkbox', function() {
  updateConfirmedSelectAllCheckbox();
});

function collectSelectedConfirmedOrders() {
  const selectedConfirmedOrders = [];
  const checkboxes = document.querySelectorAll('.confirmed-order-checkbox:checked');
  checkboxes.forEach(function(checkbox) {
    const utxoi = checkbox.getAttribute('data-utxoi');
    selectedConfirmedOrders.push(utxoi);
  });
  return selectedConfirmedOrders;
}

$('#batchDeleteButton').on('click', function() {
  const selectedConfirmedOrders = collectSelectedConfirmedOrders();
  if (selectedConfirmedOrders.length > 0) {
    $('#confirmDeleteModal').modal('show');
  } else {
    showAlert('请至少选择一个已确认订单进行删除。');
  }
});

$('#confirmDeleteButton').on('click', function() {
  const selectedConfirmedOrders = collectSelectedConfirmedOrders();
  selectedConfirmedOrders.forEach(function(utxoi) {
    removeConfirmedTransaction(utxoi);
  });
  listConfirmedTransactions();
  $('#confirmDeleteModal').modal('hide');
});

function removeConfirmedTransaction(utxoi) {
  // 从DOM中移除这个已确认订单元素
  const transactionElement = $(`#confirmedList .transaction-item input[data-utxoi="${utxoi}"]`).closest('.transaction-item');
  if (transactionElement.length) {
    transactionElement.remove();
  }

  // 从localStorage中移除该记录
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  if (confirmedTransactions[utxoi]) {
    delete confirmedTransactions[utxoi];
    localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(confirmedTransactions));
  }
}

// 渲染已确认订单列表
function renderConfirmedList(transactions) {
  const confirmedList = $('#confirmedList');
  const noconfirmedMessage = $('#noconfirmedMessage');

  if (transactions.length === 0) {
    noconfirmedMessage.show();
    return;
  } else {
    noconfirmedMessage.hide();
  }

  // 按确认时间降序排序
  transactions.sort((a, b) => new Date(b.confirmTime) - new Date(a.confirmTime));

  // 提取铭文名称并添加到uniqueRunes Set中
  transactions.forEach(tx => {
    uniqueRunes.add(tx.tick);
  });

  // 使用uniqueRunes的值更新下拉菜单
  populateRuneFilter(Array.from(uniqueRunes));

  // 在更新DOM后,调用filterTransactions函数
  const selectedRune = $('#selectedRune').text();
  filterTransactions(selectedRune === '显示全部订单' ? 'all' : selectedRune);

  confirmedList.empty();

  transactions.forEach(transaction => {

    if(!transaction.txMinSize){
      if(transaction.addressType == "P2WPKH"){
        transaction.txMinSize = "127.25";
        transaction.txSize = "159.5";
      }else{
        transaction.txMinSize = "130";
        transaction.txSize = "173";
      }
    }

    if(!transaction.output_size){
      transaction.output_size = 0;
      transaction.placeholders = 1;
    }

    if(!transaction.serviceFee){
      transaction.serviceFee = 0;
    }

    let { totalCost, totalCost_price } = calculateTotalCost(transaction);

    const listItem = $('<div>').addClass('transaction-item');
    listItem.html(`
      <div class="checkbox-container">
        <input type="checkbox" class="form-check-input confirmed-order-checkbox" id="confirmedOrderCheckbox${transaction.utxoi}" data-utxoi="${transaction.utxoi}">
        <label for="confirmedOrderCheckbox${transaction.utxoi}" class="form-check-label"></label>
      </div>
      <p>铭文名称: <span class="rune-text">${transaction.tick}</span></p>
      <p>钱包地址: <a href="#" class="wallet-address-link" data-receive-address="${transaction.receiveAddress}">${transaction.receiveAddress.length > 20 ? 
        `${transaction.receiveAddress.slice(0, 10)}...${transaction.receiveAddress.slice(-10)}` : 
        transaction.receiveAddress}</a></p>
      <p>交易数量: ${transaction.sentCount}/${transaction.totalCount}</p>
      <p>订单费率: ${transaction.newfeeRate} sats/vB</p>
      <p>订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)</p>
      <p>确认时间: ${new Date(transaction.confirmTime).toLocaleString()}</p>
      <p class="tx-hash">交易哈希: <a href="#" class="tx-hash-link">${transaction.txid}</a></p>
    `);
    confirmedList.append(listItem);
  });
}


$('#pendingButton').on('click', function() {
  $(this).addClass('button-on active').removeClass('button-off');
  $('#confirmedButton').addClass('button-off').removeClass('button-on active');
  //$('#mintingList').empty(); // 在这里清空列表
  $('#mintingList').children().not('#noTransactionsMessage').remove();
  $('#mintingList').show();
  $('#confirmedListContainer').hide();
  $('#batchDeleteButton').hide();
  $('#batchAccelerateButton').show();
  uniqueRunes.clear();
  uniqueRunes.add('all');
  populateRuneFilter(Array.from(uniqueRunes));
  $('#selectedRune').text('显示全部订单');
  listPendingTransactions();
  // 更新任务列表计数器
  updateTaskListCounts();
});

$('#confirmedButton').on('click', function() {
  $(this).addClass('button-on active').removeClass('button-off');
  $('#pendingButton').addClass('button-off').removeClass('button-on active');
  $('#mintingList').hide();
  $('#confirmedListContainer').show();
  $('#batchAccelerateButton').hide();
  $('#batchDeleteButton').show();
  uniqueRunes.clear();
  uniqueRunes.add('all');
  populateRuneFilter(Array.from(uniqueRunes));
  $('#selectedRune').text('显示全部订单');
  listConfirmedTransactions();
  // 更新任务列表计数器
  updateTaskListCounts();
});


// 周期性获取比特币价格
async function updatebtcPrices() {
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间
  async function fetchWithTimeout(url, timeout) {
    return Promise.race([
      fetch(url),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
    ]);
  }

  // 第二个接口失败，尝试第三个接口
  try {
    const response = await fetchWithTimeout(`https://mempool.space/api/v1/prices?t=${timestamp}`, fetchTimeout);
    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
    if (data.USD) {
      btcPrices = parseInt(data.USD);
      localStorage.setItem('btcPrices' + istestnet, btcPrices);
    }else{
      const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const data = await response.json();
      if (data.USD && data.USD.last) {
        btcPrices = parseInt(data.USD.last);
        localStorage.setItem('btcPrices' + istestnet, btcPrices);
      }
    }
  } catch (error) {
    try {
      const response = await fetchWithTimeout(`https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD&t=${timestamp}`, fetchTimeout);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const data = await response.json();
      if (data.USD) {
        btcPrices = parseInt(data.USD);
        localStorage.setItem('btcPrices' + istestnet, btcPrices);
      }else{
        const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
        const data = await response.json();
        if (data.USD && data.USD.last) {
          btcPrices = parseInt(data.USD.last);
          localStorage.setItem('btcPrices' + istestnet, btcPrices);
        }
      }
    } catch (error) {
      // 第一个接口失败，尝试第二个接口
      try {
        const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
        const data = await response.json();
        if (data.USD && data.USD.last) {
          btcPrices = parseInt(data.USD.last);
          localStorage.setItem('btcPrices' + istestnet, btcPrices);
        }
      } catch (error) {
      }
    }
  }
}


function getbtcPrices() {
  updatebtcPrices(); // 首次调用，立即执行

  // 使用 setTimeout 递归调用，而不是 setInterval
  function scheduleNextUpdate() {
    setTimeout(function() {
      updatebtcPrices();
      scheduleNextUpdate();
    }, 900000); // 设置轮询间隔为300秒
  }

  scheduleNextUpdate();
}

function findUtxoiByTxid(txid) {
  const txHashElements = $('.tx-hash');
  for (let i = 0; i < txHashElements.length; i++) {
    const elem = txHashElements[i];
    if ($(elem).text().includes(txid)) {
      const txElement = $(elem).closest('.utxo-group');
      if (txElement.length && txElement.attr('id')) {
        const utxoiMatch = txElement.attr('id').match(/group-(\d+)/);
        if (utxoiMatch) {
          const utxoi = utxoiMatch[1];
          const accelerateBtn = txElement.find('.accelerate-btn');
          const wif = de1999_20240924(accelerateBtn.data('wfi'));
          const addressType = accelerateBtn.data('addresstype');
          return { utxoi, wif, addressType };  // 返回包含utxoi, wif和addressType的对象
        }
      }
      break;
    }
  }
  return null;
}

// 添加这个事件监听器
$('#addressType').on('change', function() {
  if (!isLoading) {
    saveParams(this);
  }
});

$('#tick, #amount, #orditext').on('change', function() {
  if (!isLoading) {
    saveParams(this);
  }
});

function limitInscriptionSize() {
  const inscriptionSizeInput = $('#inscriptionSize');
  const inscriptionSize = parseInt(inscriptionSizeInput.val());
  
  if (inscriptionSize < 294) {
    inscriptionSizeInput.val(294);
  }
}



$(document).ready(function () {
  // 初始化UI管理器
  UIManager.init();

  const p2trButton = $('#p2trButton');
  const p2wpkhButton = $('#p2wpkhButton');
  const addressTypeSelect = $('#addressType');

  function setActiveAddressType(type) {
    addressTypeSelect.val(type);
    localStorage.setItem('addressType'+istestnet, en1999_20240924(type));
    updateActiveButton(type);
    //fetchPendingOrders();
  }

  function updateActiveButton(type) {
    if (type === 'P2WPKH') {
      // 设置P2WPKH按钮为激活状态
      p2wpkhButton.addClass('button-on active').removeClass('button-off');
      
      // 设置P2TR按钮为非激活状态
      p2trButton.removeClass('button-on active').addClass('button-off');
    } else {
      // 设置P2TR按钮为激活状态
      p2trButton.addClass('button-on active').removeClass('button-off');
      
      // 设置P2WPKH按钮为非激活状态
      p2wpkhButton.removeClass('button-on active').addClass('button-off');
    }
    updateWalletInfo();
  }

  p2trButton.on('click', function() {
    setActiveAddressType('P2TR');
  });

  p2wpkhButton.on('click', function() {
    setActiveAddressType('P2WPKH');
  });

  // 初始化页面时根据localStorage设置正确的按钮状态
  let initialType = localStorage.getItem('addressType'+istestnet);

  if (initialType === null) {
    initialType = 'P2TR';  // 使用字符串形式的默认值
  } else {
    initialType = de1999_20240924(initialType);
  }

  setActiveAddressType(initialType);
});



window.updateAddressCount = function() {
  const addresses = $('#receiveAddress').val().split('\n').filter(addr => addr.trim().match(/^(bc1[qp]|tb1[qp])/));
  const addressCount = addresses.length;
  $('#addressCount').text(addressCount);
  updateTotalQuantity(addressCount);
  updateAddressWarning();
}

function updateTotalQuantity(addressCount) {
  const quantityPerAddress = parseInt($('#addressQuantity').val());
  const totalQuantity = addressCount * quantityPerAddress;
  $('#totalQuantity').text(totalQuantity);
  updateAddressWarning();
}

$('#receiveAddress').on('input', function() {
  window.updateAddressCount();
  updateAddressWarning();
});

$('#addressQuantity').on('change', function() {
  updateTotalQuantity($('#addressCount').text());
  updateAddressWarning();
});


$('#maxMintButton').on('click', async function() {
  let mintableResult = parseInt($('#maxMintCount').text());
  if (mintableResult) {
      const count = parseInt($('#count').val());
      if(count != mintableResult){
        $('#count').val(mintableResult > 1200 ? 1200 : mintableResult);
        mintchange = true;
        const startMintingButton = $('#startMinting');
        startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
        startMintingButton.addClass('disabled'); // 添加'disabled'类
        await checkMintableCount();
      }
  } else {
      await checkMintableCount();
      let mintableResult = parseInt($('#maxMintCount').text());
      if(mintableResult){
        const count = parseInt($('#count').val());
        if(count != mintableResult){
          $('#count').val(mintableResult > 1200 ? 1200 : mintableResult);
          mintchange = true;
          const startMintingButton = $('#startMinting');
          startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
          startMintingButton.addClass('disabled'); // 添加'disabled'类
        }
      }else{
        showAlert('当前没有可用的最大Mint数量，请先查询Mint数量。');
      }
  }
});

$(document).ready(function() {

  //点击两个开关的标题让其静默
  var RescueModeLabel = $('#RescueModeId .form-check-label');
  RescueModeLabel.on('click', function(event) {
      event.preventDefault(); // 阻止默认动作（切换复选框）
      event.stopPropagation(); // 阻止事件冒泡到父元素
  });

  var label = $('#advancedSettingsContainer .form-check-label');
  label.on('click', function(event) {
      event.preventDefault(); // 阻止默认动作（切换复选框）
      event.stopPropagation(); // 阻止事件冒泡到父元素
  });

  // 高级设置开关功能
  var advancedSettingsCheckbox = $('#advancedSettings');
  var inscriptionSizeContainer = $('#inscriptionSizeContainer');

  // 高级设置开关变化事件
  advancedSettingsCheckbox.on('change', function() {
      const isChecked = $(this).prop('checked');
      if (isChecked) {
          inscriptionSizeContainer.slideDown(300); // 显示高级设置区域
      } else {
          inscriptionSizeContainer.slideUp(300); // 隐藏高级设置区域
      }

      // 保存开关状态到本地存储
      if (!isLoading) {
          StorageManager.saveParams('advancedSettings');
      }
  });

  // 页面加载时的状态恢复已在 loadParams 函数中处理

});

async function fetchRuneInfo(runeId) {
  if (!runeId || runeId.length === 0) {
    console.log('Invalid rune ID format.');
    return null;
  }

  const url = `https://ordinals.com/rune/${encodeURIComponent(runeId)}`;
  const fetchTimeout = 5000; // 请求超时时间

  try {
    const response = await Promise.race([
      fetch(url),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      return null;
    }

    const html = await response.text();
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    const runeName = doc.querySelector('h1') ? doc.querySelector('h1').textContent.trim() : null;
    const dtElements = doc.querySelectorAll('dl dt');
    let runeIdText = null, cap = null, mints = 0, mintable = null, symbol = null, premine = null, amount = null;

    dtElements.forEach(dt => {
      const nextElement = dt.nextElementSibling ? dt.nextElementSibling.textContent.trim() : null;
      if (dt.textContent.trim() === 'id') {
        runeIdText = nextElement;
      } else if (dt.textContent.trim() === 'cap') {
        cap = nextElement;
      } else if (dt.textContent.trim() === 'mints') {
        mints = nextElement;
      } else if (dt.textContent.trim() === 'mintable') {
        mintable = nextElement;
      } else if (dt.textContent.trim() === 'symbol') {
        symbol = nextElement;
      } else if (dt.textContent.trim() === 'premine') {
        premine = parseFloat(nextElement.replace(/[^\d.]/g, ''));
      } else if (dt.textContent.trim() === 'amount') {
        amount = parseFloat(nextElement.replace(/[^\d.]/g, ''));
      }
    });

    // 计算 premine 百分比
    let preminePercentage = 0;
    if (premine && premine > 0 && amount && cap) {
      premine = parseInt(premine) / parseInt(amount);
      const total = premine + parseFloat(cap);
      preminePercentage = ((premine / total) * 100).toFixed(2);
    }

    return {
      runeName,
      runeIdText,
      symbol,
      cap,
      mints,
      mintable,
      preminePercentage
    };
  } catch (error) {
    console.error('Error fetching the document:', error);
    return null;
  }
}

async function unisatRuneInfo(runeId) {
  if (!runeId || runeId.length == 0) {
    console.log('Invalid rune ID format.');
    return null;
  }

  const fetchTimeout = 5000; // 请求超时时

  try {
    const response = await Promise.race([
      fetch("https://runes.ybot.io", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ runes: runeId })
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      console.error('Request failed:', response.statusText);
      return null;
    }

    const data = await response.json();
    const runeName = data.spacedRune || null;
    const runeIdText = data.runeid || null;
    const cap = data.cap || null;
    const mints = data.mints || 0;
    const mintable = data.mintable == true ? "true" : "false";
    const symbol = data.symbol || null;
    let premine = data.premine || 0;
    const amount = data.amount || 0;

    // 计算 premine 百分比
    let preminePercentage = 0;
    if (premine && premine > 0 && amount && cap) {
      premine = parseInt(premine) / parseInt(amount);
      const total = parseInt(premine) + parseFloat(cap);
      preminePercentage = ((premine / total) * 100).toFixed(2);
    }

    return {
      runeName,
      runeIdText,
      symbol,
      cap,
      mints,
      mintable,
      preminePercentage
    };
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}


async function getRuneInfo(runes) {
  if (!runes || runes.length === 0) {
    return null;
  }
  const fetchTimeout = 10000; // 请求超时时间
  try {
    const Lowfee = $('#Lowfee').is(':checked');
    const runesData = {
      runes,
      Lowfee: Lowfee
    };
    const encryptedData = en1999_20240924(JSON.stringify(runesData), API_ENCRYPTION_KEY);

    const response = await Promise.race([
      fetch('/api/v1/getRuneInfo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    console.log(response);

    if (!response.ok) {
      console.error('Request failed:', response.statusText);
      return null;
    }
    const result = await response.json();
    if (result.code !== 0 || !result.data) {
      console.error('Invalid response:', result);
      return null;
    }


    const data = result.data;

    console.log(data);
    
    // 检查返回的tick是否与当前输入框中的tick一致
    const currentTick = document.getElementById('tick').value.toLowerCase();
    console.log(currentTick,data.tick && data.tick.toLowerCase());
    
    if (data.tick && data.tick.toLowerCase() !== currentTick) {
      console.warn(`API返回的tick(${data.tick})与当前输入的tick(${currentTick})不一致，终止处理`);
      return null;
    }

    const runeName = data.spaced_rune || null;
    const runeIdText = data.runeId || null;
    const cap = data.cap || null;
    const mints = data.toltl_mint || 0;
    const mintable = data.mintable === "true" ? "true" : "false";
    const symbol = data.symbol || null;
    const premine = data.premine || 0;

    // 计算 premine 百分比
    let preminePercentage = 0;
    if (premine && premine > 0 && cap) {
      const total = parseInt(premine) + parseFloat(cap);
      preminePercentage = ((premine / total) * 100).toFixed(2);
    }

    return {
      runeName,
      runeIdText,
      symbol,
      cap,
      mints,
      mintable,
      preminePercentage
    };
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}


$('#walletAddressLink').on('click', function(e) {
  e.preventDefault();
  const address = $(this).attr('title');
  if (address) {
    window.open(`${getAPIUrl()}address/${address}`, '_blank');
  }
});


async function fetchFeeData() {
  const fetchTimeout = 5000; // 请求超时时间
  const primaryAPI = '/api/v1/getfee';
  const backupAPI = 'https://mempool.space/api/v1/fees/recommended';

  async function fetchWithTimeout(url, options = {}) {
    return Promise.race([
      fetch(url, options),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);
  }

  try {
    // 使用 POST 方法访问主 API
    const time = getSyncedTimestamp();
    const encryptedData = en1999_20240924(JSON.stringify({"time": time}), API_ENCRYPTION_KEY);
    const response = await fetchWithTimeout(primaryAPI, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: encryptedData }),
    });

    if (!response.ok) {
      throw new Error('主API响应失败');
    }

    const data = await response.json();
    return data;

  } catch (error) {
    console.warn('主API获取气费数据失败，尝试备用API:', error);

    try {
      // 使用 GET 方法访问备用 API
      const timestamp = new Date().getTime();
      const response = await fetchWithTimeout(`${backupAPI}?t=${timestamp}`);

      if (!response.ok) {
        throw new Error('备用API响应失败');
      }

      const data = await response.json();
      return data;

    } catch (backupError) {
      console.error('备用API获取气费数据失败:', backupError);
      return null;
    }
  }
}

async function updateFeeButtons() {
  feeData = await fetchFeeData();
  if (feeData) {
    $('#fastestFee').html(`最快<br>(${feeData.fastestFee} sat/vB)`);
    $('#halfHourFee').html(`平均<br>(${feeData.halfHourFee} sat/vB)`);
    $('#hourFee').html(`稍慢<br>(${feeData.hourFee} sat/vB)`);
    $('#custom').html(`自定义<br>(sat/vB)`);

    $('#fastestFee_utxo').html(`最快<br>(${feeData.fastestFee} sat/vB)`);
    $('#halfHourFee_utxo').html(`平均<br>(${feeData.halfHourFee} sat/vB)`);
    $('#hourFee_utxo').html(`稍慢<br>(${feeData.hourFee} sat/vB)`);
    if (!$('#custom_utxo_value').val()) {
      $('#custom_utxo_value').val(feeData.fastestFee);
    }

    const selectedFeeType = $('.fee-option.active').attr('id');
    if (selectedFeeType && selectedFeeType !== 'custom') {
      const currentFeeRate = $('#feeRate').val();
      const newFeeRate = feeData[selectedFeeType];

      if (currentFeeRate !== newFeeRate.toString()) {
        $('#feeRate').val(newFeeRate);
        const maxFee = $('#maxFee').val();
        if (!maxFee) {
          const maxFee = parseInt(newFeeRate);
          $('#maxFee').val(maxFee);
        }

        if(maxFee && parseInt(maxFee) < parseInt(newFeeRate)){
          $('#maxFee').val(newFeeRate);
        }
        checkMintableCount();
        saveParams('feeRate');
        saveParams('maxFee');
      }
    }
  }
}

$('.fee-option').on('click', function() {
  if (feeData) {
    const feeType = $(this).attr('id');
    if (feeType === 'custom') {
      if (!$('#feeRate').val()) {
        $('#feeRate').val(feeData.fastestFee);
      }
      $('#feeRate').focus();
    } else {
      $('#feeRate').val(feeData[feeType]);
    }
    $('.fee-option').removeClass('active');
    $(this).addClass('active');
    $('#feeRate').trigger('input');
    checkMintableCount();
    if (!isLoading) {
      saveParams('feeRate');
    }
  }
});

let ignoreChange = false;  // 添加一个标志变量



// 更新任务列表计数器
function updateTaskListCounts() {
  const isPendingTab = $('#pendingButton').hasClass('active');
  let totalCount = 0;
  let selectedCount = 0;

  if (isPendingTab) {
    // 未确认订单
    const visibleOrders = $('.utxo-group:visible');
    totalCount = visibleOrders.length;
    selectedCount = $('.order-checkbox:visible:checked').length;
  } else {
    // 已确认订单
    const visibleOrders = $('.transaction-item:visible');
    totalCount = visibleOrders.length;
    selectedCount = $('.confirmed-order-checkbox:visible:checked').length;
  }

  // 更新总数（左边）
  $('#taskListTotalCount').text(`(${totalCount})`);

  // 更新全选标签中的已选数量
  $('#selectedCountDisplay').text(`(${selectedCount})`);
}

// 监听其他复选框的变化，更新全选复选框的状态
function updateSelectAllCheckbox() {
  let orderCheckboxes;
  if ($('#pendingButton').hasClass('active')) {
    orderCheckboxes = $('.order-checkbox').filter(':visible');
  } else if ($('#confirmedButton').hasClass('active')) {
    orderCheckboxes = $('.confirmed-order-checkbox').filter(':visible');
  } else {
    return;
  }

  const allChecked = orderCheckboxes.length === orderCheckboxes.filter(':checked').length;
  const allUnchecked = orderCheckboxes.filter(':checked').length === 0;

  $('#selectAllCheckbox').prop('checked', allChecked);
  $('#selectAllCheckbox').prop('indeterminate', !allChecked && !allUnchecked);

  // 更新任务列表计数器
  updateTaskListCounts();
}

$(document).on('change', '.confirmed-order-checkbox', function() {
  updateSelectAllCheckbox();
});

$(document).on('change', '.order-checkbox', function() {
  updateSelectAllCheckbox();
});


$(document).on('click', '[id^="options-btn-"]', function() {
  var id = $(this).attr('id').split('-')[2];
  var deleteButton = $('#delete-btn-' + id);
  var optionsButton = $(this);

  if (deleteButton.is(':hidden')) {
    deleteButton.show();
    optionsButton.hide();

    setTimeout(function() {
      deleteButton.hide();
      optionsButton.show();
    }, 2000);
  } else {
    deleteButton.hide();
    optionsButton.show();
  }
});


$('#selectAllCheckbox').on('change', function() {
  const isChecked = $(this).prop('checked');

  if ($('#pendingButton').hasClass('active')) {
    $('.order-checkbox:visible').prop('checked', isChecked);
  } else if ($('#confirmedButton').hasClass('active')) {
    $('.confirmed-order-checkbox:visible').prop('checked', isChecked);
  }

  // 更新任务列表计数器
  updateTaskListCounts();
});

//网络同步未确认交订单
async function fetchPendingOrders() {
  // 获取 utxoi 数组
  const transactionsKey = 'TransactionsV1' + istestnet;
  const transactions = JSON.parse(localStorage.getItem(transactionsKey) || '{}');
  const utxois = Object.values(transactions).map(tx => tx.utxoi);
  const wif = $('#wif').val().trim();
  const addressType = $('#addressType').val();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间

  const data = {
    wif,
    addressType,
    utxois: utxois
  };
  const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);
  try {
    const response = await Promise.race([
      fetch('/api/v1/pending-orders?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
    if(data.length) {
      renderMintingList(data);
      TransactionStatus();
    }
  } catch (error) {
    console.error('获取未确认订单失败');
  }
}

//提交订单状态更新
async function updateorderstatus(utxois, del = 0) {
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间

  const data = {
    utxois: utxois,
    del
  };
  
  const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);
  try {
    const response = await Promise.race([
      fetch('/api/v1/update-order-status?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
  } catch (error) {
    console.error('获取未确认订单失败');
  }
}

//多地址模式下判断是否超过最大地址数量
function updateAddressWarning() {
  const addressCount = parseInt($('#addressCount').text()) || 0;
  const totalQuantity = parseInt($('#totalQuantity').text()) || 0;
  const maxAddressCount = parseInt($('#maxAddressCount').text()) || 0;
  const maxTotalQuantity = parseInt($('#maxTotalQuantity').text()) || 0;
  const addressQuantity = parseInt($('#addressQuantity').val()) || 0;
  const everyAddressMintCount = parseInt($('#everyAddressMintCount').text()) || 0;
  const isMultiAddressMode = $('#addressModeToggle').prop('checked');

  if (isMultiAddressMode && maxAddressCount > 0 && maxTotalQuantity > 0 && addressQuantity === everyAddressMintCount) {
    if (addressCount > maxAddressCount || totalQuantity > maxTotalQuantity) {
      $('.total-quantity label').addClass('red-bold');
      $('#addressWarning').show();
    } else {
      $('.total-quantity label').removeClass('red-bold');
      $('#addressWarning').hide();
    }
  } else {
    $('.total-quantity label').removeClass('red-bold');
    $('#addressWarning').hide();
  }
}

// 页面加载完成事件
$(window).on('load', async () => {
  let isFirstLoad = true;
  loadParams();
  $('#checkMintable').on('click', () => checkMintableCount(true));
  $('#startMinting').on('click', startMinting);

  // 从URL获取tick参数
  const urlParams = new URLSearchParams(window.location.search);
  const tick_url = urlParams.get('tick');
  if (tick_url) {
    if (tick_url === 'inscribe') {
      // 如果是inscribe，切换到自定义文本模式
      switchToCustomTextMode();
    } else {
      // 其他情况切换到BRC20模式
      switchToBRC20Mode();
      // 设置tick值并触发change事件
      $('#tick').val(tick_url);
    }
  } else {
    // 没有URL参数时，检查保存的模式设置
    const savedMode = localStorage.getItem('ordinalstext');
    if (savedMode === 'true') {
      switchToCustomTextMode();
    }
    $('#tick').trigger('change');
  }

  $('input:not(#selectAllCheckbox):not(#splitSlider):not(#splitCount):not(.order-checkbox):not(#advancedSettings):not(#inscriptionSize):not(#count):not(#feeRate):not(#maxFee):not(#wif):not(#darkModeToggle):not(#addrunesmint), textarea').each(function() {
    $(this).on('change', function() {
      if (!isLoading) {
        saveParams(this);
      }
    });
  });

  //设置导航条的下拉菜单
  $('.navbar-item.dropdown').on('mouseenter', function () {
    $(this).find('.dropdown-menu').stop(true, true).slideDown(200);
  });
  
  $('.navbar-item.dropdown').on('mouseleave', function () {
    $(this).find('.dropdown-menu').stop(true, true).slideUp(200);
  });

  const mode_url = urlParams.get('mode');
  if(mode_url == 'rescue'){
    $('#RescueModeId').show();
  }

  $('#RescueMode').on('change', function() {
      if ($(this).prop('checked')) {
        showAlert('当发现由于订单gas设置太低，被内存池踢掉，资金卡在中间钱包时！\n可以开启本功能并提交铸造一张相同的铭文，被卡在中间钱包的资金就会回到矿工钱包！\n\n如果你在其他地方铸造的铭文可能采用的是托管钱包，无法使用本功能救援！\n\n在使用正常的情况下无需开启这个选项！');
        $('#count').prop('disabled', true);
        $('#count').val(1);
      } else {
        $('#count').prop('disabled', false);
      }
  });

  $('#Lowfee').on('change', function() {
      if ($(this).prop('checked')) {
        showAlert('温馨提示：\n\n开启本功能后，您将可以使用低于 1 sat/vB 的超低费率发送交易。但请注意，目前主流矿池并不支持此类交易，因此您的交易可能长时间无法被打包确认，也可能无法在常规的 mempool 区块浏览器中显示。\n\n如果您随后切换回「正常费率模式」，之前低费率模式下锁定的UTXO看似已被解锁，但一旦您使用这些UTXO，则此前低费率下已广播的交易将立即失效，请谨慎操作！');
      }
      checkMintableCount();
      updateRuneData();
      $('#refreshRune').trigger('click');
  });

  // 智能适配铭文占用聪值
  function autoAdjustInscriptionSize() {
    const singleAddress = $('#singleReceiveAddress').val().trim();
    const multiAddresses = $('#receiveAddress').val().trim();
    const currentSize = parseInt($('#inscriptionSize').val()) || 330;

    // 获取当前活跃的地址输入框中的地址
    let addressesToCheck = [];
    if ($('#singleReceiveAddress').is(':visible') && singleAddress) {
      addressesToCheck = [singleAddress];
    } else if ($('#receiveAddress').is(':visible') && multiAddresses) {
      addressesToCheck = multiAddresses.split('\n').map(addr => addr.trim()).filter(addr => addr);
    }

    if (addressesToCheck.length === 0) return;

    // 检测第一个地址的类型（假设所有地址类型相同）
    const firstAddress = addressesToCheck[0];
    let targetSize = currentSize;

    if (firstAddress.startsWith('bc1q')) {
      // P2WPKH 地址，最低聪值为 294
      if (currentSize === 330) {
        targetSize = 294;
      }
    } else if (firstAddress.startsWith('bc1p')) {
      // P2TR 地址，最低聪值为 330
      if (currentSize === 294) {
        targetSize = 330;
      }
    }

    // 如果需要调整，更新值并触发事件
    if (targetSize !== currentSize) {
      $('#inscriptionSize').val(targetSize);
      $('#inscriptionSize').trigger('change');
    }
  }

  $('#inscriptionSize').on('change', function() {
    limitInscriptionSize();
    if (!isLoading) {
      saveParams(this);
    }
    checkMintableCount();
  });

  // 监听地址输入框变化，自动调整铭文占用聪值
  $('#singleReceiveAddress').on('change input', function() {
    autoAdjustInscriptionSize();
  });

  $('#receiveAddress').on('change input', function() {
    autoAdjustInscriptionSize();
  });

  $('#wif').on('change', function() {
    updateWalletInfo();
    if (!isLoading) {
      saveParams(this);
    }
    checkMintableCount();
    //fetchPendingOrders();
  });

  $('#hideBoard').click(function() {
    $('.left-panel').hide();
    $('#hideBoard').hide();
    $('#showBoard').show();
  });

  $('#showBoard').click(function() {
    $('.left-panel').show();
    $('#hideBoard').show();
    $('#showBoard').hide();
  });

  $('#feeRate').on('input', function() {
    const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
    const threshold = fastestFee * 3;
    const inputValue = parseFloat($(this).val());
  
    if (inputValue > threshold && fastestFee > 0) {
      $('#feeRateError').text('Gas 过高,请确认输入是否正确!').show();
    } else {
      $('#feeRateError').hide();
    }

    const maxFee = parseFloat($('#maxFee').val());
    const threshold_max = inputValue * 5;
  
    if (maxFee > threshold_max && inputValue > 0) {
      $('#maxFeeError').text('预留Gas过高,可能会影响铸造数量!').show();
    } else {
      $('#maxFeeError').hide();
    }

  });

  $('#feeRate').on('change', function() {
    $('.fee-option').removeClass('active');
    $('#custom').addClass('active');

    const maxFee = $('#maxFee');
    if (!maxFee.val() && $(this).val()) {
      maxFee.val(parseFloat($(this).val()) * 2);
    }
  
    if ($(this).val() && parseFloat(maxFee.val()) < parseFloat($(this).val())) {
      maxFee.val(parseFloat($(this).val()));
    }
    if($(this).val()){
      checkMintableCount();
      if (!isLoading && $(this).val()) {
        saveParams(this);
      }
    }
  });
  
  $('#maxFee').on('change', function() {
    const feeRate = parseFloat($('#feeRate').val());
    const maxFee = parseFloat($(this).val());
    const threshold = feeRate * 5;
  
    if (maxFee > threshold && feeRate > 0) {
      $('#maxFeeError').text('预留Gas过高,可能会影响铸造数量!').show();
    } else {
      $('#maxFeeError').hide();
    }

    if ($(this).val() && feeRate > parseFloat($(this).val())) {
      $(this).val(feeRate);
    }
    if($(this).val()){
      checkMintableCount();
      if (!isLoading) {
        saveParams(this);
      }
    }
  });


  $(document).on('click', '#refreshRune', function(e) {
    e.preventDefault();
    e.stopPropagation();
    const tick = $('#tick').val();
    const $refreshBtn = $(this);

    if (tick) {
        // 防止重复点击
        if ($refreshBtn.hasClass('refreshing')) {
            return;
        }

        // 显示加载状态
        $('#runeInfoSpinner').show();

        // 设置刷新状态
        $refreshBtn.addClass('refreshing');
        const originalText = $refreshBtn.text();
        //$refreshBtn.html('<span class="spinner-border spinner-border-sm" role="status"></span>');

        // 执行刷新
        fetchBRC20Info(tick, false).finally(() => { // 手动刷新按钮点击视为手动查询
            // 恢复按钮状态
            $refreshBtn.removeClass('refreshing');
            $refreshBtn.text(originalText);
        });
        startTickRefreshInterval();
    }else{
      if (tickRefreshInterval) {
        clearInterval(tickRefreshInterval);
        tickRefreshInterval = null;
      }
    }
  });

  // 选择一个选项时,更新按钮的文本并过滤交易
  $('#runeFilterDropdown').on('click', function(e) {
    e.stopPropagation();
    $(this).next('.dropdown-menu').show();
  });

  // 当选一个选项时,更新按钮的文本并过滤交易
  $('#runeFilterDropdown').next('.dropdown-menu').on('click', 'a.dropdown-item', function(e) {
    e.stopPropagation();
    const selectedRune = $(this).data('value');
    $('#selectedRune').text(selectedRune === 'all' ? '显示全部订单' : selectedRune);
    filterTransactions(selectedRune);
    $('#runeFilterDropdown').next('.dropdown-menu').hide();
  });

  $('#count').on('change', function() {
    const count = $(this).val();
    if(count > 1200) $(this).val(1200);
    mintchange = true;
    const startMintingButton = $('#startMinting');
    startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
    startMintingButton.addClass('disabled'); // 添加'disabled'类
    checkMintableCount();
  });


  $('[data-toggle="tooltip"], [data-toggle="pperchaintip"], [data-toggle="wifTooltip"], [data-toggle="mintTooltip"], [data-toggle="speeduptip"]').each(function() {
    const target = $(this).data('target');
    const content = $(target).html();
    let offsetValue = '0, 10'; // 默认偏移值
    if ($(this).data('toggle') === 'tooltip') {
        offsetValue = '100, 10'; // 如果是普通提示，设置不同的偏移值
    }
    $(this).tooltip({
        boundary: 'window',
        trigger: 'hover',
        container: 'body',
        html: true,
        placement: 'top',
        offset: offsetValue,
        title: content
    });
  });

  $('#runeBoardDropdown').on('click', function() {
    $(this).next('.dropdown-menu').toggle();
  });

  getbtcPrices()
  TransactionStatus();
  renderTransactionsFromLocalStorage();
  updateWalletInfo(); // 在页面加载完成后更新钱包信息 

  await updateFeeButtons();
  setInterval(updateFeeButtons, 30000); // 每分钟更新一次费用按钮
  TransactionStatus();
  setInterval(TransactionStatus, 30000);

  if (isFirstLoad) {
    if (feeData) {
      if(parseInt(feeData.fastestFee) > 0 && parseInt(feeData.fastestFee) >= parseInt($('#feeRate').val())){
        $('.fee-option').removeClass('active');
        $('#fastestFee').addClass('active');
        $('#feeRate').val(feeData.fastestFee);
        checkMintableCount();
        if (!isLoading) {
          saveParams('feeRate');
        }
      }else{
        $('.fee-option').removeClass('active');
        $('#custom').addClass('active');
      }
    }
    isFirstLoad = false;
  }

  setupModals();
  $('.dropdown-toggle').dropdown();


  $(document).click(function(event) {
    var target = $(event.target);
    if (!target.closest('.dropdown').length) {
        $('.dropdown-menu').hide();
    }
  });
});

function renderTransactionsFromLocalStorage() {  //初始加载未确认订单
  let storedTransactions = localStorage.getItem('TransactionsV1'+istestnet);
  storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
  const transactions = Object.values(storedTransactions);
  renderMintingList(transactions);
  //fetchPendingOrders();  //网络同步订单
}


function setupModals() {
  // 关闭按钮的点击事件处理程序
  $('#cancelNewFeeRate, #closeAccelerateFeeModal').on('click', function() {
    $('#accelerateFeeModal').modal('hide');
  });

  $('#cancelBatchAccelerate, #closeBatchAccelerate').on('click', function() {
    $('#confirmBatchAccelerateModal').modal('hide');
  });

  $('#cancelDeleteButton, #closeDeleteButton').on('click', function() {
    $('#confirmDeleteModal').modal('hide');
  });

  $('#cancelNewBatchFeeRate, #closebatchAccelerateFee').on('click', function() {
    $('#batchAccelerateFeeModal').modal('hide');
  });

  $('#closeconfirmMint, #cancelconfirmMint').on('click', function() {
    $('#confirmMintModal').modal('hide');
  });

  $('#closeSplitUtxo, #cancelSplitUtxo').on('click', function() {
    $('#SplitUtxoModal').modal('hide');
  });

  // 在模态框显示时自动将焦点设置到输入框
  $('#accelerateFeeModal').on('shown.bs.modal', function () {
    $('#newFeeRateInput').trigger('focus');
  });

  $('#batchAccelerateFeeModal').on('shown.bs.modal', function () {
    $('#newBatchFeeRateInput').trigger('focus');
  });

  // 在按下回车键时触发确认按钮的点击事件
  $('#newFeeRateInput').on('keypress', function (e) {
    if (e.which == 13) {
      $('#confirmNewFeeRate').click();
    }
  });

  $('#newBatchFeeRateInput').on('keypress', function (e) {
    if (e.which == 13) {
      $('#confirmNewBatchFeeRate').click();
    }
  });
}

function addCursorPointerStyle() {
  const style = document.createElement('style');
  style.innerHTML = `
    a, button, input[type="button"], input[type="submit"], .dropdown-toggle, .dropdown-item {
      cursor: pointer;
    }
  `;
  document.head.appendChild(style);
}

// 主题切换功能 - 提前初始化CSS
const darkModeCSS = document.createElement('link');
darkModeCSS.rel = 'stylesheet';
darkModeCSS.href = 'dark-mode.css?2025022403';
darkModeCSS.id = 'darkModeCSS';
darkModeCSS.disabled = true;
document.head.appendChild(darkModeCSS);

// 确保DOM元素可用后再获取
let darkModeToggle;

function toggleDarkMode(enabled) {
  darkModeCSS.disabled = !enabled;
  document.body.classList.toggle('dark-mode', enabled);
  
  // 更新导航栏样式
  const navbar = document.querySelector('.navbar');
  if (navbar) {
    navbar.classList.toggle('navbar-dark', enabled);
    navbar.classList.toggle('bg-dark', enabled);
  }

  // 更新导航栏链接样式
  const navbarLinks = document.querySelectorAll('.navbar-link');
  navbarLinks.forEach(link => {
    link.classList.toggle('text-light', enabled);
  });

  // 更新下拉菜单样式
  const dropdownMenus = document.querySelectorAll('.dropdown-menu');
  dropdownMenus.forEach(menu => {
    menu.classList.toggle('bg-dark', enabled);
    menu.classList.toggle('dropdown-menu-dark', enabled);
  });

  // 更新钱包图标样式
  const walletIcon = document.querySelector('.wallet-icon');
  if (walletIcon) {
    walletIcon.classList.toggle('text-light', enabled);
  }
}

// 页面加载时检查用户之前的主题选择
document.addEventListener('DOMContentLoaded', () => {
  darkModeToggle = document.getElementById('darkModeToggle');
  
  const darkModeEnabled = localStorage.getItem('darkMode') === 'true';
  darkModeToggle.checked = darkModeEnabled;
  
  // 延迟应用主题，确保CSS已加载
  setTimeout(() => {
    toggleDarkMode(darkModeEnabled);
  }, 100);
  
  // 绑定事件监听器
  darkModeToggle.addEventListener('change', () => {
    const isDarkModeEnabled = darkModeToggle.checked;
    toggleDarkMode(isDarkModeEnabled);
    localStorage.setItem('darkMode', isDarkModeEnabled ? 'true' : 'false');
  });
});


// UTXO拆分模块
$(window).on('load', async () => {   
  const $splitCount = $('#splitCount');
  const $splitSlider = $('#splitSlider');

  function syncValues(value, triggerChange = true) {
      $splitCount.val(value);
      $splitSlider.val(value);
      if (triggerChange) {
          // 用 trigger 方法同时触发 'input' 和 'change' 事件
          $splitCount.trigger('input').trigger('change');
      }
  }

  $splitCount.on('input', function() {
      let value = parseInt($(this).val());
      if (value < 1) value = 1;
      if (value > 100) value = 100;
      syncValues(value, false);  // 不触发额外的 change 事件
  });

  $splitSlider.on('input', function() {
      syncValues($(this).val());
  });

  // 初始化时同步一次值
  syncValues($splitCount.val(), false);


    $('.fee-option-utxo').on('click', function() {
      if (feeData) {
        const feeType = $(this).attr('data');
        if (feeType === 'custom') {
          if (!$('#custom_utxo_value').val()) {
            $('#custom_utxo_value').val(feeData["fastestFee"]);
          }
          $('#custom_utxo_value').focus();
        } else {
          $('#custom_utxo_value').val(feeData[feeType]);
        }
        $('.fee-option-utxo').removeClass('active');
        $(this).addClass('active');
        updateSatsPerSplit();
      }
    });

    function updateMinerFee() {
      const selectedUtxos = $('.utxo-checkbox:checked').toArray().map(checkbox => ({
        txid: $(checkbox).data('txid').split(':')[0],
        vout: parseInt($(checkbox).data('txid').split(':')[1]),
        value: parseInt($(checkbox).data('value'))
      }));
      
      const splitCount = parseInt($('#splitCount').val()) || 1;
      // 修复这行，移除多余的右括号
      const feeRate = parseFloat($('#custom_utxo_value').val()) || feeData["fastestFee"]; // 使用获取的费率
    
      if (selectedUtxos.length === 0) {
        $('#minerFee').text('矿工费: 0 BTC ($0)');
        return 0;
      }
    
      const walletAddress = $('#walletAddress').text().trim();
    
      let sendArray = [];
      let receiveArray = [];
      let countvalue = 0;
    
      selectedUtxos.forEach(utxo => {
        sendArray.push(walletAddress);
        countvalue += utxo.value;
      });
    
      for (let i = 0; i < splitCount; i++) {
        receiveArray.push(walletAddress);
      }
    
      const commitVSize = estimateTxSize(sendArray, receiveArray);
      const commitFee = Math.round(commitVSize * feeRate);
      const commitFeeFB = (commitFee / 1e8).toFixed(5);
      const commitFeeUsd = (commitFee * btcPrices / 100000000).toFixed(2);
    
      $('#minerFee').text(`矿工费: ${commitFeeFB} BTC ($${commitFeeUsd})`);
      return commitFee ? commitFee : 0;
    }
    

    function getAddressType(address) {
      if (typeof address !== "string") {
        throw new Error("Invalid address type. Expected string.");
      }
    
      if (address.startsWith("bc1q") || address.startsWith("tb1q")) {
        return "P2WPKH";
      } else if (address.startsWith("bc1p") || address.startsWith("tb1p")) {
        return "P2TR";
      } else {
        throw new Error("不支持的钱包地址类型。前缀只能选择 bc1q 或 bc1p。");
      }
    }
  
    function estimateTxSize(inputAddresses, outputAddresses) {
      let inputSize = 0;
      let outputSize = 0;
      let baseSize = 10;
      let numInputs = 0;
    
      inputAddresses.forEach((address) => {
        const inputType = getAddressType(address);
        switch (inputType) {
          case "P2WPKH":
            inputSize += 68.5;
            break;
          case "P2TR":
            inputSize += 58;
            break;
        }
        numInputs++;
      });
    
      if (numInputs > 1) {
        baseSize -= 0.5 * (numInputs - 1); // 修正多输入的大小
      }
    
      outputAddresses.forEach((address) => {
        const outputType = getAddressType(address);
        switch (outputType) {
          case "P2WPKH":
            outputSize += 31;
            break;
          case "P2TR":
            outputSize += 43;
            break;
        }
      });
    
      const txSize = inputSize + outputSize + baseSize;
      return txSize;
    }
  
    async function fetchUtxoList() {
      const timestamp = new Date().getTime(); // 生成时间戳作为随机数
      const fetchTimeout = 10000; // 请求超时时间（毫秒）
      const address = $('#walletAddressLink').attr('title');
      const Lowfee = $('#Lowfee').prop('checked');
    
      const data = {
        time: getSyncedTimestamp(),
        address,
        Lowfee
      };
    
      const encryptedData = en1999_20240924(JSON.stringify(data), API_ENCRYPTION_KEY);
    
      const fetchPromise = fetch('/api/v1/wallet-utxo?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      });
    
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('请求超时')), fetchTimeout)
      );
    
      try {
        const response = await Promise.race([fetchPromise, timeoutPromise]);
    
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
    
        const utxos = await response.json();
        return utxos;
      } catch (error) {
        console.error('获取UTXO列表失败:', error);
        throw error;
      }
    }
  
    function updateUtxoList(utxos) {
      const $container = $('#utxoDetails');
      $container.empty();
      
      if (!Array.isArray(utxos) || utxos.length ===0) {
        $container.append('<tr><td colspan="3">没有可用的 UTXO</td></tr>');
        return;
      }
  
      $.each(utxos, function(index, utxo) {
        if (!utxo || typeof utxo !== 'object' || !utxo.txid || utxo.vout === undefined || !utxo.value) {
          console.error('无效的 UTXO 对象:', utxo);
          return;
        }
  
        const $row = $('<tr>').html(`
          <td>${utxo.txid.substring(0, 6)}...${utxo.txid.slice(-6)}:${utxo.vout}</td>
          <td>${(utxo.value / 100000000).toFixed(6)} BTC ($${(utxo.value * btcPrices / 100000000).toFixed(2)})</td>
          <td><input type="checkbox" class="form-check-input utxo-checkbox" data-value="${utxo.value}" data-txid="${utxo.txid}:${utxo.vout}:${utxo.value}"></td>
        `);
        $container.append($row);
      });
    }
  
    function updateSelectedUtxoInfo() {
      const selectedUtxos = $('.utxo-checkbox:checked');
      const totalSats = selectedUtxos.toArray().reduce((sum, checkbox) => sum + parseInt($(checkbox).data('value')), 0);
      const totalUsd = (totalSats * btcPrices / 100000000).toFixed(2);
      $('#selectedUtxoInfo').text(`已选: ${selectedUtxos.length} 个UTXO - ${(totalSats / 100000000).toFixed(4)} BTC ($${totalUsd})`);
      updateSatsPerSplit();
    }
    
    function updateSatsPerSplit() {
      const feeRate = updateMinerFee();
      const splitCount = parseInt($('#splitCount').val()) || 1;
      const totalSats = $('.utxo-checkbox:checked').toArray().reduce((sum, checkbox) => sum + parseInt($(checkbox).data('value')), 0);
      const satsPerSplit = Math.floor((totalSats - feeRate) / splitCount);
      const usdPerSplit = (satsPerSplit * btcPrices / 100000000).toFixed(2);
      $('#satsPerSplit').text(`${(satsPerSplit / 100000000).toFixed(8)} BTC ($${usdPerSplit})`);
      $('#splitSlider').val(splitCount);
      
    }
  
  // 更新事件监听器设置
  function setupEventListeners() {
    // 移除之前可存在事件监听器
    $('#selectAllUtxos').off('change');
    $('#utxoDetails').off('change', '.utxo-checkbox');
    $('#splitCount').off('input');
    $('#decreaseSplits').off('click');
    $('#increaseSplits').off('click');
    $('#confirmSplitUtxo').off('click');

    // 全选/取消全选
    $('#selectAllUtxos').on('change', function() {
      $('.utxo-checkbox').prop('checked', this.checked);
      updateSelectedUtxoInfo();
    });

    // 单个 UTXO 选择
    $('#utxoDetails').on('change', '.utxo-checkbox', updateSelectedUtxoInfo);

    // 拆分数量变化
    $('#splitCount').on('input', updateSatsPerSplit);

    // 增减拆分数量按钮
    $('#decreaseSplits').on('click', function() {
      const $input = $('#splitCount');
      let value = parseInt($input.val());
      if (value > 1) {
        $input.val(value - 1);
        $input.trigger('input');
      }
    });

    $('#increaseSplits').on('click', function() {
      const $input = $('#splitCount');
      let value = parseInt($input.val());
      $input.val(value + 1);
      $input.trigger('input');
    });

    $('#custom_utxo_value').on('input', function() {
      updateSatsPerSplit();
      //updateMinerFee();
    });

    // 确认拆分 UTXO
    $('#confirmSplitUtxo').on('click', async function() {
      const splitCount = parseInt($('#splitCount').val());
      const selectedUtxos = $('.utxo-checkbox:checked').toArray().map(checkbox => ({
        txid: $(checkbox).data('txid').split(':')[0],
        vout: parseInt($(checkbox).data('txid').split(':')[1]),
        value: parseInt($(checkbox).data('value'))
      }));
    
      const feeRate = parseFloat($('#custom_utxo_value').val()) || feeData["fastestFee"];
      const Lowfee = $('#Lowfee').prop('checked');
    
      if (selectedUtxos.length === 0) {
        //showAlert('请选择至少一个UTXO进行拆分');
        alert('请选择至少一个UTXO进行拆分');
        return;
      }
    
      if (splitCount < 1) {
        //showAlert('拆分数量必须大于等于1');
        alert('拆分数量必须大于等于1');
        return;
      }
    
      const wif = $('#wif').val().trim();
      const addressType = $('#addressType').val();
    
      const timestamp = new Date().getTime();
      const data = {
        wif: wif,
        addressType,
        utxos: selectedUtxos,
        splitCount,
        feeRate,
        Lowfee
      };
    
      const encryptedData = en1999_20240924(JSON.stringify({ data }), API_ENCRYPTION_KEY);
    
      // 显示加载提示
      $('#SplitUtxoModal .modal-body').hide();
      $('#SplitUtxoModal .modal-footer').hide();
      $('#SplitUtxoModal .modal-content').append('<div class="loading-message">正在提交，请稍候...</div>');
    
      try {
        const response = await fetch('/api/v1/split-utxo?t=' + timestamp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        });
    
        // 移除加载提示
        $('#SplitUtxoModal .loading-message').remove();
    
        if (!response.ok) {
          const errorResponse = await response.json();
          throw new Error(errorResponse.error || '网络响应失败');
        }
    
        const result = await response.json();
        if (result.code === 1 && result.txid) {
          alert('UTXO拆分成功！交易ID：' + result.txid);
          $('#SplitUtxoModal').modal('hide');
          $('#SplitUtxoModal .modal-body').show();
          $('#SplitUtxoModal .modal-footer').show();
          // 在这里添加刷新UTXO列表的逻辑
        } else {
          throw new Error(result.error || '未知错误');
        }
      } catch (error) {
        console.error('Error:', error);
    
        // 移除加载提示并恢复模态框内容
        $('#SplitUtxoModal .loading-message').remove();
        $('#SplitUtxoModal .modal-body').show();
        $('#SplitUtxoModal .modal-footer').show();

        alert('发生错误：' + error.message);
      }
    });
    
  }

  // 当态框显示时，获取 UTXO 列表并初始化
  $('#SplitUtxoModal').on('show.bs.modal', function () {
    $('#utxoDetails').html('<tr><td colspan="3">正在加载 UTXO 列表，请稍候...</td></tr>');
    fetchUtxoList()
      .then(function(fetchedUtxos) {
        SplitutxoList = fetchedUtxos;
        updateUtxoList(SplitutxoList);
      })
      .catch(function(error) {
        console.error('获取UTXO列表失败:', error);
        $('#utxoDetails').html('<tr><td colspan="3">无法加载 UTXO 列表，请稍后再试</td></tr>');
      });
    $('#splitCount').val(2);
    $('#selectAllUtxos').prop('checked', false);
    updateSelectedUtxoInfo();
    setupEventListeners();
  });

  // 点击分 UTXO 按钮时，显示模态框
  $('#SplitUtxo').on('click', function() {
    const address = $('#walletAddressLink').attr('title');
    console.log(address);
    if(address == undefined){
      showAlert('要拆分UTXO，请先导入矿工钱包！');
      return;
    }
    $('#SplitUtxoModal').modal('show');
  });
});

checkBlockHeight();
setInterval(checkBlockHeight, 20000);
//周期性检查区块高度
async function checkBlockHeight() {
  //const activeTxIds = collectActiveTxIds();
  const fetchTimeout = 10000; // 请求超时时间
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const index = currentBlockHeight;
  const addrunesmint = $('#addrunesmint').prop('checked'); //是否同时铸造符文
  const mintHexData = $('#mintHexData').val();
  const tick = $('#tick').val();

  try {
    const response = await Promise.race([
      fetch(`${getAPIUrl()}api/blocks/tip/height?t=${timestamp}`),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }

    const newBlockHeight = parseInt(await response.text());

    if (newBlockHeight > 840000 && newBlockHeight !== currentBlockHeight) {
      currentBlockHeight = newBlockHeight;
      await sleep(5000);
      //if(activeTxIds) {
        //await monitorTransactionHashes();
        //await updateTransactionStatus();
      //}
      if(addrunesmint && mintHexData && mintHexData !== ""){
        $('#refreshRune2').trigger('click');
      }
      if(tick && tick !== ""){
        $('#refreshRune').trigger('click');
      }
    }
  } catch (error) {
    console.error('获取区块高度失败:', error);
  }
}

//==================== 同时铸造符文 ====================

$(document).ready(function() {
  // 符文铸造开关处理
  $('#addrunesmint').on('change', function() {
    if ($(this).is(':checked')) {
      $('#runesMintContainer').slideDown();
      const runeId = $('#runes').val();
      if (runeId && runeId.trim() !== '') {
        fetchRuneInfo2(runeId);
      }
    } else {
      $('#runesMintContainer').slideUp();
    }
    // 保存复选框状态
    if (!isLoading) {
      saveParams('addrunesmint');
    }
    setTimeout(function() {
      checkMintableCount();
    }, 2000);
  });

  // 符文名称/ID输入框变化处理
  $('#runes').on('input change', function() {
    const runeId = $(this).val().trim();
    if (runeId !== '') {
      // 如果符文铸造开关已开启，则自动加载符文信息
      if ($('#addrunesmint').is(':checked')) {
        // 立即隐藏数量显示元素，提供即时视觉反馈
        $('.rune-quantity2').hide();
        fetchRuneInfo2(runeId);
      }
    }
    // 保存输入值
    if (!isLoading) {
      saveParams('runes');
    }
  });

  // 符文信息刷新按钮
  $('#refreshRune2').on('click', function(e) {
    e.preventDefault();
    const runeId = $('#runes').val();
    const $refreshBtn = $(this);

    if (runeId && runeId.trim() !== '') {
      // 防止重复点击
      if ($refreshBtn.hasClass('refreshing')) {
        return;
      }

      // 立即隐藏数量显示元素，提供即时视觉反馈
      $('.rune-quantity2').hide();

      // 设置刷新状态
      $refreshBtn.addClass('refreshing');
      const originalText = $refreshBtn.text();
      //$refreshBtn.html('<span class="spinner-border spinner-border-sm" role="status"></span>');

      // 执行刷新
      fetchRuneInfo2(runeId).finally(() => {
        // 恢复按钮状态
        $refreshBtn.removeClass('refreshing');
        $refreshBtn.text(originalText);
      });
    }
  });

  // mintHexData 输入框变化处理（虽然隐藏，但需要保存其值的变化）
  $('#mintHexData').on('input change', function() {
    if (!isLoading) {
      saveParams('mintHexData');
    }
  });

  // 初始化时符文铸造功能是关闭的
  $('#runesMintContainer').hide();
});


// 获取符文信息的函数
async function fetchRuneInfo2(runeId) {
  if (!runeId || typeof runeId !== 'string') {
    console.log('Invalid rune ID format.');
    $('#mintHexData').val('');
    return;
  }

  const spinnerElement = $('#runeInfoSpinner2');
  const currentRuneId = $('#runes').val();
  const isSameRune = runeId === currentRuneId && $('#runeInfo2').is(':visible');

  spinnerElement.show();

  try {
    const runesData = {
      runes: runeId  // 保持原始大小写和特殊字符
    };

    // 相同符文刷新时保持进度显示，不同符文时隐藏
    if (!isSameRune) {
      $('.rune-quantity2').hide();
    }
    const response = await fetch('https://ybot.io/api/v1/getRuneInfo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: en1999_20240924(JSON.stringify(runesData), API_ENCRYPTION_KEY) }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    updateRuneInfo2(data);
  } catch (error) {
    console.error('Error fetching rune info:', error);
    $('#state').text('[获取失败]').addClass('text-danger');
  } finally {
    spinnerElement.hide();
  }
}

// 新增专门用于更新符文信息的函数
function updateRuneInfo2(data) {
  console.log('Updating rune info with:', data);

  if (data.code === 0 && data.data) {
    const runeInfo = data.data;

    $('#runeId').text(runeInfo.runeId || '未知');
    $('#mintHexData').val(runeInfo.runeId || '');
    $('.hot-springs').text(runeInfo.symbol || '');
    $('.Runeinfo2').attr('href', `https://ordinals.com/rune/${runeInfo.runeId}`);
    $('.rune-mint-text2').text((runeInfo.premine || 0) + '%');
    $('#runes').val(runeInfo.spaced_rune || '');
    
    const minted = formatNumber(Number(runeInfo.toltl_mint || 0));
    const cap = runeInfo.cap.length > 12 ? "无限" : formatNumber(Number(runeInfo.cap || 0));
    const progress = `${minted}/${cap}`;
    $('#runeMintProgress').text(progress);
    
    const isMintable = runeInfo.mintable === 'true';
    const mints = runeInfo.mints ? parseInt(runeInfo.mints) : 0;

    $('#state')
      .text(isMintable ? '[可铸造]' : (mints > 0) ? '[已停止]' : '[未开始]')
      .removeClass('text-danger text-success')
      .addClass(isMintable ? 'text-success' : 'text-danger');
    $('.runeshow').show();
    $('.rune-quantity2').show();
  } else {
    $('.hot-springs').text('');
    $('.runeshow').hide();
    $('.rune-quantity2').hide();
    $('#runeId').text('未知');
    $('#mintHexData').val('');
    $('.rune-mint-text2').text('0%');
    $('#runeMintProgress').text('加载失败');
    $('#state')
      .text('[数据错误]')
      .removeClass('text-success')
      .addClass('text-danger');
  }
}


// 格式化数字显示
function formatNumber(num) {
    if (num >= 100000000) { // 1亿及以上
        return (num / 100000000).toFixed(2) + '亿';
    } else if (num >= 100000) { // 10万及以上
        return (num / 10000).toFixed(2) + '万';
    }
    return num.toString(); // 小于10万时直接显示原始数字,不添加千位分隔符
}

// 全局变量
let myChart; // 用于保存图表实例

// 更新图表函数
function updateChart(feeDistribution) {
    // 显示图表容器
    $('.rune-progress-container').show();

    // 延迟渲染图表，确保容器已完全显示
    setTimeout(() => {
        const ctx = document.getElementById('myChart').getContext('2d');

        const labels = Object.keys(feeDistribution);
        const counts = Object.values(feeDistribution);

        // 响应式字体大小 - 根据屏幕宽度调整
        let fontSize = 12;
        let barThickness = 20;

        if (window.innerWidth <= 576) {
            fontSize = 36; // 超小屏幕使用极大字体
            barThickness = 15;
        } else if (window.innerWidth <= 768) {
            fontSize = 32; // 小屏幕使用超大字体
            barThickness = 18;
        } else if (window.innerWidth <= 991) {
            fontSize = 26; // 中等屏幕使用大字体
            barThickness = 19;
        }

        let fontcolor = (localStorage.getItem('darkMode') === 'true') ? 'rgba(50, 50, 50, 1)' : 'rgba(136, 136, 136, 1)';

        const chartData = {
            labels: labels,
            datasets: [{
                label: '交易量',
                data: counts,
                backgroundColor: '#28a745',
                borderColor: '#28a745',
                borderWidth: 1,
                barThickness: barThickness,
                maxBarThickness: 50
            }]
        };

        const chartOptions = {
            maintainAspectRatio: false,
            responsive: true, // 启用响应式
            devicePixelRatio: window.devicePixelRatio || 1, // 使用设备像素比例
            layout: {
                padding: {
                    top: fontSize + 10, // 根据字体大小动态调整顶部间距
                    bottom: 5,
                    left: 5,
                    right: 5
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    suggestedMax: Math.max(...counts) * 1.15,
                    grid: {
                        display: false,
                        drawBorder: false
                    },
                    ticks: {
                        display: false,
                        font: {
                            size: fontSize
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: fontcolor,
                        font: {
                            size: fontSize
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const section = `${context.label} sat/vB`;
                            const count = context.raw;
                            return `${section} (交易量: ${count})`;
                        }
                    }
                },
                datalabels: {
                    align: 'end',
                    anchor: 'end',
                    offset: -6,
                    color: fontcolor,
                    font: {
                        weight: 'bold',
                        size: fontSize
                    }
                }
            }
        };

        if (myChart) {
            // 更新现有图表数据和选项，避免重新创建导致的动画飘移
            myChart.data = chartData;
            myChart.options = chartOptions;
            myChart.update();
        } else {
            // 创建新图表
            myChart = new Chart(ctx, {
                type: 'bar',
                data: chartData,
                options: chartOptions,
                plugins: [ChartDataLabels]
            });
        }
    }, 100); // 延迟 100 毫秒
}

// fetchBRC20Info 函数
// isAutoRefresh: true表示自动刷新调用，false表示手动查询调用
async function fetchBRC20Info(tick, isAutoRefresh = false) {
  // 更新为新的铭文信息模块元素
  const inscriptionContainer = $('.inscription-info-container');
  const runeInfoSpinner = $('#runeInfoSpinner'); // 主加载动画
  const Lowfee = $('#Lowfee').is(':checked');

  // 检查是否是相同tick的刷新
  const currentTick = $('#tick').val().toLowerCase();
  const isSameTick = tick.toLowerCase() === currentTick && inscriptionContainer.is(':visible');

  try {
      // 显示主加载动画
      runeInfoSpinner.show();

      if (isSameTick) {
          // 相同tick刷新：保持内容显示，只显示加载动画
          runeInfoSpinner.show();
          // 重置内存池信息为加载状态
          $('.inscription-info-container #memsum').text('加载中...');
          $('#mempool-spinner').attr('style', 'margin-top: -3px; display: inline-block;');
      } else {
          // 不同tick或首次加载：隐藏铭文信息容器并重置内存池信息
          inscriptionContainer.hide();
          runeInfoSpinner.show();
          // 重置内存池信息为加载状态
          $('.inscription-info-container #memsum').text('加载中...');
          $('#mempool-spinner').attr('style', 'margin-top: -3px; display: inline-block;');

          // 对于新的tick，立即显示持有人列表加载状态
          HoldersManager.showLoadingState();
      }

      // 检查是否需要并行请求持有人数据
      const normalizedTick = tick.toLowerCase();
      const cached = AppState.holdersCache.get(normalizedTick);
      const shouldFetchHolders = !isSameTick && !cached; // 新tick且无缓存时并行请求

      // 构造 getRuneInfo 请求数据
      const runesData = {
          runes: normalizedTick,
          Lowfee: Lowfee
      };
      const encryptedRuneData = en1999_20240924(JSON.stringify(runesData), API_ENCRYPTION_KEY);

      // 并行请求：同时发起 getRuneInfo 和 getHolder 请求
      const requests = [
          fetch('/api/v1/getRuneInfo', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
              },
              body: JSON.stringify({ data: encryptedRuneData }),
          })
      ];

      // 如果需要获取持有人数据，添加到并行请求中
      let holdersPromise = null;
      if (shouldFetchHolders) {
          const holdersData = {
              tick: normalizedTick,
              height: 0, // 新tick无缓存，使用0获取全量数据
              time: getSyncedTimestamp()
          };
          const encryptedHoldersData = en1999_20240924(JSON.stringify(holdersData), API_ENCRYPTION_KEY);

          holdersPromise = fetch('/api/v1/getHolder', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
              },
              body: JSON.stringify({ data: encryptedHoldersData }),
          });

          requests.push(holdersPromise);
      }

      // 等待所有请求完成
      const responses = await Promise.all(requests);
      const runeResponse = responses[0];
      const holdersResponse = responses[1]; // 可能为 undefined

      const result = await runeResponse.json();

      // 处理并行获取的持有人数据
      let holdersData = null;
      if (holdersResponse) {
          try {
              if (holdersResponse.ok) {
                  holdersData = await holdersResponse.json();
                  if (holdersData && !holdersData.error && holdersData.result) {
                      // 验证API响应的tick是否与请求的tick一致
                      const responseTick = (holdersData.result.tick || '').toLowerCase();
                      const requestTick = normalizedTick;

                      if (responseTick === requestTick) {
                          // 更新缓存
                          HoldersManager.updateCache(normalizedTick, holdersData, holdersData.result.block_height || 0);
                          console.log(`并行获取持有人数据成功: tick=${tick}`);
                      } else {
                          console.log(`并行获取的持有人数据tick(${responseTick})与请求tick(${requestTick})不匹配，忽略该数据`);
                          holdersData = null;
                      }
                  }
              }
          } catch (error) {
              console.error('处理并行持有人数据失败:', error);
          }
      }

      if (runeResponse.ok && result.code === 0 && result.data) {
          const data = result.data;

          // 验证API响应的tick是否与当前输入框中的tick一致
          const currentInputTick = $('#tick').val().toLowerCase();
          const responseTick = (data.tick || '').toLowerCase();

          if (responseTick !== currentInputTick) {
              console.log(`getRuneInfo API响应的tick(${responseTick})与当前输入(${currentInputTick})不匹配，忽略该响应`);
              runeInfoSpinner.hide();
              return;
          }

          // 检查返回的tick是否与当前输入框中的tick一致
          const currentTick = document.getElementById('tick').value.toLowerCase();
          console.log(currentTick,data.tick && data.tick.toLowerCase());

          if (data.tick && data.tick.toLowerCase() !== currentTick) {
            console.warn(`API返回的tick(${data.tick})与当前输入的tick(${currentTick})不一致，终止处理`);
            return null;
          }

          // 检查是否为未知铭文或返回空白数据
          if (parseFloat(data.limitPerMint) === 0 || parseFloat(data.supply) === 0) {
              // 根据调用来源决定错误处理策略
              if (isAutoRefresh) {
                  // 自动刷新时：静默处理，不显示错误提示，等待下一轮刷新
                  console.log('自动刷新获取到空白数据，静默忽略，等待下一轮刷新');
                  runeInfoSpinner.hide();
                  return; // 静默结束函数执行，保持当前显示状态
              } else {
                  // 手动查询时：显示友好提示
                  showAlert('无法读取铭文信息，可能是网络延迟或铭文不存在，请稍后重试');
                  inscriptionContainer.hide();
                  $('.rune-progress-container').hide();

                  // 清空持有人列表
                  HoldersManager.showEmptyState();

                  // 隐藏内存池相关显示
                  $('.inscription-info-container #memsum').hide();
                  // 使用延迟执行确保jQuery能找到元素
                  setTimeout(() => {
                    $('#mempool-spinner').attr('style', 'margin-top: -3px; display: none !important;');
                  }, 10);

                  // 销毁图表（如果存在）
                  if (myChart) {
                      myChart.destroy();
                      myChart = null;
                  }

                  // 隐藏加载动画，但不影响铭文信息模块的转动图标
                  runeInfoSpinner.hide();

                  return; // 结束函数执行
              }
          }

          // 更新铭文信息模块
          updateInscriptionInfo(data);

          // 计算铭文张数
          const limitPerMint = parseFloat(data.limitPerMint);
          const totalSupply = Math.floor(parseFloat(data.supply) / limitPerMint); // 总张数
          const mintedCount = Math.floor(parseFloat(data.minted) / limitPerMint); // 已铸造张数
          const remaining = totalSupply - mintedCount;
          const percent = ((mintedCount / totalSupply) * 100).toFixed(2);

          updateURL(tick);

          // 填充 gas size
          $('#gassize').text(parseFloat(data.median_fee).toFixed(1) + ' sats/vB');
          $('.text-success').show();

          $('#pperchain').text(parseInt(data.pperchain).toString() + "张");
          if(parseFloat(data.pperchain_fee).toFixed(2) > 0){
            $('#pperchain_fee').text(parseFloat(data.pperchain_fee).toFixed(2) + " sats/vB");
            $('#pperchain_fee_label').show();
          }else{
            $('#pperchain_fee').text("");
            $('#pperchain_fee_label').hide();
          }

          // 显示内存池透视容器
          $('.rune-progress-container').show();

          // 自动填充 amount 输入框
          if (data.limitPerMint) {
              $('#amount').val(data.limitPerMint);
              updateOrditext();
          }

          // **集成图表更新**
          if (data.fee_distribution && Object.keys(data.fee_distribution).length > 0) {
              updateChart(data.fee_distribution);
          } else {
              // 如果 fee_distribution 为空，可以选择隐藏图表或显示默认信息
              $('.rune-progress-container').hide();
              if (myChart) {
                  myChart.destroy();
                  myChart = null;
              }
          }

          // 显示铭文信息容器
          inscriptionContainer.show();

          // 更新持有人列表
          const currentBlockHeight = data.current_block_height || 0;

          if (holdersData && !isSameTick) {
              // 如果有并行获取的持有人数据，直接渲染
              HoldersManager.renderHoldersList(holdersData);
              console.log(`使用并行获取的持有人数据: tick=${tick}`);
          } else {
              // 否则按正常流程获取持有人数据
              HoldersManager.updateHoldersList(tick, !isSameTick, currentBlockHeight);
          }

          // 隐藏主加载动画
          runeInfoSpinner.hide();
          // 内存池转动图标的显示由 updateInscriptionInfo 函数中的数据决定

      } else {
          // 根据调用来源决定错误处理策略
          if (isAutoRefresh) {
              // 自动刷新时：静默处理，不显示错误提示
              console.log('自动刷新API响应异常，静默忽略，等待下一轮刷新');
              runeInfoSpinner.hide();
              return; // 静默结束函数执行，保持当前显示状态
          } else {
              // 手动查询时：显示友好提示
              showAlert('无法读取铭文信息，可能是网络延迟或铭文不存在，请稍后重试');
              inscriptionContainer.hide();
              $('.rune-progress-container').hide();

              // 清空持有人列表
              HoldersManager.showEmptyState();

              if (!isLoading) {
                  updateOrditext();
              }

              // 隐藏加载动画，但不影响铭文信息模块的转动图标
              runeInfoSpinner.hide();
          }
      }
  } catch (error) {
      console.error('获取铭文信息失败:', error);

      // 根据调用来源决定错误处理策略
      if (isAutoRefresh) {
          // 自动刷新时：静默处理，不显示错误提示
          console.log('自动刷新网络异常，静默忽略，等待下一轮刷新');
          runeInfoSpinner.hide();
          return; // 静默结束函数执行，保持当前显示状态
      } else {
          // 手动查询时：显示友好提示
          showAlert('无法读取铭文信息，可能是网络延迟或铭文不存在，请稍后重试');
          inscriptionContainer.hide();
          $('.rune-progress-container').hide();

          // 清空持有人列表
          HoldersManager.showEmptyState();

          // 隐藏内存池相关显示
          $('.inscription-info-container #memsum').hide();
          // 使用延迟执行确保jQuery能找到元素
          setTimeout(() => {
            $('#mempool-spinner').attr('style', 'margin-top: -3px; display: none !important;');
          }, 10);

          if (!isLoading) {
              updateOrditext();
          }

          // 隐藏加载动画，但不影响铭文信息模块的转动图标
          runeInfoSpinner.hide();
      }
  } finally {
      // 确保主加载动画被隐藏，但不影响铭文信息模块的转动图标
      runeInfoSpinner.hide();
  }
}

// 更新铭文信息模块的函数
function updateInscriptionInfo(data) {
  const tick = data.tick || '';

  // 更新铭文标题
  $('.inscription-title').text(tick);

  // 更新社交链接
  updateSocialLinks(tick);

  // 计算进度数据
  const limitPerMint = parseFloat(data.limitPerMint);
  const totalSupply = Math.floor(parseFloat(data.supply) / limitPerMint);
  const mintedCount = Math.floor(parseFloat(data.minted) / limitPerMint);
  const percent = ((mintedCount / totalSupply) * 100).toFixed(2);

  // 计算内存池进度
  let memPoolPercent = 0;
  if (data.total_txs && data.limitPerMint) {
    // total_txs 是内存池中的交易数量，每个交易铸造 limitPerMint 数量
    const memPoolCount = Math.floor(parseFloat(data.total_txs));
    memPoolPercent = ((memPoolCount / totalSupply) * 100).toFixed(2);
  }

  // 更新双进度条 - 使用更具体的选择器避免影响铭文看板
  const greenBar = $('.inscription-info-container .progress-bar.bg-green');
  const redBar = $('.inscription-info-container .progress-bar.bg-red');

  greenBar.css('width', percent + '%').attr('aria-valuenow', percent);
  redBar.css('width', memPoolPercent + '%').attr('aria-valuenow', memPoolPercent);

  $('.inscription-info-container .progress-percentage').text(`　${percent}%`);

  // 更新进度文本
  $('.inscription-info-container .progress-numbers').text(`${formatNumber(mintedCount)} / ${formatNumber(totalSupply)} 张`);

  // 更新内存池信息（如果有数据）
  if (data.total_txs && data.unique_addresses) {
    $('.inscription-info-container #memsum').text(`${formatNumber(data.total_txs)}张（${formatNumber(data.unique_addresses)}人）`);
    $('.inscription-info-container #memsum').show();
    $('#mempool-spinner').attr('style', 'margin-top: -3px; display: inline-block;');
  } else {
    // 如果没有内存池数据，隐藏相关显示
    $('.inscription-info-container #memsum').hide();
    // 使用延迟执行确保jQuery能找到元素
    setTimeout(() => {
      $('#mempool-spinner').attr('style', 'margin-top: -3px; display: none !important;');
    }, 10);
  }

  // 更新详细信息网格
  updateInscriptionDetails(data, limitPerMint, totalSupply);
}

// 更新社交链接
function updateSocialLinks(tick) {
  const encodedTick = encodeURIComponent(tick);

  // 更新各个社交链接
  $('.social-icons a').each(function() {
    const $link = $(this);
    const title = $link.attr('title');

    switch(title) {
      case 'uniscan':
        $link.attr('href', `https://uniscan.cc/brc20/${encodedTick}`);
        break;
      case 'OKX':
        $link.attr('href', `https://www.okx.com/zh-hans/web3/marketplace/ordinals/token/${encodedTick}`);
        break;
      case 'Unisat':
        $link.attr('href', `https://unisat.io/market/brc20?tick=${encodedTick}`);
        break;
      case 'Twitter':
        $link.attr('href', `https://x.com/search?q=${encodedTick}`);
        break;
    }
  });
}

// 更新铭文详细信息网格
function updateInscriptionDetails(data, limitPerMint, totalSupply) {
  const detailItems = $('.inscription-details-grid .detail-item');

  // 单次最大铸刻
  $(detailItems[0]).find('strong').text(formatNumber(limitPerMint));

  // 总量
  const totalAmount = formatNumber(totalSupply);
  $(detailItems[1]).find('strong').text(totalAmount + '张');

  // 公开铸刻状态
  const isPublicMint = !data.selfMint;
  const statusElement = $(detailItems[2]).find('strong');
  if (isPublicMint) {
    statusElement.removeClass('text-danger').addClass('text-success').text('✓');
  } else {
    statusElement.removeClass('text-success').addClass('text-danger').text('✗');
  }

  // 部署者钱包
  if (data.deployer_wallet) {
    const shortWallet = data.deployer_wallet.substring(0, 6) + '...' + data.deployer_wallet.substring(data.deployer_wallet.length - 4);
    $(detailItems[3]).find('a').text(shortWallet).attr('href', `https://mempool.space/address/${data.deployer_wallet}`);
  }

  // 部署Hash
  if (data.deploy_txid) {
    const shortTxid = data.deploy_txid.substring(0, 6) + '...' + data.deploy_txid.substring(data.deploy_txid.length - 4);
    $(detailItems[4]).find('a').text(shortTxid).attr('href', `https://mempool.space/tx/${data.deploy_txid}`);
  }

  // 持有人数量
  if (data.holders_total) {
    $(detailItems[5]).find('strong').text(data.holders_total);
  }
}

// 更新内存池透视信息
function updateMemPoolInfo(data) {
  // 填充 gas size
  if (data.median_fee) {
    $('#gassize').text(parseFloat(data.median_fee).toFixed(1) + ' sats/vB');
  } else {
    $('#gassize').text('加载中');
  }

  // 更新下个区块信息
  if (data.pperchain) {
    $('#pperchain').text(parseInt(data.pperchain).toString() + "张");
    if(parseFloat(data.pperchain_fee).toFixed(2) > 0){
      $('#pperchain_fee').text(parseFloat(data.pperchain_fee).toFixed(2) + " sats/vB");
      $('#pperchain_fee_label').show();
    }else{
      $('#pperchain_fee').text("");
      $('#pperchain_fee_label').hide();
    }
  } else {
    $('#pperchain').text('加载中');
    $('#pperchain_fee').text('');
    $('#pperchain_fee_label').hide();
  }

  // 强制显示内存池透视容器和相关元素
  setTimeout(() => {
    $('.rune-progress-container').show();
    $('#gassize').show();
    $('#pperchain').show();
    // pperchain_fee_label 的显示由数据决定，不在这里强制显示
    $('.text-success').show();
    // 内存池转动图标的显示由数据决定，不在这里强制显示
  }, 100);
}

// 监听窗口大小变化，动态调整图表
window.addEventListener('resize', () => {
    if (myChart) {
        // 延迟更新，避免频繁调用
        clearTimeout(window.chartResizeTimeout);
        window.chartResizeTimeout = setTimeout(() => {
            // 获取当前图表的数据
            const currentData = myChart.data;
            if (currentData && currentData.labels && currentData.labels.length > 0) {
                // 重新构建费率分布对象
                const feeDistribution = {};
                currentData.labels.forEach((label, index) => {
                    feeDistribution[label] = currentData.datasets[0].data[index];
                });
                // 重新创建图表以应用新的字体大小
                updateChart(feeDistribution);
            }
        }, 300);
    }
});

function startTickRefreshInterval() {
  // 清除可能存在的旧定时器
  if (tickRefreshInterval) {
    clearInterval(tickRefreshInterval);
  }

  // 设置新的定时器，每30秒执行一次
  tickRefreshInterval = setInterval(() => {
    const tick = $('#tick').val();
    const runeId = $('#runes').val();
    const isRunesMintEnabled = $('#addrunesmint').is(':checked');

    if (tick && tick != '自定义文本') {
      // 自动刷新BRC20铭文信息（静默模式）
      fetchBRC20Info(tick, true); // 传入 true 表示自动刷新

      // 如果启用了符文铸造且有符文ID，也触发符文刷新
      if (isRunesMintEnabled && runeId && runeId.trim() !== '') {
        $('#refreshRune2').trigger('click');
      }
    } else {
      // 如果tick为空，停止定时器
      clearInterval(tickRefreshInterval);
      tickRefreshInterval = null;
    }
  }, 30000); // 30秒 = 30000毫秒
}

// 在 fetchBRC20Info 函数中添加详情链接的处理
$(document).on('click', '#tickDetails', function(e) {
    e.preventDefault();
    // 移除 trim()，保留可能的空格
    const tick = $('#tick').val().replace(/%20/g, ' ');
    if (tick) {
        const encodedTick = encodeURIComponent(tick.toLowerCase());
        window.open(`https://uniscan.cc/brc20/${encodedTick}`, '_blank');
    }
});

// 修改tick输入框的监听事件
$('#tick').on('change', function() {
    const tick = $(this).val().replace(/%20/g, ' ');
    $(this).val(tick);
    
    // 如果是自定义文本模式，不进行铭文信息查询
    if (isCustomTextMode || tick === '自定义文本') {
        if (tickRefreshInterval) {
            clearInterval(tickRefreshInterval);
            tickRefreshInterval = null;
        }
        $('#runeInfo').hide();
        // 清空持有人列表
        HoldersManager.showEmptyState();
        return;
    }
    
    if (tick) {
        // 计算字符串的实际显示长度
        function getStringDisplayLength(str) {
            let length = 0;
            // 将字符串拆分成单个字符
            for (let char of str) {
                const encoded = encodeURIComponent(char);
                if (encoded.includes('%')) {
                    // 如果编码后包含%，说明是非ASCII字符
                    length += Math.ceil(encoded.split('%').length - 1);
                } else {
                    // 如果编码后不包含%，说明是ASCII字符
                    length += 1;
                }
            }
            return length;
        }

        const displayLength = getStringDisplayLength(tick);
        let spacesNeeded = 0;

        // 所有类型的字符串都需要至少4个长度
        if (displayLength < 4) {
            spacesNeeded = 4 - displayLength;
        }

        if (spacesNeeded > 0) {
            // 添加空格
            const paddedTick = tick + ' '.repeat(spacesNeeded);
            // 更新输入框的值
            $(this).val(paddedTick);

            // 立即隐藏数量显示元素，提供即时视觉反馈
            $('.rune-quantity').hide();

            // 使用更新后的值调用API
            $('#runeInfoSpinner').show();
            fetchBRC20Info(paddedTick, false); // 用户手动修改视为手动查询
            startTickRefreshInterval();
            if (!isLoading) {
                saveParams(this);
            }
        } else {
            // 立即隐藏数量显示元素，提供即时视觉反馈
            $('.rune-quantity').hide();

            // 如果不需要补全，直接使用原值
            $('#runeInfoSpinner').show();
            fetchBRC20Info(tick, false); // 用户手动修改视为手动查询
            startTickRefreshInterval();
            if (!isLoading) {
                saveParams(this);
            }
        }
    } else {
        if (tickRefreshInterval) {
          clearInterval(tickRefreshInterval);
          tickRefreshInterval = null;
        }
        $('#runeInfo').hide();
        // 清空持有人列表
        HoldersManager.showEmptyState();
        updateURL("");
    }
});

// 在按下回车键时也触发检查
$('#tick').on('keypress', function(e) {
    if (e.which == 13) {  // 回车键的keyCode是13
        $(this).trigger('change');
    }
});

window.addEventListener('load', addCursorPointerStyle);


$('.rune-board-dropdown').on('click', 'a.rune-board-item', function(e) {
  e.stopPropagation();
  currentSelectedId = $(this).attr('id');
  $('#runeBoardDropdown').text($(this).text());
  $('#runeBoardDropdown').next('.dropdown-menu').hide();
  updateRuneData(currentSelectedId);
});


function mintRune(runeId) {
  if($('#tick').val() != runeId){
    // 立即隐藏数量显示元素，提供即时视觉反馈
    $('.rune-quantity').hide();

    // 立即显示持有人列表加载状态，清空旧数据
    HoldersManager.showLoadingState();

    // 如果当前处于自定义文本模式，自动切换到BRC20模式
    if (isCustomTextMode) {
      switchToBRC20Mode();
    }

    // 设置tick值并触发change事件
    $('#tick').val(runeId).trigger('change');

    // 更新 URL，但不刷新页面
    updateURL(runeId);
  }
}

$(document).ready(function() {

  $(document).on('click', '.rune-item', function(event) {
    if (!$(event.target).closest('.rune-card').length) {
      event.preventDefault();
      const runeId = $(this).data('tick');
      mintRune(runeId);
    }
  });

  $(document).on('click', '.mint-link', function(event) {
    event.preventDefault();
    const runeId = $(this).data('rune-id');
    mintRune(runeId);
  });

  $(document).on('click', '.rune-link', function(event) {
    const href = $(this).attr('href');
    /*
    if (href && href !== '#') {
      window.open(href, '_blank');
    }
    */
  });
});


async function updateRuneData(id = "hot") {
  try {
    if ($('.left-panel').is(':hidden')) {
      return;
    }
    const timestamp = new Date().getTime(); // 生成时间戳作为随机数
    const fetchTimeout = 30000;
    const Lowfee = $('#Lowfee').is(':checked');
    let send = {};
    const apiUrl = '/api/v1/mints_hot';
    send = {
      time: getSyncedTimestamp(),
      Lowfee: Lowfee
    };
    const encryptedData = en1999_20240924(JSON.stringify(send), API_ENCRYPTION_KEY);

    const response = await Promise.race([
      fetch(apiUrl + '?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    const data = await response.json();

    const container = document.getElementById('hotlist');
    container.innerHTML = ''; // 清空原有内容

    // 检查是否有数据
    if (parseInt(data.total) == 0 || !data.results || data.results.length === 0) {
      // 显示空状态消息，使用统一的CSS样式
      container.className = 'hotlist-empty-container';

      const emptyMessage = document.createElement('div');
      emptyMessage.className = 'empty-state-message';
      emptyMessage.textContent = '没有热门铭文';
      container.appendChild(emptyMessage);
      return;
    }

    // 重置容器样式（当有数据时）
    container.className = '';

    const runes = data.results;

    runes.forEach(rune => {
      const runeItem = document.createElement('div');
      runeItem.className = 'rune-item';
      runeItem.dataset.tick = rune.tick;

      const runeHeader = document.createElement('div');
      runeHeader.className = 'rune-header';

      const runeName = document.createElement('span');
      runeName.className = 'rune-name-text';
      runeName.textContent = rune.tick.replace(/ /g, '%20');

      runeHeader.appendChild(runeName);

      const progressContainer = document.createElement('div');
      progressContainer.className = 'progress-container';

      const progress = document.createElement('div');
      progress.className = 'progress multi-progress';
      
      const totalAmount = parseFloat(rune.supply);  //代币总量
      const mintsPercentage = parseFloat(parseFloat(rune.minted) / totalAmount * 100).toFixed(2);  //已铸造的进度
      const minted_leaf = Math.round(parseFloat(rune.minted) / parseFloat(rune.limitPerMint));   //已铸造张数
      const wait_leaf = Math.round(parseFloat(rune.total_amount) / parseFloat(rune.limitPerMint));   //内存池等待张数
      const pendingsPercentageOfTotalSupply = rune.total_amount ? ((parseFloat(rune.total_amount) / totalAmount) * 100).toFixed(2) : null;

      const progressBarGreen = document.createElement('div');
      progressBarGreen.className = 'progress-bar bg-green';
      progressBarGreen.role = 'progressbar';
      progressBarGreen.style.width = mintsPercentage ? mintsPercentage + '%' : '0%';
      progressBarGreen.setAttribute('aria-valuenow', mintsPercentage || '0');
      progressBarGreen.setAttribute('aria-valuemin', '0');
      progressBarGreen.setAttribute('aria-valuemax', '100');

      const progressBarRed = document.createElement('div');
      progressBarRed.className = 'progress-bar bg-red';
      progressBarRed.role = 'progressbar';
      progressBarRed.style.width = pendingsPercentageOfTotalSupply ? pendingsPercentageOfTotalSupply + '%' : '0%';
      progressBarRed.setAttribute('aria-valuenow', pendingsPercentageOfTotalSupply || '0');
      progressBarRed.setAttribute('aria-valuemin', '0');
      progressBarRed.setAttribute('aria-valuemax', '100');

      progress.appendChild(progressBarGreen);
      progress.appendChild(progressBarRed);

      const progressPercentageSpan = document.createElement('span');
      progressPercentageSpan.className = 'green-percentage';
      progressPercentageSpan.textContent = parseFloat(mintsPercentage).toFixed(2) + '%';

      progressContainer.appendChild(progress);
      progressContainer.appendChild(progressPercentageSpan);

      const runeDetails = document.createElement('div');
      runeDetails.className = 'rune-details';

      const runeDetailText = document.createElement('span');

      runeDetailText.innerHTML = `进度:${formatNumber(minted_leaf)}/${formatNumber(Math.round(totalAmount / parseFloat(rune.limitPerMint)))} <span class="spinner-border spinner-border-sm" role="status"${!rune.total_txs ? " style='display: none;'" : " style='margin-top: -3px; margin-left: 4px;'"}></span> <span class="rune-text"${!rune.total_txs ? " style='display: none;'" : ""}>${wait_leaf}张（${rune.unique_addresses}人）</span>`;

      runeDetails.appendChild(runeDetailText);

      runeItem.appendChild(runeHeader);
      runeItem.appendChild(progressContainer);
      runeItem.appendChild(runeDetails);

      runeItem.className = 'rune-item';


      // 获取视窗高度的一半作为判断标准
      const halfViewportHeight = window.innerHeight / 2;

      // 添加事件监听
      runeItem.onmouseenter = (event) => {
        const runeItemRect = event.currentTarget.getBoundingClientRect();
        const cardHeight = card.offsetHeight;

        // 检查元素的中心点是否位于视窗的下半部分
        if (runeItemRect.top + runeItemRect.height / 2 > halfViewportHeight) {
          // 如果在下半部分，设置卡片在元素上方显示
          card.style.bottom = (runeItemRect.height - 30) + 'px';  // 5px 是间隙
          card.style.top = 'auto';  // 清除之前可能设置的 top 值
        } else {
          // 如果在上半部分，设置卡片在元素下方显示
          card.style.top = (runeItemRect.height - 30) + 'px';  // 5px 是间隙
          card.style.bottom = 'auto';  // 清除之前可能设置的 bottom 值
        }

        card.style.display = 'block';  // 显示卡片
      };

      runeItem.onmouseleave = () => {
        card.style.display = 'none';  // 隐藏卡片
      };

      // 同时创建一个隐藏的卡片，仅在悬停时显示
      const card = document.createElement('div');
      card.className = 'rune-card hidden';
      card.innerHTML = `
        <div class="rune-body">
          <p>铭文: ${rune.tick}</p>
          <p>可铭刻: ${formatNumber(Math.round(parseFloat(rune.supply) / parseFloat(rune.limitPerMint)))}张（<a href="#" class="mint-link" data-rune-id="${rune.tick}">点击铸造</a>）</p>
          <p>已铸造: ${formatNumber(minted_leaf)}张 (${mintsPercentage ? parseFloat(mintsPercentage).toFixed(2) + '%' : '0%'})</p>
          <p>剩余数: ${formatNumber(Math.round((parseFloat(rune.supply) - parseFloat(rune.minted)) / parseFloat(rune.limitPerMint)))}张</p>
          ${rune.holders_total ? `<p>持有人: ${rune.holders_total}</p>` : ""}
          ${rune.unique_addresses ? `<p>内存池钱包: ${rune.unique_addresses}</p>` : ""}
          <p>内存池数量: ${formatNumber(wait_leaf)}张 (${rune.total_amount ? pendingsPercentageOfTotalSupply : "0"}%)</p>
          <p>
            <a href="https://uniscan.cc/brc20/${encodeURIComponent(rune.tick)}" target="_blank" class="rune-link" style="margin-right: 2px;"><img src="uniscan.svg" alt="uniscan"></a>
            <a href="https://www.okx.com/zh-hans/web3/marketplace/ordinals/token/${encodeURIComponent(rune.tick)}" target="_blank" class="rune-link" style="margin-right: 2px;"><img src="okx.ico" alt="okx"></a>
            <a href="https://unisat.io/market/brc20?tick=${encodeURIComponent(rune.tick)}" target="_blank" class="rune-link" style="margin-right: 2px;"><img src="unisat.ico" alt="unisat"></a>
            <a href="https://x.com/search?q=${encodeURIComponent(rune.tick)}" target="_blank" class="rune-link"><img src="tuite.ico" alt="unisat"></a>
          </p>
        </div>
      `;
      if(parseFloat(mintsPercentage) > 99.99){
        if(parseInt(rune.pendings) > 5){
          container.appendChild(runeItem);
          runeItem.appendChild(card);  // 卡片作为 runeItem 的子元素   
        }
      }else{
        container.appendChild(runeItem);
        runeItem.appendChild(card);  // 卡片作为 runeItem 的子元素    
      }
    });

  } catch (error) {
    console.error('获取API数据失败:', error);

    // 在错误情况下也显示友好的错误消息，使用统一的CSS样式
    const container = document.getElementById('hotlist');
    if (container) {
      container.innerHTML = '';
      container.className = 'hotlist-empty-container';

      const errorMessage = document.createElement('div');
      errorMessage.className = 'error-state-message';
      errorMessage.textContent = '加载铭文数据失败';
      container.appendChild(errorMessage);
    }
  }
}

// 切换到自定义文本模式的函数
function switchToCustomTextMode() {
    isCustomTextMode = true;

    // 更新按钮样式
    $('#ordinalstext').removeClass('button-off').addClass('button-on active');
    $('#brc20text').removeClass('button-on active').addClass('button-off');

    // 隐藏BRC20相关的标签和输入框
    $('label[for="tick"]').hide();
    $('#tick').hide();
    $('label[for="amount"]').hide();
    $('#amount').hide();

    // 隐藏铭文信息容器和内存池透视
    $('.inscription-info-container').hide();
    $('#runeInfo').hide();
    $('.rune-progress-container').hide();

    // 停止所有定时API调用
    if (tickRefreshInterval) {
      clearInterval(tickRefreshInterval);
      tickRefreshInterval = null;
    }

    // 销毁图表（如果存在）
    if (myChart) {
      myChart.destroy();
      myChart = null;
    }

    // 设置URL参数为inscribe
    updateURL("inscribe");

    // 调整textarea样式为多行模式
    $('#orditext').attr('rows', 6).attr('placeholder', '请输入您要铭刻的自定义文本内容\n例如:\n{"p":"block-20","op":"mint","tick":"block","amt":"1000","hash":"00000000000000000001e8e792531645104223de7b09d771926b3f9a47598b79"}');

    // 检查当前内容并设置tick
    const content = $('#orditext').val().toLowerCase();
    if (!content.includes('brc-20')) {
      // 如果不包含"brc-20"，设置tick为"自定义文本"
      $('#tick').val('自定义文本');
    }

    // 清空amount
    $('#amount').val('');

    // 更新警告提示
    updateWarningTip();

    // 清空持有人列表
    HoldersManager.showEmptyState();
  }

// 切换到BRC20模式的函数
function switchToBRC20Mode() {
  isCustomTextMode = false;

  // 更新按钮样式
  $('#brc20text').removeClass('button-off').addClass('button-on active');
  $('#ordinalstext').removeClass('button-on active').addClass('button-off');

  // 显示BRC20相关的标签和输入框
  $('label[for="tick"]').show();
  $('#tick').show();
  $('label[for="amount"]').show();
  $('#amount').show();
  $('.inscription-info-container').show();

  // 调整textarea样式为单行模式
  $('#orditext').attr('rows', 1).attr('placeholder', '');

  // 只有当内容是标准BRC20格式时才自动解析填充字段
  const orditext = $('#orditext').val().trim();
  if (orditext) {
    try {
      const parsed = JSON.parse(orditext);
      if (parsed.p === "brc-20" && parsed.tick && parsed.amt) {
        $('#tick').val(parsed.tick);
        $('#amount').val(parsed.amt);
        $('#tick').trigger('change');
      }
    } catch (e) {
      // 如果解析失败，不执行任何操作，保持BRC20模式
      console.log('当前内容不是有效的JSON格式');
    }
  }

  // 重置警告提示为默认状态
  updateWarningTip();
}

  // 添加BRC20和自定义文本模式切换功能
  $(document).ready(function() {
    
    setTimeout(() => {
      updateRuneData();
    }, 500);
    setInterval(() => updateRuneData(currentSelectedId), 30000);

    $('#brc20text').on('click', function() {
      // 切换到BRC20模式
      switchToBRC20Mode();

      // 保存参数
      if (!isLoading) {
        saveParams('brc20text');
      }
    });

    $('#ordinalstext').on('click', function() {
      // 切换到自定义文本模式
      switchToCustomTextMode();

      // 保存参数
      if (!isLoading) {
        saveParams('ordinalstext');
      }
    });
  });

// URL 管理功能
function updateURL(tick) {
  if (!tick) {
    // 如果没有 runeId，清除 URL 参数
    const url = new URL(window.location);
    url.searchParams.delete('tick');
    history.replaceState(null, '', url.toString());
    return;
  }

  const url = new URL(window.location);
  url.searchParams.set('tick', tick);
  
  // 使用 replaceState 避免在浏览器历史中创建太多条目
  history.replaceState({ tick }, '', url.toString());
}

// 持有人列表钱包地址点击事件处理
$(document).on('click', '#holdersTableBody .wallet-link', function(e) {
  e.preventDefault();
  const fullAddress = $(this).data('full-address');
  if (fullAddress) {
    const mempoolUrl = HoldersManager.getMempoolUrl(fullAddress);
    window.open(mempoolUrl, '_blank');
  }
});
<!DOCTYPE html>
<html>
<head>
  <title>Ybot.io - 比特币BRC20铭文批量铸造工具</title>
  <link href="bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="index.css?20250720003">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
  <nav class="navbar">
    <div class="navbar-container">
      <div class="navbar-left" style="display: flex; align-items: center; justify-content: flex-start;">
        <a href="https://ybot.io/" class="navbar-logo">Ybot.io</a>
        <button class="mobile-menu-toggle" id="mobileMenuToggle" style="display: none;">
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
          <span class="hamburger-line"></span>
        </button>
        <ul class="navbar-menu desktop-menu" style="margin-left: 20px;">
          <li class="navbar-item">
            <a href="#" class="navbar-link">
              <span class="material-icons">apps</span>
            </a>
          </li>
          <li class="navbar-item dropdown">
            <a href="https://ybot.io/" class="navbar-link">比特币 BTC <span class="material-icons">expand_more</span></a>
            <ul class="dropdown-menu" style="width: 250px;">
              <li><a href="https://ybot.io">符文批量铸造工具</a></li>
              <li><a href="https://brc20.ybot.io">BRC20批量铸造工具<span class="badge badge-hot">HOT</span></a></li>
              <li><a href="https://alkanes.ybot.io/">Alkanes批量铸造工具<span class="badge badge-new">NEW</span></a></li>
              <li><a href="https://t.me/ybot_runes" target="_blank">BTC热门铸造电报推送</a></li>
            </ul>
          </li>
          <li class="navbar-item dropdown">
            <a href="https://alkanes.ybot.io/" class="navbar-link">Alkanes 协议<span class="badge badge-new">NEW</span> <span class="material-icons">expand_more</span></a>
            <ul class="dropdown-menu" style="width: 250px;">
              <li><a href="https://alkanes.ybot.io/">Alkanes批量铸造工具<span class="badge badge-hot">HOT</span></a></li>
              <li><a href="https://alkanes.ybot.io/?runeid=2:21568">Alkanes打卡工具<span class="badge badge-new">NEW</span></a></li>
              <li><a href="https://t.me/ybot_runes" target="_blank">Alkanes热门铸造电报推送</a></li>
            </ul>
          </li>
          <li class="navbar-item dropdown">
            <a href="https://runes.ybot.io/" class="navbar-link">分形 Fractal <span class="material-icons">expand_more</span></a>
            <ul class="dropdown-menu" style="width: 250px;">
              <li><a href="https://runes.ybot.io/">符文批量铸造工具<span class="badge badge-new">NEW</span></a></li>
              <li><a href="https://ordinals.ybot.io/">BRC20批量铸造工具</a></li>
            </ul>
          </li>
          <li class="navbar-item dropdown">
            <a href="#" class="navbar-link">其他工具<span class="badge badge-new">NEW</span> <span class="material-icons">expand_more</span></a>
            <ul class="dropdown-menu" style="width: 250px;">
              <li><a href="https://chromewebstore.google.com/detail/bkjehbfgepikkfdhodlmadnceidllbpa?utm_source=item-share-cb" target="_blank">BTC地址标记插件</a><span class="badge badge-new">NEW</span></li>
              <li><a href="https://x.com/intent/follow?screen_name=ybot_io" target="_blank">Ybot官方推特</a></li>
            </ul>
          </li>
        </ul>
      </div>
      <div class="navbar-right" style="display: flex; align-items: center; justify-content: flex-end; margin-left: auto;">
        <a href="#" class="navbar-icon wallet-icon" style="display:none;">
          <span class="material-icons">account_balance_wallet</span>
        </a>
        <span style="margin-left: 5px; border-left: 1px solid #ccc; padding-left: 10px;">
          <div class="form-switch" id="darkModeSwitch">
            暗黑模式: <input class="form-check-input" type="checkbox" role="switch" style="margin-left: 0.5rem;" id="darkModeToggle">
          </div>
        </span>
      </div>
    </div>
  </nav>

  <!-- 移动端下拉菜单 -->
  <div class="mobile-menu" id="mobileMenu">
    <div class="mobile-menu-content">
      <div class="mobile-menu-section">
        <h6>比特币 BTC</h6>
        <a href="https://ybot.io" class="mobile-menu-link">符文批量铸造工具</a>
        <a href="https://brc20.ybot.io" class="mobile-menu-link">BRC20批量铸造工具 <span class="badge badge-hot">HOT</span></a>
        <a href="https://alkanes.ybot.io/" class="mobile-menu-link">Alkanes批量铸造工具 <span class="badge badge-new">NEW</span></a>
        <a href="https://t.me/ybot_runes" target="_blank" class="mobile-menu-link">BTC热门铸造电报推送</a>
      </div>
      <div class="mobile-menu-section">
        <h6>Alkanes 协议 <span class="badge badge-new">NEW</span></h6>
        <a href="https://alkanes.ybot.io/" class="mobile-menu-link">Alkanes批量铸造工具 <span class="badge badge-hot">HOT</span></a>
        <a href="https://alkanes.ybot.io/?runeid=2:21568" class="mobile-menu-link">Alkanes打卡工具 <span class="badge badge-new">NEW</span></a>
        <a href="https://t.me/ybot_runes" target="_blank" class="mobile-menu-link">Alkanes热门铸造电报推送</a>
      </div>
      <div class="mobile-menu-section">
        <h6>分形 Fractal</h6>
        <a href="https://runes.ybot.io/" class="mobile-menu-link">符文批量铸造工具 <span class="badge badge-new">NEW</span></a>
        <a href="https://ordinals.ybot.io/" class="mobile-menu-link">BRC20批量铸造工具</a>
      </div>
      <div class="mobile-menu-section">
        <h6>其他工具</h6>
        <a href="https://chromewebstore.google.com/detail/bkjehbfgepikkfdhodlmadnceidllbpa?utm_source=item-share-cb" target="_blank" class="mobile-menu-link">BTC地址标记插件 <span class="badge badge-new">NEW</span></a>
        <a href="https://x.com/intent/follow?screen_name=ybot_io" target="_blank" class="mobile-menu-link">Ybot官方推特</a>
      </div>
    </div>
  </div>

  <div class="container-fluid" style="margin-top: 65px;">
    <h4 class="text-center mb-3" style="margin-top: 10px;">比特币BTC - BRC20铭文铸造工具</h4>
    <div class="row">
      <div class="col-md-3 left-panel">
        <div class="bg-white p-3 mb-4 rounded shadow transaction-container">
          <div class="row mb-2 align-items-center">
            <div class="col-6 mt-3">
              <label class="form-label bold-label"><img id="hideBoard" src="hide.svg" alt="隐藏符文看板"> 铭文看板：</label>
            </div>
            <div class="col-6 text-end mt-3">
              <div class="dropdown" style="margin-top: -6px;">
                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="runeBoardDropdown" data-bs-toggle="dropdown" aria-expanded="false">当前热门铭刻</button>
                <ul class="dropdown-menu dropdown-menu-end rune-board-dropdown" aria-labelledby="runeBoardDropdown">
                  <li><a class="dropdown-item rune-board-item" href="#" id="hot">当前热门铭刻</a></li>
                </ul>
              </div>
            </div>
          </div>
            <div style="margin-top: 15px; margin-bottom: 15px;" id="hotlist"></div>
          </div>
        </div>

      <div class="col-md-6 left-area" style="position: relative;">
        <img id="showBoard" src="show.svg" alt="显示铭文看板">
        <div class="bg-white p-4 mb-4 rounded shadow transaction-container">
          <div class="mb-2">
            <div class="button-group-text">
              <button type="button" id="brc20text" class="button-on active">BRC20</button>
              <button type="button" id="ordinalstext" class="button-off">自定义文本</button>
            </div>
            <label for="tick" class="form-label bold-label">铭文名称：<span class="normal-label">(示例: ordi)</span></label>
            <input type="text" id="tick" class="form-control" placeholder="请输入铭文Tick名称">
            <div id="runeInfo" class="rune-info-box" style="display:none;">
                <span class="rune-name">Limit: <span id="limitPerMint"></span> <a href="#" id="tickDetails" class="tick-details">(详情)</a></span>
                <span class="rune-quantity">
                    进度: <span id="remainingSupply">0</span>/<span id="totalSupply">0</span>
                    (<span id="mintPercent">0%</span>)
                    [<span class="mint-status">加载中</span>]
                    <a href="#" id="refreshRune">(刷新)</a>
                </span>
                <div class="spinner-border spinner-border-sm text-primary ms-2" role="status" id="runeInfoSpinner" style="display: none;">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
          </div>

          <div class="mb-2">
              <label for="amount" class="form-label bold-label">代币数量：<span class="normal-label">(单张铸币数量，示例: 1000)</span></label></label>
              <input type="number" id="amount" class="form-control" placeholder="请输入铭文单张数量">
          </div>

          <div class="mb-2">
             <label for="orditext" class="form-label bold-label">铭刻内容：<span class="red-bold safetip">(请确认铸造的最终文本内容)</span></label>
             <textarea id="orditext" class="form-control" placeholder="" rows="1" style="resize: vertical;"></textarea>
          </div>

          <div class="inscription-info-container" style="display:none;">
            <div class="inscription-header-row d-flex justify-content-between align-items-center mb-1">
              <h6 class="inscription-title mb-0">加载中...</h6>
              <div class="social-icons">
                <a href="#" target="_blank" class="rune-link" title="uniscan">
                  <img src="uniscan.svg" alt="uniscan">
                </a>
                <a href="#" target="_blank" class="rune-link" title="OKX">
                  <img src="okx.ico" alt="okx">
                </a>
                <a href="#" target="_blank" class="rune-link" title="Unisat">
                  <img src="unisat.ico" alt="unisat">
                </a>
                <a href="#" target="_blank" class="rune-link" title="Twitter">
                  <img src="tuite.ico" alt="twitter">
                </a>
              </div>
            </div>

            <div class="row">
              <div class="col-lg-6">
                <div class="inscription-info-left">
                  <div class="d-flex justify-content-between align-items-center">
                    <div class="progress inscription-progress-bar flex-grow-1 multi-progress">
                      <div class="progress-bar bg-green" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                      <div class="progress-bar bg-red" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <span class="progress-percentage ms-2">　0%</span>
                  </div>
                  <p class="progress-text mb-1">进度: <span class="progress-numbers">加载中...</span>　<span class="spinner-border spinner-border-sm text-success" role="status" style="margin-top: -3px;" id="mempool-spinner"></span>
                  <span class="rune-text" id="memsum" style="">加载中...</span></p>

                  <div class="inscription-details-grid">
                    <div class="detail-item"><span>单次最大铸刻</span><strong>加载中...</strong></div>
                    <div class="detail-item"><span>总量</span><strong>加载中...</strong></div>
                    <div class="detail-item"><span>公开铸刻</span><strong class="text-muted">加载中...</strong></div>
                    <div class="detail-item"><span>部署者</span><a href="#" class="wallet-link" target="_blank">加载中...</a></div>
                    <div class="detail-item"><span>部署Hash</span><a href="#" class="wallet-link" target="_blank">加载中...</a></div>
                    <div class="detail-item"><span>持有人</span><strong>加载中...</strong></div>
                  </div>
                </div>
              </div>

              <div class="col-lg-6">
                <div class="holders-list">
                  <div class="table-responsive holders-table-container">
                    <table class="table table-borderless table-sm holders-table">
                      <thead>
                        <tr>
                          <th>Top</th>
                          <th>钱包地址</th>
                          <th class="text-end">余额</th>
                          <th class="text-end">可挂单</th>
                          <th class="text-end">占比</th>
                        </tr>
                      </thead>
                      <tbody id="holdersTableBody">
                        <tr id="holdersLoadingRow">
                          <td colspan="5" class="text-center">
                            <span class="spinner-border spinner-border-sm text-primary" role="status"></span>
                            <span class="ms-2">加载持有人数据中...</span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="rune-progress-container" style="display:none;">
            <div class="row" id="mem-left">
                <label class="form-label bold-label">内存池透视：</label>
                <label class="form-label">排队中位数费率：<span class="rune-text" id="gassize">加载中</span></label>
                <label class="form-label">下个区块打包：<span class="rune-text" id="pperchain">加载中</span></label>
                <label class="form-label" id="pperchain_fee_label" style="display: none;">过块最低费率：<span class="rune-text" id="pperchain_fee"></span> <span data-toggle="pperchaintip" data-target="#pperchaintip" class="tooltip-icon">?</span></label>
            </div>
            <div class="col" id="mem-right">
              <canvas id="myChart" width="250" height="100"></canvas>          
            </div>
          </div>

          <div class="mb-3">
            <label for="wif" class="form-label bold-label">矿工钱包WIF(私钥): <span data-toggle="wifTooltip" data-target="#wifTooltip" class="tooltip-icon">?</span>
              <span class="red-bold" id="safetip">重要提示：请务必使用不含其他资产的新钱包作为矿工钱包！</span></label>
            <div class="button-group-wallet">
              <button type="button" id="p2trButton" class="button-on active">P2TR (bc1p)</button>
              <button type="button" id="p2wpkhButton" class="button-off">P2WPKH (bc1q)</button>
            </div>
            <select id="addressType" class="form-select" style="display:none;">
              <option value="P2TR">P2TR (bc1p)</option>
              <option value="P2WPKH">P2WPKH (bc1q)</option>
            </select>
            <input type="password" id="wif" class="form-control" placeholder="请输入矿工钱包WIF私钥">
            <div id="walletInfo" class="mt-1">
              <span>矿工钱包: <a href="#" id="walletAddressLink"><span id="walletAddress">加载中...</span></a><img src="og.png" id="og"></span>
              <span id="walletBalanceInfo">余额: <span id="walletBalance"></span> BTC</span>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <div id="runesmint" class="form-switch">
                <input class="form-check-input" type="checkbox" id="addrunesmint">
                <label class="form-check-label" for="runesmint">同时铸造符文<span class="badge badge-new">NEW</span></label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-switch">
                <input class="form-check-input" type="checkbox" id="Lowfee">
                <label class="form-check-label" for="Lowfee">低费率模式<span id="showSave">(广播低于1sat的交易)</span><span class="badge badge-new">NEW</span></label>
              </div>
            </div>
          </div>

          <div class="mb-3" id="runesMintContainer" style="display: none;">
            <input type="text" id="runes" class="form-control" placeholder="请输入符文名称或ID" value="UNCOMMON•GOODS">
            <input type="text" id="mintHexData" style="display: none;" value="1:0">
            <div class="mb-2">
              <div id="runeInfo2" class="rune-info-box" style="display: flex;">
                <span class="rune-name2"><span class="hot-springs"></span>符文ID: <span id="runeId">加载中...</span><span class="runeshow"> 预留:<span class="rune-mint-text2">0%</span> (<a href="#" class="Runeinfo2" target="_blank">详情</a>)</span></span>
                <span><label class="rune-quantity2">进度: <span id="runeMintProgress">加载中...</span> <span class="rune-mint-text" id="state">[加载中]</span> <a href="#" id="refreshRune2">(刷新)</a></label></span>
                <div class="spinner-border spinner-border-sm text-primary ms-2" role="status" id="runeInfoSpinner2" style="display: none;">
                  <span class="visually-hidden">Loading...</span>
                </div>
              </div>
            </div>
          </div>

          <label for="feeRate" class="form-label bold-label">当前gas费:</label>
          <div class="fee-options">
            <button type="button" class="fee-option active" id="fastestFee">快速</button>
            <button type="button" class="fee-option" id="halfHourFee">平均</button>
            <button type="button" class="fee-option" id="hourFee">稍慢</button>
            <button type="button" class="fee-option" id="custom">自定义</button>
          </div>

          <div class="mb-3">
            <div class="row">
              <div class="col">
                <label for="feeRate" class="form-label bold-label">设置GAS费率：<span class="normal-label">(sat/vB)</span></label>
              </div>
              <div class="col">
                <label for="maxFee" class="form-label bold-label">预留最大费率：<span class="normal-label">(sat/vB)</span> <span  data-toggle="tooltip" data-target="#maxFeeTooltip" class="tooltip-icon">?</span>
                </label>              
              </div>
            </div>
            <div class="row">
              <div class="col">
                <input type="number" id="feeRate" class="form-control">
                <div id="feeRateError" class="error-message" style="display: none;"></div>
              </div>
              <div class="col">
                <input type="number" id="maxFee" class="form-control">
                <div id="maxFeeError" class="error-message" style="display: none;"></div>
              </div>
            </div>
          </div>


          <div class="mb-3">
            <label for="receiveAddress" class="form-label bold-label">铭文收货地址：<span class="normal-label">(不能与矿工钱包地址相同)</span></label>
            <div class="address-input-flex-container">
              <div class="button-group">
                <button type="button" id="buttonOn" class="button-on active" style="display:none;">单地址接收</button>
                <button type="button" id="buttonOff" class="button-off" style="display:none;">多地址接收</button>
              </div>
            </div>
            <input type="checkbox" id="addressModeToggle" style="display: none;">
            <input type="text" id="singleReceiveAddress" class="form-control mt-1" placeholder="请输入bc1q或bc1p开头钱包地址">
            <textarea id="receiveAddress" class="form-control mt-1" placeholder="请输入bc1q或bc1p开头钱包地址,一行一个" style="display: none;"></textarea>
          </div>

          <button id="checkMintable" class="btn btn-primary btn-sm">查询矿工钱包可Mint数量</button>
          <span id="SplitUtxo"><a href="#">矿工钱包UTXO拆分</a></span>
          <div id="mintableResult" class="hidden"></div>

          <div class="mb-3"  id="inscriptionSizeContainer" style="display: none;">
            <div class="row">
              <div class="col">
                <label for="inscriptionSize" class="form-label bold-label">铭文占用聪: <span class="normal-label">(bc1p最低330,bc1q最低294)</span></label>
              </div>
              <div class="col">
                <label for="protect" class="form-label bold-label">保护低于指定聪值的UTXO：</label>
              </div>
            </div>
            <div class="row">
              <div class="col">
                <input type="number" id="inscriptionSize" class="form-control" value="330">
              </div>
              <div class="col">
                <input type="number" id="protect" class="form-control" value="10000">
              </div>
            </div>
          </div>

          <div class="mb-2 row align-items-center">
            <div class="col-5">
              <div id="advancedSettingsContainer" class="form-switch mb-3">
                <input class="form-check-input" type="checkbox" id="advancedSettings">
                <label class="form-check-label" for="advancedSettings">高级设置</label>
              </div>
              <div id="RescueModeId" class="form-switch mb-2" style="display:none;">
                <input class="form-check-input" type="checkbox" id="RescueMode">
                <label class="form-check-label" for="RescueMode">救援模式<span id="showSave">(资金卡在中间钱包时用)</span></label>
              </div>
              <label for="count" class="form-label bold-label"><span id="mintmsg">铸造数量: </span><span data-toggle="mintTooltip" data-target="#mintTooltip" class="tooltip-icon">?</span></label>
              <div class="input-group">
                <input type="number" id="count" class="form-control" placeholder="请输入铸造数量" value="24">
                <button id="maxMintButton" class="btn btn-secondary">MAX</button>
              </div>
            </div>
            <div class="col-1 text-center">
              <div class="vr"></div>
            </div>
            <div class="col-6 text-end fees-info">
              <div class="fee-item">
                  <p>矿工费:</p>
                  <p><span id="totalFee">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
              <div class="fee-item">
                  <p>铭文占用:</p>
                  <p><span id="inscriptionCost">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
              <div class="fee-item">
                <p class='gray-text'>服务费:</p>
                <p><span id="serviceFee">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
              <div class="fee-item" id="ogFee" style="display: none;">
                <p class='gray-text'>OG折后费:</p>
                <p><span id="ogserviceFee">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
              <div class="fee-item">
                  <p>单张成本:</p>
                  <p><span id="unitCost">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
              <div class="fee-item">
                  <p><span id="mintcount">总共</span>:</p>
                  <p><span id="totalCost">0.00000000 BTC <span class="gray-text btc-amount">($0.00)</span></span></p>
              </div>
          </div>
          
          </div>
          <label class="form-label"><span class="red-bold">重要提示：矿工钱包不存放任何资产，否则可能被烧！</span></label>
          <div class="text-center">
            <button id="startMinting" class="btn btn-primary start-minting-btn">开始铸造</button>
          </div>

        </div>
      </div>
      <div class="col-md-3 right-area">
        <div class="bg-white p-4 mb-4 rounded shadow transaction-container">

          <div class="row mb-2 align-items-center">
            <div class="col-6">
              <label class="form-label bold-label">任务列表<span id="taskListTotalCount">(0)</span>：<span data-toggle="speeduptip" data-target="#speeduptip" class="tooltip-icon">?</span></label>
              <div class="dropdown">
                <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="runeFilterDropdown" data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false">
                  <span id="selectedRune">显示全部订单</span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="runeFilterDropdown">
                  <li><a class="dropdown-item" href="#">显示全部订单</a></li>
                </ul>
              </div>
            </div>
            <div class="col-6 text-end">
              <button type="button" id="batchAccelerateButton" class="btn btn-sm btn-primary custom-size">一键加速</button>
              <button type="button" id="batchDeleteButton" class="btn btn-sm btn-danger custom-size" style="display: none;">一键删除</button>
            </div>
          </div>
          <hr style="border-top: 1px solid #ccc; margin: 8px 0;">
          <div class="row mb-2">
            <div class="col-8">
              <button type="button" id="pendingButton" class="button-on active">
                <span class="short-text">未确认</span>
                <span class="long-text">未确交易</span>
              </button>
              <button type="button" id="confirmedButton" class="button-off">
                <span class="short-text">已确认</span>
                <span class="long-text">已确认(7天内)</span>
              </button>
            </div>
            <div class="col-4 text-end d-flex align-items-center justify-content-end">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selectAllCheckbox">
                <label class="form-check-label ms-0" for="selectAllCheckbox">全选<span id="selectedCountDisplay">(0)</span></label>
              </div>
            </div>
          </div>
          
          <div id="mintingList" class="transaction-list">
            <div id="noTransactionsMessage" class="text-center mt-3">没有未确认交易</div>
          </div>

          <div id="confirmedListContainer" style="display: none;">
            <div id="confirmedList" class="transaction-list">
              <div id="noconfirmedMessage" class="text-center mt-3">没有已确认交易</div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="alertModal" tabindex="-1" role="dialog" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="alertModalLabel">提示</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" id="alertModalBody" style="white-space: pre-line; word-wrap: break-word;"></div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="accelerateFeeModal" tabindex="-1" aria-labelledby="accelerateFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="accelerateFeeModalLabel">请输入要替换的目标费率：</h5>
          <button type="button" class="btn-close" id="closeAccelerateFeeModal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <input type="number" class="form-control" id="newFeeRateInput" placeholder="输入要替换成多少费率 (sat/vB)">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancelNewFeeRate">取消</button>
          <button type="button" class="btn btn-primary" id="confirmNewFeeRate">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="batchAccelerateFeeModal" tabindex="-1" aria-labelledby="batchAccelerateFeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="batchAccelerateFeeModalLabel">请输入要替换的目标费率：</h5>
          <button type="button" class="btn-close" id="closebatchAccelerateFee" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <input type="number" class="form-control" id="newBatchFeeRateInput" placeholder="请输入要替换成多费率 (sat/vB)">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelNewBatchFeeRate">取消</button>
          <button type="button" class="btn btn-primary" id="confirmNewBatchFeeRate">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="SplitUtxoModal" tabindex="-1" aria-labelledby="SplitUtxoModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="SplitUtxoModalLabel">UTXO拆分或合并</h5>
          <button type="button" class="btn-close" id="closeSplitUtxo" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p style="margin-bottom: 35px;"><strong>选择要拆分的UTXO：</strong><span class="red-bold">请确保该钱包没有铭文等其他资产，防止拆分后被燃烧！</span></p>
          <div id="mintDetailsContainer">
            <table class="table">
              <thead>
                <tr>
                  <th>UTXO编号</th>
                  <th>UTXO大小</th>
                  <th><input type="checkbox" id="selectAllUtxos" class="form-check-input"></th>
                </tr>
              </thead>
              <tbody id="utxoDetails"></tbody>
            </table>
          </div>
          <div class="mt-1">
            <p id="selectedUtxoInfo">已选: 0 UTXO - 0 BTC ($0)</p>
          </div>
          <div class="mt-3">
            <p><strong>拆成几份 (UTXO)：</strong></p>
            <div class="split-input-container">
                <input type="number" class="form-control split-count-input" id="splitCount" value="1" min="1" max="100">
                <div class="slider-container">
                    <div class="slider-border">
                        <input type="range" class="form-range" id="splitSlider" min="1" max="100" value="1">
                    </div>
                </div>
            </div>
          </div>
            
          <div class="mt-3">
            <p><strong>每份UTXO大小：</strong></p>
            <p id="satsPerSplit">请先选择要拆合的UTXO</p>
          </div>
          <div class="mt-3">
            <p><strong>交易费率：</strong></p>
            <div class="fee-options">
              <button type="button" class="fee-option-utxo active" id="fastestFee_utxo" data="fastestFee">快速<br>(6 sat/vB)</button>
              <button type="button" class="fee-option-utxo" id="halfHourFee_utxo" data="halfHourFee">平均<br>(6 sat/vB)</button>
              <button type="button" class="fee-option-utxo" id="hourFee_utxo" data="hourFee">稍慢<br>(6 sat/vB)</button>
              <button type="button" class="fee-option-utxo" id="custom_utxo" data="custom">
                定义
                <div style="display: flex; align-items: center; margin-top: 5px;">
                  <input type="number" class="form-control" id="custom_utxo_value" value="">
                </div>
              </button>          
            </div>
          </div>

          <div class="mt-1">
            <p style="text-align: right;">免服务费</p>
            <p id="minerFee" style="text-align: right;">矿工费: 0 BTC ($0)</p>
          </div>          
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancelSplitUtxo" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="confirmSplitUtxo">确认拆分</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="confirmMintModal" tabindex="-1" aria-labelledby="confirmMintModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmMintModalLabel">确认铸造</h5>
          <button type="button" class="btn-close" id="closeconfirmMint" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p><strong>确定要铸造下铭文和数量吗？</strong></p>
          <p>铭刻内容：<span id="confirmOrditext"></span></p>
          <p>铭刻数量：<span id="confirmMintCount"></span></p>
          <p id="confirmRunestext_show">铸造符文：<span id="confirmRunestext"></span></p>
          <p id="confirmRunesMintCount_show">铸造数量：<span id="confirmRunesMintCount"></span></p>
          <p>矿工费用：<span id="confirmMintFee"></span></p>
          <p>花费金额：<span id="confirmTotalCost"></span></p>
          <p><strong>铸造所涉UTXO信息：</strong></p>
        </div>
        <div id="mintDetailsContainer">
          <table class="table">
            <thead>
              <tr>
                <th>编号</th>
                <th>UTXO大小</th>
                <th>铸造数量</th>
              </tr>
            </thead>
            <tbody id="mintDetails"></tbody>
          </table>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancelconfirmMint" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="confirmMintButton">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmDeleteModalLabel">确认删除</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" id="closeDeleteButton" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>您确定要删除选中的已确认订单吗？</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancelDeleteButton" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteButton">确认删除</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="confirmBatchAccelerateModal" tabindex="-1" aria-labelledby="confirmBatchAccelerateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmBatchAccelerateModalLabel">批量加速确认</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" id="closeBatchAccelerate" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <p>您选择了不同铭文的订单进行批量加速,是否继续?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" id="cancelBatchAccelerate"  data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="confirmBatchAccelerate">确认</button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="waitingModal" tabindex="-1" role="dialog" aria-labelledby="waitingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="waitingModalLabel">请稍候...</h5>
        </div>
        <div class="modal-body">
          <p>订单正在提交中,请勿退出窗口！</p>
          <div class="progress">
            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="refreshToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="toast-header">
        <strong class="me-auto">提示</strong>
        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        正在刷新可铸造数量,请稍候...
      </div>
    </div>
  </div>

  <div id="maxFeeTooltip" style="display: none;">
    <p>预留最高Gas所需聪的作用如下：</p>
    <ol>
      <li>目的：保护小UTXO，确保在要加速时有足够的聪，避免因聪不足而无法加速</li>
      <li>示例：
        <ul>
          <li>当前gas为30，一个聪值较小的UTXO可以铸造24张。</li>
          <li>若真的铸造了24张,后续想要加速到150gas时,UTXO聪不够加速。</li>
          <li>设置预留150gas后,该UTXO可能只会安排铸造20张,确保后续有足够聪加速到150gas。</li>
        </ul>
      </li>
      <li>如果你的UTXO较大，该设置不会影响该UTXO一次铸造的数量。只有当UTXO变小时，预留聪设置才会起作用。</li>
    </ol>
    <p>总结：预留功能通过提前预留足够的聪，保护小UTXO，确保每次操作能顺利进行并方便后续加速。同时，通过修改这个参数，可以了解到你当前钱包余额在适合在什么样的最高费用下，能安全的铸造多少张。</p>
  </div>

  <div id="pperchaintip" style="display: none;">
    <p>通过内存池，预测了下一个区块打包的铸造交易数量及最低费率。</p>
  </div>
  
  <div id="wifTooltip" style="display: none;">
    <p>为什么需要输入钱包WIF私钥：</p>
    <p>因为我们使用订单无限加速技术，每次铸造都需要进行签名广播。如果通过钱包弹出签名，铸造100张就需要弹出100次钱包签名，极大地影响效率。</p>
    <p>关于WIF的使用：为了加快上链速度，Ybot采用服务端构建交易，因此需要使用您的WIF私钥进行签名，建议采用全新独立的矿工钱包。</p>
    <p>关于安全性：Ybot承诺不会保存任何用户的私钥，但也恳请使用全新独立的矿工钱包，后续我们将推出更安全的免私钥铸造模式。</p>
  </div>

  <div id="mintTooltip" style="display: none;">
    <p>每个UTXO在同一个区块内最多只能只能铸造24张，这是因为比特币交易的特性所致。</p>
    <p>要在一个区块内发送更多交易，您可以将使用"矿工钱包UTXO拆分"功能，将原本较大的UTXO拆分成多个UTXO，例如拆分成10个UTXO，这样您在一个区块内最多可以铸造240张。</p>
    <p>小Y温馨提醒：不建议拆分太小的UTXO，以免影响后续订单加速。不足可能导加速失败。</p>
  </div>

  <div id="speeduptip" style="display: none;">
    <p>加速交易的成本计算方式如下：</p>
    <ol>
      <li>以不同的gas费率铸造铭文的成本示例：</li>
      <li>示例：
        <ul>
          <li>以20 gas费率铸造24张铭文的成本为45美元。</li>
          <li>以30 gas费率铸造24张铭文的成本为60美元。</li>
        </ul>
      </li>
      <li>假设张三最初以20 gas费率铸造了24张铭文，花费了45美元。随后，他决定将交易提速到30 gas费率，因此需要补充15美元的矿工费，使总成本达到60美元。这表明，加速到30 gas费率的总费用与一开始直接以30 gas费率上链铸造的成本是相同的。</li>
    </ol>
    <p>结论：加速交易不会额外增加消耗，只是补足差额的矿工费来进行替换交易。</p>
  </div>

  <script src="env.js?20241130"></script>
  <script src="jquery-3.6.0.min.js"></script>
  <script src="popper.min.js"></script>
  <script src="bootstrap.min.js"></script>
  <script src="c.js"></script>
  <script src="p.js"></script>
  <script src="u.js?20241130"></script>
  <script src="index.js?2025072101"></script>
</body>
</html>
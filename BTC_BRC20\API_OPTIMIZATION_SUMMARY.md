# BRC20 API 调用优化总结

## 优化目标
解决 `/api/v1/getRuneInfo` API 因网络波动导致的误判问题，区分用户手动查询和系统自动刷新的处理策略。

## 问题描述
当前 API 有时会因为网络波动或后端索引更新，返回空白数据：
```json
{
    "code": 0,
    "msg": "success", 
    "data": {
        "limitPerMint": "0",
        "supply": "0", 
        "minted": "0",
        "selfMint": true
    }
}
```
这会误导用户认为铭文不存在，但实际上铭文是存在的。

## 解决方案

### 1. 函数签名修改
修改 `fetchBRC20Info` 函数，添加 `isAutoRefresh` 参数：
```javascript
// 修改前
async function fetchBRC20Info(tick)

// 修改后  
async function fetchBRC20Info(tick, isAutoRefresh = false)
```

### 2. 错误处理策略区分

#### 手动查询 (isAutoRefresh = false)
- **触发场景**：用户手动修改 tick 输入框、点击刷新按钮、页面初始加载
- **处理策略**：显示友好提示 "无法读取铭文信息，可能是网络延迟或铭文不存在，请稍后重试"
- **用户体验**：提供明确反馈，引导用户重试

#### 自动刷新 (isAutoRefresh = true)  
- **触发场景**：系统每30秒自动刷新
- **处理策略**：静默忽略错误，不显示任何提示，保持当前显示状态
- **用户体验**：不打断用户的挂机等待体验，等待下一轮刷新

### 3. 代码修改点

#### 3.1 函数调用修改
```javascript
// 手动查询调用
fetchBRC20Info(tick, false);

// 自动刷新调用  
fetchBRC20Info(tick, true);
```

#### 3.2 错误处理逻辑修改
在三个错误处理位置都添加了区分逻辑：
1. 空白数据检测 (limitPerMint === 0 || supply === 0)
2. API 响应异常 (response.ok === false)
3. 网络异常 (catch 块)

#### 3.3 自动刷新机制优化
修改 `startTickRefreshInterval` 函数，直接调用 `fetchBRC20Info(tick, true)` 而不是触发按钮点击事件，避免混淆调用来源。

## 实现效果

### 用户手动查询时
- 遇到空白数据：显示 "无法读取铭文信息，可能是网络延迟或铭文不存在，请稍后重试"
- 隐藏铭文信息容器和相关UI元素
- 提供明确的用户反馈

### 自动刷新时
- 遇到空白数据：静默忽略，在控制台记录日志
- 保持当前显示状态不变
- 不打断用户体验，等待下一轮30秒刷新

## 技术细节

### 修改的文件
- `public/index.js`

### 修改的函数
- `fetchBRC20Info(tick, isAutoRefresh = false)`
- `startTickRefreshInterval()`

### 调用点统计
总共修改了6个调用点：
1. 页面初始加载：`fetchBRC20Info(params.tick, false)`
2. 手动刷新按钮：`fetchBRC20Info(tick, false)`  
3. 自动刷新定时器：`fetchBRC20Info(tick, true)`
4. 用户修改tick（补全）：`fetchBRC20Info(paddedTick, false)`
5. 用户修改tick（直接）：`fetchBRC20Info(tick, false)`

## 测试建议

### 手动测试场景
1. 输入一个不存在的铭文名称，验证显示友好提示
2. 输入一个存在的铭文名称，等待30秒自动刷新，观察是否静默处理
3. 在网络不稳定环境下测试两种场景的表现

### 预期结果
- 手动查询失败时有明确提示
- 自动刷新失败时静默处理，不影响用户体验
- 保持30秒自动刷新机制正常工作

const sharp = require('sharp');
const { createCanvas, loadImage } = require('canvas');
const { IMAGE_CONFIG } = require('../config');

/**
 * RGB 转 HSL
 * @param {number} r - 红色 (0-1)
 * @param {number} g - 绿色 (0-1)
 * @param {number} b - 蓝色 (0-1)
 * @returns {Array<number>} [h, s, l]
 */
function rgbToHsl(r, g, b) {
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  const l = (max + min) / 2;

  if (diff === 0) {
    return [0, 0, l];
  }

  const s = l > 0.5 ? diff / (2 - max - min) : diff / (max + min);
  
  let h;
  if (max === r) {
    h = 60 * (((g - b) / diff) % 6);
  } else if (max === g) {
    h = 60 * (((b - r) / diff) + 2);
  } else {
    h = 60 * (((r - g) / diff) + 4);
  }

  if (h < 0) h += 360;
  return [h, s, l];
}

/**
 * HSL 转 RGB
 * @param {number} h - 色调 (0-360)
 * @param {number} s - 饱和度 (0-1)
 * @param {number} l - 亮度 (0-1)
 * @returns {Array<number>} [r, g, b] (0-1)
 */
function hslToRgb(h, s, l) {
  if (s === 0) {
    return [l, l, l];
  }

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;

  let rPrime, gPrime, bPrime;
  if (h < 60) {
    [rPrime, gPrime, bPrime] = [c, x, 0];
  } else if (h < 120) {
    [rPrime, gPrime, bPrime] = [x, c, 0];
  } else if (h < 180) {
    [rPrime, gPrime, bPrime] = [0, c, x];
  } else if (h < 240) {
    [rPrime, gPrime, bPrime] = [0, x, c];
  } else if (h < 300) {
    [rPrime, gPrime, bPrime] = [x, 0, c];
  } else {
    [rPrime, gPrime, bPrime] = [c, 0, x];
  }

  return [rPrime + m, gPrime + m, bPrime + m];
}

/**
 * 应用色调偏移
 * @param {Buffer} imageBuffer - 图像缓冲区
 * @param {number} hueShift - 色调偏移 (0-360)
 * @returns {Promise<Buffer>} 处理后的图像缓冲区
 */
async function applyHueShift(imageBuffer, hueShift) {
  try {
    const { data, info } = await sharp(imageBuffer)
      .raw()
      .ensureAlpha()
      .toBuffer({ resolveWithObject: true });

    const { width, height, channels } = info;
    const newData = Buffer.from(data);

    for (let i = 0; i < data.length; i += channels) {
      const r = data[i] / 255;
      const g = data[i + 1] / 255;
      const b = data[i + 2] / 255;
      const a = data[i + 3];

      // 跳过透明像素和接近白色的像素
      if (a === 0 || (r > 0.94 && g > 0.94 && b > 0.94)) {
        continue;
      }

      const [h, s, l] = rgbToHsl(r, g, b);
      const newH = (hueShift + h) % 360;
      const newS = Math.max(s, IMAGE_CONFIG.MIN_SATURATION);

      const [newR, newG, newB] = hslToRgb(newH, newS, l);

      newData[i] = Math.round(newR * 255);
      newData[i + 1] = Math.round(newG * 255);
      newData[i + 2] = Math.round(newB * 255);
    }

    return await sharp(newData, {
      raw: {
        width,
        height,
        channels
      }
    }).png().toBuffer();
  } catch (error) {
    throw new Error(`色调偏移失败: ${error.message}`);
  }
}

/**
 * 合成图像
 * @param {Buffer} baseImageBuffer - 基础图像缓冲区
 * @param {Buffer} accessoryBuffer - 配件图像缓冲区
 * @returns {Promise<Buffer>} 合成后的图像缓冲区
 */
async function compositeImages(baseImageBuffer, accessoryBuffer) {
  try {
    // 获取基础图像信息
    const baseInfo = await sharp(baseImageBuffer).metadata();
    
    // 调整配件图像大小以匹配基础图像
    const resizedAccessory = await sharp(accessoryBuffer)
      .resize(baseInfo.width, baseInfo.height, {
        fit: 'fill'
      })
      .toBuffer();

    // 合成图像
    const result = await sharp(baseImageBuffer)
      .composite([{
        input: resizedAccessory,
        blend: 'over'
      }])
      .png()
      .toBuffer();

    return result;
  } catch (error) {
    throw new Error(`图像合成失败: ${error.message}`);
  }
}

/**
 * 编码为 WebP 格式
 * @param {Buffer} imageBuffer - 图像缓冲区
 * @returns {Promise<Buffer>} WebP 格式的图像缓冲区
 */
async function encodeToWebP(imageBuffer) {
  try {
    const webpBuffer = await sharp(imageBuffer)
      .webp({
        quality: IMAGE_CONFIG.WEBP_QUALITY,
        effort: 6,
        lossless: false
      })
      .toBuffer();

    return webpBuffer;
  } catch (error) {
    throw new Error(`WebP 编码失败: ${error.message}`);
  }
}

/**
 * 从十六进制字符串加载图像
 * @param {string} hexString - 十六进制图像数据
 * @returns {Promise<Buffer>} 图像缓冲区
 */
async function loadImageFromHex(hexString) {
  try {
    const buffer = Buffer.from(hexString, 'hex');
    
    // 验证是否为有效图像
    const metadata = await sharp(buffer).metadata();
    if (!metadata.width || !metadata.height) {
      throw new Error('Invalid image data');
    }
    
    return buffer;
  } catch (error) {
    throw new Error(`加载图像失败: ${error.message}`);
  }
}

module.exports = {
  rgbToHsl,
  hslToRgb,
  applyHueShift,
  compositeImages,
  encodeToWebP,
  loadImageFromHex
};

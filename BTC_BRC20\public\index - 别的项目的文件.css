/* ========================================
   1. 基础样式 (Base Styles)
   ======================================== */

/* 全局样式 */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 15px;
  font-size: 14px;
  background-color: #f8f9fa;
}

/* 基础元素样式 */
#inscriptionSize {
  width: 100%;
}

.switch-container {
  align-items: center;
  margin-bottom: 5px;
}

#addressModeLabel {
  margin-left: 5px;
}

.price-text {
  color: gray;
  font-weight: bold;
}

/* ========================================
   2. 布局样式 (Layout Styles)
   ======================================== */

/* 容器布局 */
.container {
  display: flex;
}

.left-area { 
  flex: 6;
  max-width: 700px;
}

.left-panel {
  flex: 0 0 25%;
  max-width: 350px;
}

.middle-panel {
  flex: 0 0 50%;
  max-width: 50%;
}

.right-area {
  flex: 0 0 25%;
  max-width: 400px;
}

.row {
  display: flex;
  justify-content: center;
}

/* ========================================
   3. 按钮组样式 (Button Group Styles)
   ======================================== */

/* 按钮组容器 */
.button-group,
.button-group-wallet {
  display: flex;
  text-align: left;
  margin-bottom: 10px;
}

.button-group {
  max-width: 200px;
}

.button-group-wallet {
  max-width: 250px;
}

/* 按钮样式 */
.button-on, 
.button-off {
  flex-grow: 1;
  margin-right: 0;
  background-color: #bcbaba;
  color: #ffffff;
  border: 1px solid #bcbaba;
  padding: 5px 10px;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s, color 0.2s;
}

.button-on:not(:last-child) {
  border-right: none;
}

.button-on.active, 
.button-off.active {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

/* ========================================
   4. 表单和输入框样式 (Form & Input Styles)
   ======================================== */

/* 表单开关 */
.form-switch {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.form-switch .form-check-input {
  width: 2.5em;
  height: 1.2em;
  cursor: pointer;
  position: relative;
  top: -2.5px;
}

.form-switch .form-check-label {
  margin-left: 0.5em;
  line-height: 1.2em;
  cursor: default;
}

/* 输入框样式 */
#receiveAddress {
  height: 200px;
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group input {
  flex-grow: 1;
  margin-right: 5px;
}

.input-group button {
  font-size: 1rem;
  padding: 5px 12px;
}

#switchesContainer {
  display: flex;
  justify-content: space-between;
}

/* 标签样式 */
.red-bold {
  color: rgb(222, 34, 34);
  font-size: 13px;
  font-weight: bold;
}

.bold-label {
  font-weight: bold;
}

.normal-label {
  font-weight: normal;
  font-size: 13px;
}

#walletInfo p {
  margin-top: 10px;
}

/* ========================================
   5. 交易相关样式 (Transaction Styles)
   ======================================== */

/* 交易列表 */
.transaction-list {
  max-height: 400px;
  overflow-y: auto;
}

.transaction-container {
  height: 100%;
}

/* UTXO 组样式 */
.utxo-group {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  background-color: #fff;
  position: relative;
}

.utxo-group p {
  margin-bottom: 5px;
}

.utxo-group .close-btn,
.utxo-group .accelerate-btn {
  margin-top: 10px;
}

.utxo-group .close-btn {
  float: right;
}

/* 交易操作 */
.transaction-actions {
  display: flex;
  align-items: center;
}

.transaction-actions .accelerate-btn {
  margin-right: 10px;
}

.transaction-actions .order-rate {
  margin-right: auto;
  position: relative;
  top: 5px;
}

.transaction-actions .close-btn {
  margin-left: 10px;
}

.tx-hash {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 复选框容器 */
.checkbox-container {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 2;
}

.form-check {
  position: relative;
  top: 3px;
}

/* ========================================
   6. 费用相关样式 (Fee Styles)
   ======================================== */

/* 费用信息 */
.fees-info {
  padding-left: 10px;
}

.fees-info p {
  margin-bottom: 4.5px;
}

.vr {
  width: 1px;
  background-color: #ccc;
  height: 120px;
  margin: 0 20px;
}

/* 费用选项 */
.fee-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 10px;
}

.fee-option, 
.fee-option-utxo {
  flex: 1;
  padding: 6px 8px;
  background-color: #f8f9fa;
  border: 2px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1.2;
  font-size: 16px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.fee-option:hover, 
.fee-option-utxo:hover {
  background-color: #e9ecef;
}

.fee-option.active, 
.fee-option-utxo.active {
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-item span {
  flex-shrink: 0;
}

/* ========================================
   7. 符文相关样式 (Rune Styles)
   ======================================== */

/* 符文信息框 */
.rune-info-box {
  border: 1px solid #dee2e6;
  padding: 10px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  box-sizing: border-box;
  margin-top: -1px;
}

.rune-name {
  display: flex;
  align-items: center;
}

.rune-name .label {
  margin-right: 5px;
}

.rune-quantity {
  text-align: right;
}

/* 符文链接样式 */
#refreshRune {
  color: #007bff;
  text-decoration: none;
}

#refreshRune:hover {
  text-decoration: underline;
  cursor: pointer;
}

.tRune {
  color: #007bff;
  text-decoration: none;
}

.Runeinfo {
  color: #007bff;
  text-decoration: none;
}

.Runeinfo:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* 符文文本样式 */
.rune-name-text {
  color: rgb(222, 34, 34);
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.rune-red-text {
  color: rgb(222, 34, 34);
  font-weight: bold;
}

.rune-mint-text {
  color: rgb(6, 145, 57);
}

.rune-text {
  color: rgb(6, 145, 57);
  font-weight: bold;
}

/* 符文进度容器 */
.rune-progress-container {
  border: 1px solid #dee2e6;
  padding: 5px;
  margin-bottom: 10px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

#myChart {
  height: 100px;
  width: 250px;
  max-width: 100%;
  display: block;
}

/* 内存池透视布局 */
#mem-left {
  width: 60%;
}

#mem-right {
  width: 40%;
}

/* 热门符文样式 */
.hot-springs {
  color: #ff0000;
  font-size: 16px;
  font-weight: normal;
  margin-right: 4px;
}

.hot-runeid {
  color: #4c4b4b;
  font-size: 14px;
  font-weight: normal;
  margin-right: 4px;
}

.mintuser {
  font-size: 14px;
  font-weight: normal;
  margin-top: 0px;
  margin-right: -1px;
}

/* 符文过滤器 */
#runeFilterDropdown {
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 开始挖矿按钮 */
.start-minting-btn {
  width: 100%;
  height: 40px;
  font-size: 18px;
  margin-top: 20px;
}

/* 检查可挖矿性 */
#checkMintable {
  margin-bottom: 10px;
}

#mintableResult {
  margin-top: 10px;
}

#mintableResult.hidden {
  margin-top: 0;
}

/* 分割 UTXO */
#SplitUtxo {
  margin-bottom: 10px;
  float: right;
}

#SplitUtxo a {
  color: #007bff;
  text-decoration: none;
}

.SplitUtxo a:hover {
  text-decoration: underline;
  cursor: pointer;
}

/* ========================================
   8. 左侧面板符文列表样式 (Left Panel Rune List)
   ======================================== */

/* 符文项样式 */
.left-panel .rune-item {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 0;
  border-radius: 0;
  background-color: #fff;
}

.left-panel .rune-item + .rune-item {
  border-top: none;
}

.left-panel .rune-item:hover {
  background-color: #f8f9fa;
  cursor: pointer;
}

/* 符文项头部样式 */
.left-panel .rune-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.left-panel .rune-header .hot-springs {
  color: #ff0000;
  font-size: 15px;
  margin-right: 4px;
}

.left-panel .rune-header .rune-name-text {
  font-weight: bold;
  font-size: 14px;
  color: #494e4e;
}

/* 进度条容器 */
.left-panel .progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  width: 100%;
}

/* 进度条样式 */
.left-panel .progress {
  height: 10px;
  width: 60%;
  margin-right: 10px;
}

.left-panel .progress-bar {
  text-align: center;
  white-space: nowrap;
  color: #fff;
  padding: 0;
}

.multi-progress {
  display: flex;
}

.left-panel .rune-details {
  font-size: 13px;
  color: #2d2b2b;
}

.left-panel .green-percentage {
  color: #6c757d;
  font-weight: bold;
  margin-left: 10px;
}

/* 热门列表 */
#hotlist {
  height: 915px;
  max-height: 915px;
  overflow-y: auto;
  overflow-x: hidden;
}

#hotlist::-webkit-scrollbar {
  width: 6px;
}

#hotlist::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 10px;
}

#hotlist::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f0f0f0;
}

/* ========================================
   9. 模态框和弹窗样式 (Modal Styles)
   ======================================== */

/* 模态框头部 */
.modal-header .close {
  color: #000;
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  opacity: .5;
  padding: 0;
  background-color: transparent;
  border: 0;
  appearance: none;
  -webkit-appearance: none;
}

/* 分割 UTXO 模态框 */
#SplitUtxoModal .modal-dialog {
  max-width: 600px;
  width: 98%;
}

#confirmMintModal .modal-dialog {
  max-width: 600px;
  width: 98%;
}

#splitCount {
  max-width: 150px;
  text-align: center;
}

#custom_utxo_value {
  height: 25px;
  width: 100px;
  margin-right: 5px;
  text-align: center;
}

/* 警告模态框 */
#alertModalBody {
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.5;
  padding: 10px;
}

/* ========================================
   10. 滚动条样式 (Scrollbar Styles)
   ======================================== */

/* 挖矿列表滚动条 */
#mintingList {
  max-height: 850px;
  overflow-y: auto;
  overflow-x: hidden;
}

#mintingList::-webkit-scrollbar {
  width: 6px;
}

#mintingList::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 10px;
}

#mintingList::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f0f0f0;
}

/* 确认列表 */
#confirmedList {
  max-height: 850px;
  overflow-y: auto;
}

#confirmedList .transaction-item {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  background-color: #fff;
  position: relative;
}

#confirmedList .transaction-item p {
  margin-bottom: 5px;
}

/* ========================================
   11. 钱包和链接样式 (Wallet & Link Styles)
   ======================================== */

/* 钱包信息 */
#walletInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#walletAddressLink {
  display: inline-block;
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

#walletBalanceInfo {
  margin-left: auto;
}

/* 链接样式 */
#walletAddressLink,
.wallet-address-link,
.tx-hash-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

#walletAddressLink:hover,
.wallet-address-link:hover,
.tx-hash-link:hover {
  text-decoration: underline;
}

.link {
  color: #007BFF;
  text-decoration: none;
  font-size: 18px;
  font-weight: bold;
  vertical-align: middle;
  line-height: 1.2;
  display: inline-block;
  margin-top: -2px;
}

.link:hover {
  text-decoration: underline;
}

/* 符文链接 */
.rune-links {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rune-link {
  margin-right: 20px;
  text-decoration: none;
}

.rune-link img {
  width: 16px;
  height: 16px;
  margin-top: -3px;
}

.mint-link {
  text-decoration: none;
  margin-left: auto;
}

/* ========================================
   12. 工具提示样式 (Tooltip Styles)
   ======================================== */

.tooltip-inner {
  background-color: #007bff;
  color: #fff;
  border-radius: 6px;
  padding: 10px;
  font-size: 14px;
  max-width: 450px;
  width: 450px;
  text-align: left;
}

.tooltip .tooltip-arrow {
  display: none;
}

.tooltip::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #007bff transparent transparent transparent;
}

.tooltip.bs-tooltip-top::after {
  top: auto;
  bottom: -10px;
  border-color: #007bff transparent transparent transparent;
}

.tooltip-icon {
  position: relative;
  cursor: pointer;
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #3498db;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tooltip-icon:hover {
  background-color: #2980b9;
}

/* ========================================
   13. 通用工具类样式 (Utility Classes)
   ======================================== */

/* 文本样式 */
.gray-text {
  color: #6c757d;
}

.pay-attention {
  color: #dc3545;
  font-size: 12px;
}

.line-through {
  text-decoration: line-through;
}

.error-message {
  color: rgb(222, 34, 34);
  font-size: 14px;
  margin-top: 5px;
  font-weight: bold;
}

.text-success {
  color: green;
}

.text-danger {
  color: red;
}

.loading-message {
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

/* 输入错误动画 */
@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.input-error {
  border-color: red !important;
  animation: shake 0.5s;
}

/* 背景颜色 */
.bg-black {
  background-color: #555151 !important;
}

.bg-green {
  background-color: #28a745 !important;
}

.bg-red {
  background-color: #ff0000 !important;
}

/* 其他工具类 */
.w-auto {
  width: auto !important;
}

.custom-size {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
  margin-top: 25px;
}

#accelerateButton.custom-size {
  padding: 0.075rem 0.5rem;
}

/* ========================================
   14. 导航栏样式 (Navigation Styles)
   ======================================== */

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  width: 100%;
  display: flex;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.navbar-logo {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  margin-right: 20px;
}

.navbar-menu {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.navbar-item {
  margin-right: 20px;
}

.navbar-icon {
  margin-right: 15px;
}

.navbar-link {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.navbar-link:hover {
  color: #007bff;
}

.material-icons {
  font-size: 18px;
  margin-left: 2px;
}

.dropdown-menu {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 150px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1;
  border-radius: 4px;
  padding: 5px 0;
  background-color: #f9f9f9;
  font-size: 14px;
}

.navbar-item:hover .dropdown-menu {
  display: block;
}

.dropdown-menu li {
  padding: 8px 15px;
}

.dropdown-menu li a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
}

.dropdown-menu li a:hover {
  color: #007bff;
}

.wallet-icon {
  color: #333;
  margin-right: 15px;
}

/* 汉堡菜单按钮 */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 15px;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: #333;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* 移动端菜单 */
.mobile-menu {
  display: none;
  position: fixed;
  top: 45px; /* 导航栏高度 */
  left: 0;
  right: 0;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
  max-height: calc(100vh - 45px);
  overflow-y: auto;
}

.mobile-menu-content {
  padding: 25px; /* 增大整体内边距 */
}

.mobile-menu-section {
  margin-bottom: 30px; /* 增大区块间距 */
  border-bottom: 1px solid #eee;
  padding-bottom: 20px; /* 增大底部内边距 */
}

.mobile-menu-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.mobile-menu-section h6 {
  font-size: 22px; /* 进一步增大标题字体 */
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.mobile-menu-link {
  display: block;
  padding: 18px 0; /* 进一步增大内边距 */
  color: #555;
  text-decoration: none;
  font-size: 20px; /* 进一步增大链接字体 */
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
  line-height: 1.4; /* 增加行高 */
}

.mobile-menu-link:hover {
  color: #007bff;
  background-color: #f8f9fa;
  padding-left: 10px;
}

.mobile-menu-link:last-child {
  border-bottom: none;
}

/* 暗黑模式切换开关 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

#darkModeSwitch {
  display: inline-flex;
  font-size: 14px;
  margin-left: -2rem;
}

/* ========================================
   15. 暗黑模式样式 (Dark Mode Styles)
   ======================================== */

/* 导航栏暗黑模式 */
.navbar.navbar-dark {
  background-color: #333;
  border-bottom-color: #444;
}

.navbar-dark .navbar-logo {
  color: #fff;
}

.navbar-dark .navbar-link {
  color: #ccc;
}

.navbar-dark .navbar-link:hover {
  color: #fff;
}

.navbar-dark .dropdown-menu {
  background-color: #333;
}

.navbar-dark .dropdown-menu li a {
  color: #ccc;
}

.navbar-dark .dropdown-menu li a:hover {
  color: #fff;
  background-color: #444;
}

.navbar-dark .wallet-icon {
  color: #ccc;
}

/* 汉堡菜单暗黑模式 */
.navbar-dark .hamburger-line {
  background-color: #ccc;
}

.navbar-dark .mobile-menu {
  background-color: #333;
  border-bottom-color: #444;
}

.navbar-dark .mobile-menu-section h6 {
  color: #fff;
  border-bottom-color: #007bff;
}

.navbar-dark .mobile-menu-link {
  color: #ccc;
  border-bottom-color: #444;
}

.navbar-dark .mobile-menu-link:hover {
  color: #fff;
  background-color: #444;
}

/* 页面暗黑模式 */
body.dark-mode {
  background-color: #222;
  color: #eee;
}

.dark-mode .transaction-container,
.dark-mode .utxo-group,
.dark-mode #confirmedList .transaction-item {
  background-color: #333;
  border-color: #444;
}

.dark-mode .gray-text {
  color: #aaa;
}

.dark-mode .fee-option,
.dark-mode .fee-option-utxo {
  background-color: #444;
  border-color: #555;
  color: #eee;
}

.dark-mode .fee-option:hover,
.dark-mode .fee-option-utxo:hover {
  background-color: #555;
}

.dark-mode .fee-option.active,
.dark-mode .fee-option-utxo.active {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

/* ========================================
   16. 其他特殊样式 (Special Styles)
   ======================================== */

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 2px 4px;
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 3px;
  margin-left: 5px;
}

.badge-new {
  background-color: #28a745;
  color: white;
}

.badge-hot {
  background-color: #dc3545;
  color: white;
}

.dark-mode .badge-new {
  background-color: #34d058;
}

.dark-mode .badge-hot {
  background-color: #f85149;
}

/* 加载动画 */
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  border: 0.15em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

.spinner-border-sm {
  width: 0.8rem;
  height: 0.8rem;
  border-width: 0.1em;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

/* 复制通知 */
.copy-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #4caf50;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: opacity 0.5s ease-in-out;
}

/* 隐藏/显示面板按钮 */
#hideBoard {
  height: 16px;
  width: 16px;
  margin-bottom: 2px;
  cursor: pointer;
}

#showBoard {
  cursor: pointer;
  position: absolute;
  left: -20px;
  height: 25px;
  width: 25px;
  z-index: 1;
  display: none;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  padding: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮文本切换 */
#pendingButton, #confirmedButton {
  width: auto;
}

#confirmedButton {
  margin-left: -5px;
}

.short-text {
  display: none;
}

.long-text {
  display: inline;
}

/* 头部样式 */
.alkanes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.index-block {
  color: #3b3c3d;
  font-size: 12px;
}

/* 特殊元素 */
#og {
  display: none;
  width: 60px;
  margin-left: 7px;
}

.btc-amount {
  display: inline;
}

/* 关闭按钮 */
.close-btn {
  display: none;
}

.options-btn {
  font-size: 24px;
  line-height: 1;
  padding: 0px 5px;
  position: relative;
  top: 5px;
}

.dots {
  display: inline-block;
  text-align: center;
  width: 100%;
  position: relative;
  top: -5px;
}

/* 无交易消息 */
#noTransactionsMessage, #noconfirmedMessage {
  display: none;
  padding: 330px 8px;
  color: #6c757d;
  font-size: 16px;
  font-weight: bold;
}

/* 详情容器 */
#mintDetailsContainer,
#utxoDetailsContainer {
  max-height: 170px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: -25px;
}

#mintDetailsContainer table,
#utxoDetailsContainer table {
  width: 100%;
}

#mintDetailsContainer th,
#mintDetailsContainer td,
#utxoDetailsContainer th,
#utxoDetailsContainer td {
  padding: 5px;
  text-align: left;
  border-bottom: 1px solid #ccc;
}

#mintDetailsContainer th,
#utxoDetailsContainer th {
  background-color: #f2f2f2;
}

#mintDetailsContainer tr:last-child td,
#utxoDetailsContainer tr:last-child td {
  border-bottom: none;
}

/* 符文卡片 */
.rune-card {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rune-body p {
  margin: 5px 0;
  color: #666;
}

.rune-item {
  position: relative;
  padding: 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.rune-card.hidden {
  display: none;
  position: absolute;
  left: -5px;
  top: 70px;
  min-width: 200px;
  max-width: 0px;
  padding: 10px;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 10px rgba(0,0,0,0.2);
  z-index: 100;
}

/* ========================================
   17. 媒体查询 (Media Queries)
   ======================================== */

/* 平板和小屏幕 */
@media (max-width: 1600px) {
  #pendingButton, #confirmedButton {
    width: 60px;
  }

  .short-text {
    display: inline;
  }

  .long-text {
    display: none;
  }
}

/* 中等屏幕 */
@media (max-width: 1150px) {
  .left-panel, .right-area {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .middle-panel {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media (max-width: 1024px) {
  .col-md-6 {
    width: 100%;
  }
}

/* 小屏幕适配 */
@media (max-width: 991px) {
  .btc-amount {
    display: none;
  }

  #mem-left {
    width: 60%;
  }

  #mem-right {
    width: 40%;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  /* 导航栏保持舒适大小 */
  .navbar {
    padding: 12px 12px;
    height: 65px;
    min-height: 65px;
  }

  .navbar-logo {
    font-size: 1.1rem;
    margin-right: 12px;
  }

  .mobile-menu-toggle {
    width: 38px;
    height: 38px;
    margin-left: 12px;
  }

  .hamburger-line {
    width: 28px;
    height: 3.5px;
  }

  .mobile-menu {
    top: 65px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 23px; /* 适中标题字体 */
  }

  .mobile-menu-link {
    font-size: 21px; /* 适中链接字体 */
    padding: 19px 0; /* 适中内边距 */
  }

  #darkModeSwitch {
    font-size: 0.85rem;
    margin-left: 8px;
  }

  .switch {
    width: 42px;
    height: 22px;
    margin-right: 6px;
  }

  .slider:before {
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(20px);
  }

  body {
    padding-top: 70px; /* 调整页面顶部间距 */
  }

  /* 钱包信息容器优化 */
  #walletInfo {
    justify-content: flex-start; /* 移除space-between */
  }

  #walletInfo > span:first-child {
    width: 100%; /* 钱包地址使用全宽 */
    margin-bottom: 8px;
  }

  /* 移动端钱包地址显示优化 */
  #walletAddressLink {
    max-width: none; /* 移除宽度限制 */
    width: 100%; /* 使用全宽 */
    word-break: break-all; /* 允许在任意字符处换行 */
    white-space: normal; /* 允许换行 */
    overflow: visible; /* 显示完整内容 */
    text-overflow: clip; /* 不使用省略号 */
    line-height: 1.3; /* 增加行高便于阅读 */
  }

  .address-input-flex-container {
    flex-direction: column;
    align-items: stretch;
  }

  .quantity-input-group {
    justify-content: flex-start;
    margin-top: 10px;
  }

  .address-quantity-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .quantity-input-group {
    margin-top: 10px;
    width: 100%;
  }
}

@media (max-width: 576px) {
  /* 超小屏幕导航栏优化 */
  .navbar {
    padding: 10px 10px;
    height: 60px;
    min-height: 60px;
  }

  .navbar-logo {
    font-size: 1rem;
    margin-right: 10px;
  }

  .mobile-menu-toggle {
    width: 35px;
    height: 35px;
    margin-left: 10px;
  }

  .hamburger-line {
    width: 25px;
    height: 3px;
  }

  .mobile-menu {
    top: 60px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 22px; /* 紧凑但清晰的标题字体 */
  }

  .mobile-menu-link {
    font-size: 20px; /* 紧凑但清晰的链接字体 */
    padding: 18px 0; /* 紧凑但舒适的内边距 */
  }

  #darkModeSwitch {
    font-size: 0.8rem;
    margin-left: 6px;
  }

  .switch {
    width: 38px;
    height: 20px;
    margin-right: 5px;
  }

  .slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(18px);
  }

  body {
    padding-top: 65px; /* 调整页面顶部间距 */
  }

  #walletInfo {
    flex-direction: column;
    align-items: flex-start;
  }

  #walletInfo > span:first-child {
    margin-bottom: 5px;
  }

  #walletBalanceInfo {
    margin-left: 0;
    margin-top: 5px;
  }

  .quantity-input-group {
    flex-wrap: wrap;
  }
}

/* 移动端全面适配 */
@media (max-width: 991px) {
  /* 基础字体大小 */
  html {
    font-size: 32px;
  }

  body {
    font-size: 1rem;
    line-height: 1.5;
    padding-top: 75px; /* 调整为导航栏高度 + 5px */
  }

  /* 导航栏移动端优化 - 汉堡菜单 */
  .navbar {
    padding: 15px 15px; /* 进一步增大内边距 */
    height: 70px; /* 进一步增大高度 */
    min-height: 70px;
    flex-wrap: nowrap; /* 不换行 */
  }

  .navbar-container {
    flex-direction: row; /* 保持水平布局 */
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }

  .navbar-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .navbar-right {
    display: flex;
    align-items: center;
    margin-left: auto;
    flex-shrink: 0;
  }

  .navbar-logo {
    font-size: 1.2rem; /* 增大 Logo 字体 */
    margin-right: 15px;
    flex-shrink: 0;
    font-weight: bold;
  }

  /* 隐藏桌面端菜单，显示汉堡菜单 */
  .desktop-menu {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: flex !important;
    width: 40px; /* 增大汉堡菜单按钮 */
    height: 40px;
    margin-left: 15px;
  }

  .hamburger-line {
    width: 30px; /* 增大汉堡线条 */
    height: 4px;
  }

  .mobile-menu {
    top: 70px; /* 调整菜单位置匹配新的导航栏高度 */
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 24px; /* 最大标题字体 */
  }

  .mobile-menu-link {
    font-size: 22px; /* 最大链接字体 */
    padding: 20px 0; /* 最大内边距 */
  }

  /* 暗黑模式切换开关移动端优化 */
  #darkModeSwitch {
    display: inline-flex;
    font-size: 0.9rem; /* 增大字体 */
    margin-left: 10px;
    align-items: center;
  }

  .switch {
    width: 45px; /* 增大开关 */
    height: 24px;
    margin-right: 8px;
  }

  .slider:before {
    height: 18px; /* 增大滑块 */
    width: 18px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(21px);
  }



  /* 确保所有文本元素使用相对字体大小 */
  p, span, div, label, input, select, button, a,
  .red-bold, .normal-label, #myChart,
  #noTransactionsMessage, #noconfirmedMessage,
  .ybot_head, .index-block, .rune-name-text,
  .left-panel .rune-header .rune-name-text, .hot-runeid {
    font-size: 1rem;
  }

  #feesInfoContainer {
    margin-top: 2rem;
  }

  #hideBoard {
    height: 28px;
    width: 28px;
    margin-bottom: 5px;
    cursor: pointer;
  }

  #showSave {
    display: none;
  }

  /* 特别调整某些元素的大小 */
  .fee-option, .fee-option-utxo, .fee-item, #feeRate, #maxFee {
    font-size: 0.9rem;
  }

  .rune-link img {
    width: 1rem;
    height: 1rem;
    margin-top: -3px;
  }

  /* 调整标题大小 */
  h1 { font-size: 1.8rem; }
  h2 { font-size: 1.6rem; }
  h3 { font-size: 1.4rem; }
  h4 { font-size: 1.3rem; }

  /* 增大按钮和输入框的大小 */
  .btn, input[type="text"], input[type="number"], select {
    font-size: 1rem;
    padding: 10px;
    height: auto;
  }

  /* 调整图表区域 - 保持固定尺寸避免飘移 */
  #myChart {
    height: 100px;
    width: 250px;
    max-width: 100%;
  }

  /* 确保小图标也适当放大 */
  .icon, .fa {
    font-size: 1.2rem;
  }

  /* 调整特定区域的布局 */
  .rune-progress-container, .fee-options {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .fee-option, .fee-option-utxo {
    margin-bottom: 5px;
  }

  /* 调整布局 */
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  .row {
    margin-left: -15px;
    margin-right: -15px;
  }

  .col-md-3, .col-md-6, .left-panel, .middle-panel, .right-panel {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }

  /* 将左侧面板显示为块级元素 */
  .left-panel {
    display: block;
    margin-bottom: 20px;
    width: 100%;
    order: -1;
  }

  /* 调整热门列表的高度 */
  #hotlist {
    height: auto;
    max-height: 600px;
  }

  /* 确保中间面板和右侧面板垂直堆叠 */
  .middle-panel, .right-panel {
    margin-bottom: 20px;
  }

  /* 调整右侧任务栏的样式 */
  .right-panel {
    margin-top: 20px;
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
  }

  #splitCount {
    max-width: none;
    text-align: center;
  }

  #mintingList, #confirmedList {
    max-height: none;
  }

  .rune-progress-container {
    overflow-x: auto;
  }

  .btn, input, select, textarea {
    width: 100%;
    margin-bottom: 10px;
  }

  .button-on, .button-off {
    flex: 1 1 auto;
    padding: 5px;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 60px;
    min-width: 240px;
  }

  .rune-info-box {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 80px;
  }

  .rune-name, .rune-quantity {
    width: 100%;
    word-break: break-word;
  }

  #runeFilterDropdown {
    max-width: 300px;
  }

  #batchAccelerateButton, #batchDeleteButton {
    max-width: 300px;
    margin-top: 46px;
  }

  .tooltip-icon, #safetip {
    display: none;
  }

  .right-area {
    margin-top: 20px;
  }

  .rune-info-box {
    flex-direction: column;
    align-items: flex-start;
  }

  .rune-name {
    margin-bottom: 5px;
  }

  .rune-quantity {
    display: block;
    margin-bottom: 5px;
  }

  #refreshRune {
    display: inline-block;
    margin-top: 5px;
  }

  #mem-left, #mem-right {
    width: 100%;
  }

  .address-quantity-container {
    flex-direction: column;
    align-items: stretch;
  }

  .total-quantity {
    margin-bottom: 10px;
  }

  .quantity-input-group {
    width: 100%;
    justify-content: flex-start;
  }

  #addressQuantity {
    width: 180px;
    height: 50px;
    margin-top: 15px;
  }

  #walletInfo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start; /* 移除space-between */
  }

  #walletInfo > span:first-child {
    margin-bottom: 5px;
    width: 100%; /* 钱包地址使用全宽 */
  }

  #walletBalanceInfo {
    margin-left: 0;
    display: flex;
    align-items: center;
  }

  #walletBalance {
    margin-left: 5px;
    margin-right: 5px;
  }

  #og {
    width: 120px;
    margin-top: 5px;
  }

  .hot-springs {
    color: #ff0000;
    font-size: 1.2rem;
    font-weight: normal;
    margin-top: -3px;
    margin-right: 10px;
  }
}

/* ========================================
   18. 地址输入相关样式 (Address Input Styles)
   ======================================== */

.address-input-flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 10px;
}

.button-group {
  display: flex;
  flex-shrink: 0;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  flex-grow: 1;
  justify-content: flex-end;
}

.quantity-input-group label {
  white-space: nowrap;
  font-size: 0.875rem;
}

#addressQuantity {
  width: 80px;
  flex-shrink: 1;
  min-width: 60px;
  height: 30px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  appearance: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  cursor: pointer;
}

.button-on, .button-off {
  height: 30px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1;
}

.address-quantity-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.total-quantity {
  flex-shrink: 0;
}

.split-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.slider-container, .split-count-input {
  flex: 1 1 calc(50% - 5px);
  min-width: 150px;
}

.slider-border {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  height: 38px;
  display: flex;
  align-items: center;
}

.form-range {
  width: 100%;
  margin: 0;
}

@media (max-width: 991px) {
  .slider-container, .split-count-input {
    flex-basis: 100%;
  }
}
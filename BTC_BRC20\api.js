const express = require('express');
const utils = require('./brc20.js');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const dotenv = require("dotenv");
const workerpool = require('workerpool');
const pool = workerpool.pool('./worker.js', { minWorkers: 'max' });
const { encodeRuneId } = require('./runes');
dotenv.config();

const router = express.Router();

// 声明全局变量
global.mempoolFeeRate = 0;
global.mempoolNTx = 0;
global.latestBlockHeight = 0;
let isFetchingMempool = false;

// 缓存配置
const CACHE_CONFIG = {
  fees: 10000, // 10秒
  mints_hot: 20000, // 20秒
  getRuneInfo: 20000, // 20秒
  getHolder: 30000 // 30秒
};

// BRC20热门铸造推送相关变量
let brc20TrackingData = {};  // 用于记录BRC20检测数据
let fastGrowingBrc20 = [];  // 用于记录需要关注的BRC20
const brc20TrackingDataFilePath = path.join(__dirname, 'brc20TrackingData.json');
const fastGrowingBrc20FilePath = path.join(__dirname, 'fastGrowingBrc20.json');
let dataChanged = false;

// getHolder API 防重复请求机制
const holderRequestTracker = new Map(); // 记录每个tick的最后请求时间
const HOLDER_REQUEST_INTERVAL = 30000; // 30秒间隔

// 通用缓存管理器
const cacheManager = {
  caches: new Map(),
  pendingPromises: new Map(),

  /**
   * 确保缓存数据
   * @param {string} cacheKey - 缓存键
   * @param {number} cacheDuration - 缓存持续时间（毫秒）
   * @param {Function} updateFunction - 更新函数，返回Promise
   * @returns {Promise<any>} - 缓存数据
   */
  async ensureCacheData(cacheKey, cacheDuration, updateFunction) {
    const now = Date.now();

    // 检查缓存是否存在且未过期
    const cacheEntry = this.caches.get(cacheKey);
    if (cacheEntry && (now - cacheEntry.timestamp < cacheDuration)) {
      return cacheEntry.data;
    }

    // 如果有正在进行的更新请求，等待它完成
    if (this.pendingPromises.has(cacheKey)) {
      return this.pendingPromises.get(cacheKey);
    }

    // 没有缓存且没有进行中的更新，开始更新
    const updatePromise = (async () => {
      try {
        const data = await updateFunction();
        this.caches.set(cacheKey, { data, timestamp: Date.now() });
        return data;
      } catch (error) {
        // 如果更新失败，保持旧的缓存数据
        if (cacheEntry) {
          return cacheEntry.data;
        }
        throw error;
      } finally {
        this.pendingPromises.delete(cacheKey);
      }
    })();

    this.pendingPromises.set(cacheKey, updatePromise);
    return updatePromise;
  },

  /**
   * 清理过期的缓存
   */
  cleanup() {
    const now = Date.now();
    for (const [key, { timestamp }] of this.caches.entries()) {
      let cacheDuration;
      switch (key) {
        case 'fees':
          cacheDuration = CACHE_CONFIG.fees;
          break;
        case 'mints_hot':
          cacheDuration = CACHE_CONFIG.mints_hot;
          break;
        default:
          if (key.startsWith('getRuneInfo_')) {
            cacheDuration = CACHE_CONFIG.getRuneInfo;
          } else if (key.startsWith('getHolder_')) {
            // getHolder 缓存保留更长时间，避免频繁请求外部API
            cacheDuration = CACHE_CONFIG.getHolder * 10; // 5分钟
          } else {
            cacheDuration = 0; // 默认不保留
          }
      }
      if (now - timestamp > cacheDuration) {
        this.caches.delete(key);
      }
    }

    // 清理过期的请求跟踪记录
    for (const [tick, timestamp] of holderRequestTracker.entries()) {
      if (now - timestamp > HOLDER_REQUEST_INTERVAL * 2) { // 保留1分钟
        holderRequestTracker.delete(tick);
      }
    }
  }
};

// 定期清理缓存
setInterval(() => {
  cacheManager.cleanup();
}, 5000); // 每5秒检查一次

let cachedFees = null;

// 更新费用信息并返回处理后的数据
async function updateFees() {
  // 定义API接口列表（主接口和备用接口）
  const apiUrls = [
    'http://************:1080/api/v1/fees/recommended',
    'http://*************:1080/api/v1/fees/recommended'
  ];

  let lastError = null;

  // 依次尝试每个API接口
  for (let i = 0; i < apiUrls.length; i++) {
    try {
      //console.log(`尝试获取费用信息 - API ${i + 1}: ${apiUrls[i]}`);
      const response = await axios.get(apiUrls[i], { timeout: 10000 });

      if (response.data) {
        cachedFees = response.data;
        //console.log(`费用信息更新成功 (使用API ${i + 1})`);
        return cachedFees;
      }
    } catch (error) {
      lastError = error;
      //console.warn(`API ${i + 1} 获取费用信息失败: ${error.message}`);
      // 继续尝试下一个API
    }
  }

  // 所有API都失败了
  //console.error('所有API接口获取费用信息都失败:', lastError?.message || '未知错误');

  // 如果更新失败，保持现有的缓存
  if (cachedFees) {
    return cachedFees;
  }
  throw lastError || new Error('所有费用API接口都不可用');
}

// 获取最新区块高度并更新全局变量
let isUpdating = false;
let lastProcessedHeight = 0;

async function fetchAndUpdateData() {
  if (isUpdating) {
    return;
  }

  isUpdating = true;

  // 定义API接口列表（主接口和备用接口）
  const apiUrls = [
    'http://************:1080/api/blocks/tip/height',
    'http://*************:1080/api/blocks/tip/height'
  ];

  let latestBlockHeight = null;
  let lastError = null;

  // 依次尝试每个API接口
  for (let i = 0; i < apiUrls.length; i++) {
    try {
      //console.log(`尝试获取区块高度 - API ${i + 1}: ${apiUrls[i]}`);
      const tipResponse = await axios.get(apiUrls[i], { timeout: 10000 });
      latestBlockHeight = parseInt(tipResponse.data);

      if (!isNaN(latestBlockHeight) && latestBlockHeight > 0) {
        global.latestBlockHeight = latestBlockHeight;

        if (latestBlockHeight <= lastProcessedHeight) {
          isUpdating = false;
          return;
        }

        lastProcessedHeight = latestBlockHeight;
        //console.log(`已更新最新区块高度: ${latestBlockHeight} (使用API ${i + 1})`);
        isUpdating = false;
        return; // 成功获取，退出函数
      }
    } catch (error) {
      lastError = error;
      //console.warn(`API ${i + 1} 获取区块高度失败: ${error.message}`);
      // 继续尝试下一个API
    }
  }

  // 所有API都失败了
  //console.error('所有API接口获取最新区块高度都失败:', lastError?.message || '未知错误');
  isUpdating = false;
}

let cachedMints_hot = null;

// 更新 mints_hot 信息并返回处理后的数据
async function updateMints_hot(Lowfee = false) {
  try {
    const url = Lowfee ? 'http://*************:5002/api/brc20/trending?limit=15' : 'http://***********:5002/api/brc20/trending?limit=15';
    const response = await axios.get(url);
    const rawData = response.data;

    if (!rawData || typeof rawData.total !== 'number' || !Array.isArray(rawData.results)) {
      throw new Error('Invalid data format');
    }

    let data = rawData.results;

    // 如果结果大于10个，去掉 median_fee 最低的 50%
    if (data.length > 15) {
      // 按 median_fee 排序（降序）
      data.sort((a, b) => b.median_fee - a.median_fee);

      // 计算要保留的数量（保留前50%）
      const keepCount = Math.floor(data.length * 0.5);

      // 只保留前50%的数据
      data = data.slice(0, keepCount);
    }

    // 找出最大值，用于归一化
    const maxTransactions = Math.max(...data.map(item => item.total_txs));
    const maxFeeRate = Math.max(...data.map(item => item.median_fee));
    const maxHolders = Math.max(...data.map(item => item.unique_addresses));

    // 计算每个项目的得分
    const scoredData = data.map(item => ({
      ...item,
      score: (
        (item.total_txs / maxTransactions * 0.15) +
        (item.median_fee / maxFeeRate * 0.6) +
        (item.unique_addresses / maxHolders * 0.25)
      )
    }));

    // 根据得分排序（降序）
    scoredData.sort((a, b) => b.score - a.score);

    // 构建返回数据，保持原有结构
    const resultData = {
      total: rawData.total,
      results: scoredData
    };

    // 仅在处理默认情况(Lowfee=false)时更新全局变量，用于向后兼容
    if (!Lowfee) {
      cachedMints_hot = resultData;
    }

    return resultData;
  } catch (error) {
    // 如果更新失败，尝试从缓存管理器获取现有数据
    const cacheKey = `mints_hot_${Lowfee}`;
    const existingCache = cacheManager.caches.get(cacheKey);
    if (existingCache && existingCache.data) {
      return existingCache.data;
    }
    
    // 最后回退到全局变量（仅限Lowfee=false的情况）
    if (!Lowfee && cachedMints_hot) {
      return cachedMints_hot;
    }
    
    throw error;
  }
}

// 获取北京时间
function getBeijingTime() {
  const now = new Date();

  // 获取 UTC 时间
  const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);

  // 北京时间偏移 UTC+8
  const beijingOffset = 8 * 60 * 60000;
  const beijingTime = new Date(utcTime + beijingOffset);

  const year = beijingTime.getFullYear();
  const month = String(beijingTime.getMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getDate()).padStart(2, '0');
  const hours = String(beijingTime.getHours()).padStart(2, '0');
  const minutes = String(beijingTime.getMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 格式化数字为中文表示（万、亿）
function formatToChineseNumber(num) {
  if (!num || isNaN(num)) return "0";
  
  num = parseInt(num);
  if (num >= 100000000) {
    const formattedNum = (num / 100000000).toFixed(2);
    if (formattedNum.endsWith('.00')) {
      return formattedNum.slice(0, -3) + '亿';
    } else {
      return formattedNum + '亿';
    }
  } else if (num >= 10000) {
    const formattedNum = (num / 10000).toFixed(2);
    if (formattedNum.endsWith('.00')) {
      return formattedNum.slice(0, -3) + '万';
    } else {
      return formattedNum + '万';
    }
  }
  return num;
}

// 获取最快手续费
async function getFastestFee() {
  try {
    const response = await axios.get('https://mempool.space/api/v1/fees/recommended');
    const data = response.data;
    return data.fastestFee;
  } catch (error) {
    // 主API失败，尝试备用API
    try {
      const response = await axios.get('http://************:1080/api/v1/fees/recommended');
      const data = response.data;
      return data.fastestFee;
    } catch (backupError) {
      return "暂无";
    }
  }
}

// 获取最近区块时间
async function getRecentBlockTimes() {
  try {
    const response = await axios.get('http://************:1080/api/v1/blocks');
    const blocks = response.data.slice(0, 3); // 取最近三个块的信息
    const currentTime = Math.floor(Date.now() / 1000);
    const times = blocks.map(block => {
      const timeDiff = currentTime - block.timestamp;
      const minutesAgo = Math.floor(timeDiff / 60);
      return `${minutesAgo}分钟前`;
    });
    return times;
  } catch (error) {
    return [];
  }
}

// 电报通知函数
async function sendTelegramNotification(brc20Info) {
  console.log('推送BRC20电报信息:', brc20Info);
  if(process.env.TESTNET === "true"){
    return;
  }
  // 检查是否存在 debug.txt 文件，如果存在则不发送电报推送
  const debugFilePath = path.join(__dirname, 'debug.txt');
  if (fs.existsSync(debugFilePath)) {
    console.log('检测到 debug.txt 文件，跳过电报推送');
    return;
  }
  const feeRate = await getFastestFee();
  const times = await getRecentBlockTimes();
  const API_TOKEN = '6950143867:AAFLEbME6cx62FC8BCIPr900MHgv7WdsNU8';
  const CHANNEL_USERNAME = '@ybot_runes'; // 设置您的BRC20频道名称
  
  // 根据不同级别显示不同的提示
  let levelText;
  if (brc20Info.level === 3) {
    levelText = `【Ybot热门BRC20铸造-三级提醒：${getBeijingTime()}】\`\`\`
  🔥 最近10分钟提交铸造 ${brc20Info.change} 个交易，内存池数量：${brc20Info.total_txs}个。
  ⚠️ 这是最高级别提醒！
    \`\`\``;
  } else if (brc20Info.level === 2) {
    levelText = `【Ybot热门BRC20铸造-二级提醒：${getBeijingTime()}】\`\`\`
  🔥 最近10分钟提交铸造 ${brc20Info.change} 个交易，内存池数量：${brc20Info.total_txs}个。
    \`\`\``;
  } else {
    levelText = `【Ybot热门BRC20铸造-一级提醒：${getBeijingTime()}】\`\`\`
  📈 最近10分钟提交铸造 ${brc20Info.change} 个交易，内存池数量：${brc20Info.total_txs}个。
    \`\`\``;
  }
  
  // 计算进度
  const progress = brc20Info.minted && brc20Info.supply ? 
    parseFloat(parseInt(brc20Info.minted) / parseInt(brc20Info.supply) * 100).toFixed(2) : "0";
  
  const MESSAGE = `
  ${levelText}
  🤖 *铭文名称：*${brc20Info.tick}
  
  🏆 *铸造限制：*${formatToChineseNumber(brc20Info.limitPerMint)}/次
  
  📈 *铸造进度：*${formatToChineseNumber(Math.floor(parseInt(brc20Info.minted) / parseInt(brc20Info.limitPerMint)))}/${formatToChineseNumber(Math.floor(parseInt(brc20Info.supply) / parseInt(brc20Info.limitPerMint)))} (${progress}%)
  
  📊 *内存池数：*${brc20Info.total_txs}张 (${brc20Info.unique_addresses}人)
  ${feeRate ? `
  ⛽️ *当前gas：* ${feeRate} sats/vB` : ""}
  ${times ? `
  🧱 *最近出块：* ${times.join('，')}` : ""}

  🔗 *交易市场：*[okx](https://www.okx.com/zh-hans/web3/marketplace/ordinals/token/${encodeURIComponent(brc20Info.tick)}) | [unisat](https://unisat.io/market/brc20/${encodeURIComponent(brc20Info.tick)}) | [geniidata](https://geniidata.com/ordinals/brc20/${encodeURIComponent(brc20Info.tick)})
  
  🔗 *铸造链接：*[通过Ybot铸造,支持无限加速](https://brc20.ybot.io/?tick=${encodeURIComponent(brc20Info.tick)})
  
  🔗 [点我跳转【推特】查看话题](https://x.com/search?q=${encodeURIComponent(brc20Info.tick)})
  `;

  console.log(MESSAGE);
  
  const url = `https://api.telegram.org/bot${API_TOKEN}/sendMessage`;
  const payload = {
    chat_id: CHANNEL_USERNAME,
    text: MESSAGE,
    parse_mode: 'Markdown',
    disable_web_page_preview: true
  };
  
  try {
    const response = await axios.post(url, payload);
    if (response.status === 200) {
      console.log(`成功发送BRC20第${brc20Info.level}级提醒!`);
    } else {
      console.log("Failed to send message:", response.data);
    }
  } catch (error) {
    console.log("Error sending message:", error.response ? error.response.data : error.message);
  }
}

// 保存数据到本地 JSON 文件
function saveDataToFile(filePath, data) {
  if (Math.random() < 1 / 20) {
    cleanOldData();
  }
  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  dataChanged = false;
}

// 清理超过1小时的数据记录
function cleanOldData() {
  const oneHourAgo = Date.now() - 60 * 60 * 1000 * 2;
  for (const tick in brc20TrackingData) {
    brc20TrackingData[tick] = brc20TrackingData[tick].filter(record => record.time > oneHourAgo);
  }
}

// 加载本地 JSON 文件作为初始数据
function loadInitialData(filePath, dataVar) {
  if (fs.existsSync(filePath)) {
    try {
      const rawData = fs.readFileSync(filePath);
      return JSON.parse(rawData);
    } catch (error) {
      console.error(`Error loading data from ${filePath}:`, error.message);
      return dataVar;
    }
  }
  return dataVar;
}

// 新增API接口，返回服务器时间戳
router.get('/v1/server_time', (req, res) => {
  res.json({
    code: 0,
    msg: 'ok',
    data: Date.now()
  });
});

// 修改后的路由处理函数
router.post('/v1/update-rpc-node', (req, res) => {
  try {
    const { ipPort, username, password } = req.body;

    if (!ipPort || !username || !password) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const rpcJsonPath = path.join(__dirname, 'rpc.json');
    let rpcConfig = JSON.parse(fs.readFileSync(rpcJsonPath, 'utf8'));

    // 移除 URL 中的 "http://" 或 "https://" 前缀
    const cleanIpPort = ipPort.replace(/^(https?:\/\/)/, '');

    const nodeIndex = rpcConfig.nodes.findIndex(node => node.url === `http://${cleanIpPort}`);

    if (nodeIndex === -1) {
      // 如果节点不存在，添加新节点
      rpcConfig.nodes.push({
        url: `http://${cleanIpPort}`,
        auth: {
          user: username,
          pass: password
        }
      });
    } else {
      // 如果节点存在，更新用户名和密码
      rpcConfig.nodes[nodeIndex].auth.user = username;
      rpcConfig.nodes[nodeIndex].auth.pass = password;
    }

    fs.writeFileSync(rpcJsonPath, JSON.stringify(rpcConfig, null, 2));

    res.json({
      code: 0,
      msg: 'RPC node updated successfully',
    });
  } catch (error) {
    console.error('Error updating RPC node:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 校验时间戳函数
function isValidTimestamp(clientTimestamp) {
  try {
    const serverTimestamp = Date.now();
    const difference = Math.abs(serverTimestamp - clientTimestamp);
    const fiveMinutes = 5 * 60 * 1000; // 5分钟的毫秒数
    return difference <= fiveMinutes;
  } catch (error) {
    return false;
  }
}

// 修改后的 /v1/mints_hot 路由，使用通用的缓存管理
router.post('/v1/mints_hot', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { time, Lowfee = false } = JSON.parse(decryptedData);

    if (!isValidTimestamp(time)) {
      return res.status(400).json({ error: 'Invalid timestamp' });
    }

    // 使用通用的缓存管理确保数据
    const data = await cacheManager.ensureCacheData(
      `mints_hot_${Lowfee}`,
      CACHE_CONFIG.mints_hot,
      () => updateMints_hot(Lowfee)
    );

    if (data) {
      res.json(data); // 直接返回原始格式的数据
    } else {
      res.status(503).json({ error: 'Service unavailable', message: 'Mints_hot data is not available' });
    }
  } catch (error) {
    console.error('Error in /v1/mints_hot:', error.message);
    res.status(500).json({ code: 1, msg: 'Internal server error' });
  }
});

// 修改后的 /v1/getfee 路由，使用通用的缓存管理
router.post('/v1/getfee', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { time } = JSON.parse(decryptedData);

    if (!isValidTimestamp(time)) {
      return res.status(400).json({ error: 'Invalid timestamp' });
    }

    // 使用通用的缓存管理确保数据
    const data = await cacheManager.ensureCacheData(
      'fees',
      CACHE_CONFIG.fees,
      updateFees
    );

    if (data) {
      res.json(data); // 直接返回原始格式的数据
    } else {
      res.status(503).json({ error: 'Service unavailable', message: 'Gas fee data is not available' });
    }
  } catch (error) {
    console.error('Error in /v1/getfee:', error.message);
    res.status(500).json({ code: 1, msg: 'Internal server error' });
  }
});


// 获取mempool-blocks最新数据
const mempool_blocks = async () => {
  // 防止并发请求
  if (isFetchingMempool) {
    return;
  }
  
  isFetchingMempool = true;
  
  // 定义所有可用的URL
  const urls = [
    'https://mempool.space/api/v1/fees/mempool-blocks',
    'https://unisat.mempool.space/api/v1/fees/mempool-blocks',
    'http://************:1080/api/v1/fees/mempool-blocks'
  ];
  
  let success = false;
  
  for (const url of urls) {
    if (success) break;
    
    try {
      //console.log(`尝试从 ${url} 获取mempool数据...`);
      const response = await axios.get(url, {
        timeout: 5000 // 设置5秒超时
      });
      
      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        const firstBlock = response.data[0];
        
        // 获取第一个区块的feeRange第一个值
        if (firstBlock.feeRange && firstBlock.feeRange.length > 0) {
          // 保留最多8位小数
          global.mempoolFeeRate = parseFloat(firstBlock.feeRange[0].toFixed(8));
        }
        
        // 获取nTx值
        if (firstBlock.nTx !== undefined) {
          global.mempoolNTx = firstBlock.nTx;
        }
        
        //console.log(`已成功更新mempool数据(从 ${url}): feeRate=${global.mempoolFeeRate}, nTx=${global.mempoolNTx}`);
        success = true;
      }
    } catch (error) {
      //console.error(`从 ${url} 获取mempool-blocks数据失败:`, error.message);
      // 继续尝试下一个URL
    }
  }
  
  if (!success) {
    console.error('所有mempool数据源都获取失败');
  }
  
  isFetchingMempool = false;
};

// 修改后的 /v1/getRuneInfo 路由，保持原始输出格式
router.post('/v1/getRuneInfo', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    if (!encryptedData) {
      return res.status(400).json({ 
        code: 1,
        msg: 'Missing encrypted data',
        data: null 
      });
    }

    let decryptedData;
    try {
      decryptedData = utils.decryptData(encryptedData);
    } catch (error) {
      console.warn('Decryption failed:', error.message);
      return res.status(400).json({
        code: 1,
        msg: 'Invalid encrypted data',
        data: null
      });
    }

    const { runes, Lowfee = false} = JSON.parse(decryptedData);
    
    if (!runes || typeof runes !== 'string') {
      return res.status(400).json({ 
        code: 1,
        msg: 'Invalid runes parameter',
        data: null 
      });
    }

    const encodedTick = encodeURIComponent(runes.toLowerCase());

    // 使用通用的缓存管理确保数据
    const data = await cacheManager.ensureCacheData(
      `getRuneInfo_${encodedTick}_${Lowfee}`,
      CACHE_CONFIG.getRuneInfo,
      async () => {
        try {
          // 构建基础URL
          let url = Lowfee ? `http://*************:5002/api/brc20/tick/${encodedTick}/info` : `http://***********:5002/api/brc20/tick/${encodedTick}/info`;
          
          // 如果全局变量存在，添加fee和nTx参数
          if (global.mempoolFeeRate !== undefined && global.mempoolNTx !== undefined) {
            url += `?fee=${global.mempoolFeeRate},10000&nTx=${global.mempoolNTx}`;
          }
          const response = await axios.get(
            url,
            { 
              timeout: 5000, // 5秒超时
              validateStatus: function (status) {
                // 接受任何状态码,包括404等错误状态
                return true;
              }
            }
          );

          // 定义默认的空响应数据
          const emptyData = {
            limitPerMint: "0",
            supply: "0",
            minted: "0",
            selfMint: true
          };

          let runeData;

          // 检查响应数据并添加tick字段
          if (response.data && !response.data.tick) {
            response.data.tick = runes.toLowerCase();
          }
          
          if (response.status === 200 && response.data && response.data.inscriptionId) {
            runeData = response.data;
          } else {
            // 对于404或其他错误响应,使用空数据
            runeData = emptyData;
          }

          return runeData;
        } catch (error) {
          // 网络错误等情况也返回空数据
          return {
            limitPerMint: "0",
            supply: "0",
            minted: "0",
            selfMint: true
          };
        }
      }
    );

    // 返回包装后的格式
    return res.json({
      code: 0,
      msg: 'success',
      data: data
    });

  } catch (error) {
    console.warn('Error in /v1/getRuneInfo:', error.message);
    // 返回一个友好的错误响应，保持原始格式
    return res.status(500).json({
      code: 1,
      msg: 'Failed to fetch rune info',
      data: null
    });
  }
});

// /v1/getHolder API 接口
router.post('/v1/getHolder', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    if (!encryptedData) {
      return res.status(400).json({
        error: 'Missing encrypted data'
      });
    }

    let decryptedData;
    try {
      decryptedData = utils.decryptData(encryptedData);
    } catch (error) {
      //console.warn('Decryption failed:', error.message);
      return res.status(400).json({
        error: 'Invalid encrypted data'
      });
    }

    const { tick, height = 0, time } = JSON.parse(decryptedData);

    if (!tick || typeof tick !== 'string') {
      return res.status(400).json({
        error: 'Invalid tick parameter'
      });
    }

    if (!isValidTimestamp(time)) {
      return res.status(400).json({ error: 'Invalid timestamp' });
    }

    const encodedTick = encodeURIComponent(tick.toLowerCase());
    const now = Date.now();

    // 区块高度判断逻辑
    if (height === 0) {
      // 前端无数据，需要全量输出
      //console.log(`getHolder: 前端无数据，全量输出 tick=${tick}`);
    } else if (height > 0 && height !== global.latestBlockHeight) {
      // 优先返回缓存数据
      const cacheKey = `getHolder_${encodedTick}_${height}`;
      const existingCache = cacheManager.caches.get(cacheKey);
      if (existingCache && existingCache.data) {
        //console.log(`getHolder: 返回缓存数据 tick=${tick}, height=${height}`);
        // 确保缓存数据包含正确的 tick 字段
        const cacheData = { ...existingCache.data };
        if (cacheData.result) {
          cacheData.result.tick = tick;
        }
        return res.json(cacheData);
      }
    } else if (height === global.latestBlockHeight) {
      // 直接返回成功，unused_txes 为空数组（节省流量）
      //console.log(`getHolder: 高度匹配，返回空数据 tick=${tick}, height=${height}`);
      return res.json({
        error: null,
        result: {
          unused_txes: [],
          block_height: global.latestBlockHeight,
          total: 0,
          decimals: 18,
          limit_per_mint: "1000",
          tick: tick
        }
      });
    }

    // 防重复请求机制：同一个tick至少间隔30秒才能再次请求API
    const lastRequestTime = holderRequestTracker.get(encodedTick) || 0;
    const timeSinceLastRequest = now - lastRequestTime;

    if (timeSinceLastRequest < HOLDER_REQUEST_INTERVAL) {
      // 如果间隔不足30秒，尝试返回最新的缓存数据
      const latestCacheKey = Array.from(cacheManager.caches.keys())
        .filter(key => key.startsWith(`getHolder_${encodedTick}_`))
        .sort((a, b) => {
          const heightA = parseInt(a.split('_')[2]) || 0;
          const heightB = parseInt(b.split('_')[2]) || 0;
          return heightB - heightA;
        })[0];

      if (latestCacheKey) {
        const latestCache = cacheManager.caches.get(latestCacheKey);
        if (latestCache && latestCache.data) {
          //console.log(`getHolder: 防重复请求，返回最新缓存 tick=${tick}`);
          // 确保缓存数据包含正确的 tick 字段
          const cacheData = { ...latestCache.data };
          if (cacheData.result) {
            cacheData.result.tick = tick;
          }
          return res.json(cacheData);
        }
      }
    }

    // 智能缓存使用：检查是否有缓存数据的 block_height >= global.latestBlockHeight
    const smartCacheKey = Array.from(cacheManager.caches.keys())
      .filter(key => key.startsWith(`getHolder_${encodedTick}_`))
      .find(key => {
        const cache = cacheManager.caches.get(key);
        return cache && cache.data && cache.data.result &&
               cache.data.result.block_height >= global.latestBlockHeight;
      });

    if (smartCacheKey) {
      const smartCache = cacheManager.caches.get(smartCacheKey);
      //console.log(`getHolder: 智能缓存命中 tick=${tick}, cache_height=${smartCache.data.result.block_height}`);
      // 确保缓存数据包含正确的 tick 字段
      const cacheData = { ...smartCache.data };
      if (cacheData.result) {
        cacheData.result.tick = tick;
      }
      return res.json(cacheData);
    }

    // 请求外部API
    try {
      const apiUrl = `http://*************:8180/v1/brc20/holders?ticker=${encodedTick}&limit=100&simple=true`;
      //console.log(`getHolder: 请求外部API tick=${tick}, url=${apiUrl}`);

      const response = await axios.get(apiUrl, {
        timeout: 10000 // 10秒超时
      });

      // 更新请求时间记录
      holderRequestTracker.set(encodedTick, now);

      if (response.data && response.data.result) {
        const apiData = response.data;
        const blockHeight = apiData.result.block_height || global.latestBlockHeight;

        // 添加 tick 字段到响应数据中
        apiData.result.tick = tick;

        // 缓存数据
        const cacheKey = `getHolder_${encodedTick}_${blockHeight}`;
        cacheManager.caches.set(cacheKey, {
          data: apiData,
          timestamp: now
        });

        //console.log(`getHolder: API请求成功 tick=${tick}, block_height=${blockHeight}, holders=${apiData.result.total}`);
        return res.json(apiData);
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (error) {
      //console.error(`getHolder: API请求失败 tick=${tick}, error=${error.message}`);

      // API失败时，尝试返回任何可用的缓存数据
      const fallbackCacheKey = Array.from(cacheManager.caches.keys())
        .filter(key => key.startsWith(`getHolder_${encodedTick}_`))
        .sort((a, b) => {
          const heightA = parseInt(a.split('_')[2]) || 0;
          const heightB = parseInt(b.split('_')[2]) || 0;
          return heightB - heightA;
        })[0];

      if (fallbackCacheKey) {
        const fallbackCache = cacheManager.caches.get(fallbackCacheKey);
        if (fallbackCache && fallbackCache.data) {
          //console.log(`getHolder: API失败，返回备用缓存 tick=${tick}`);
          // 确保缓存数据包含正确的 tick 字段
          const cacheData = { ...fallbackCache.data };
          if (cacheData.result) {
            cacheData.result.tick = tick;
          }
          return res.json(cacheData);
        }
      }

      // 返回默认空数据
      return res.json({
        error: null,
        result: {
          unused_txes: [],
          block_height: global.latestBlockHeight,
          total: 0,
          decimals: 18,
          limit_per_mint: "1000",
          tick: tick
        }
      });
    }

  } catch (error) {
    console.error('Error in /v1/getHolder:', error.message);
    return res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// 查询当前账户可Mint数量
router.post('/v1/mintable', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { 
      wif, 
      feeRate, 
      maxFee, 
      orditext, 
      inscriptionSize, 
      protect, 
      receiveAddresses, 
      addressType, 
      count,
      mintHexData = "",
      Lowfee = false
    } = JSON.parse(decryptedData);

    // 构建参数数组
    const params = [
      wif, 
      feeRate, 
      maxFee, 
      orditext, 
      inscriptionSize, 
      parseInt(protect), 
      receiveAddresses, 
      addressType, 
      count,
      mintHexData,
      Lowfee
    ];

    const mintable = await utils.getMintableCount(...params);
    res.json(mintable);
  } catch (error) {
    console.error('Error in /v1/mintable:', error.message);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 添加一个简单的内存缓存用于去重
const mintRequestCache = new Map();
const MINT_CACHE_EXPIRY = 3000; // 3秒

// 清理过期的缓存条目
setInterval(() => {
  const now = Date.now();
  for (const [key, { timestamp }] of mintRequestCache.entries()) {
    if (now - timestamp > MINT_CACHE_EXPIRY) {
      mintRequestCache.delete(key);
    }
  }
}, 60000); // 每分钟检查一次

// 修改 /v1/mint 路由处理函数
router.post('/v1/mint', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const mintParams = JSON.parse(decryptedData);
    
    // 创建请求指纹
    const requestFingerprint = JSON.stringify({
      wif: mintParams.wif.substring(0, 8) + '...', // 只使用WIF的开头部分
      address: mintParams.receiveAddresses,
      count: mintParams.count,
      timestamp: Math.floor(Date.now() / MINT_CACHE_EXPIRY) // 向下取整到最近的3秒
    });
    
    // 检查是否是重复请求
    if (mintRequestCache.has(requestFingerprint)) {
      console.log("检测到重复的铸造请求，使用缓存结果");
      return res.json(mintRequestCache.get(requestFingerprint).result);
    }
    
    const { wif, inscriptionSize, protect, orditext, tick, receiveAddresses, feeRate, maxFee, count, activeUtxoi, addressType, RescueMode, runes = "", mintHexData = "", Lowfee = false } = mintParams;
    const mintRequestParams = [
      wif,
      inscriptionSize,
      parseInt(protect),
      orditext,
      tick,
      receiveAddresses,
      feeRate,
      maxFee,
      count,
      activeUtxoi,
      addressType,
      RescueMode,
      Lowfee,
      runes,
      mintHexData
    ];

    console.log(`开始处理铸造请求: ${receiveAddresses} 数量: ${count}`);
    
    // 使用线程池执行铸造操作
    const result = await pool.exec('mintInscriptions', mintRequestParams);
    
    // 缓存结果
    mintRequestCache.set(requestFingerprint, {
      result,
      timestamp: Date.now()
    });
    
    res.json(result);
  } catch (error) {
    console.error('Error in /v1/mint:', error.message);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取钱包信息
router.post('/v1/wallet-info', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { wif, addressType, Lowfee = false } = JSON.parse(decryptedData);
    const { address, balance } = await utils.getWalletInfo(wif, addressType, Lowfee);
    const oguser = parseFloat(utils.queryWallet(address)) <= 0.8 ? true : false;
    res.json({ address, balance, oguser });
  } catch (error) {
    console.error('Error in /v1/wallet-info:', error.message);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取钱包UTXO
router.post('/v1/wallet-utxo', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    
    const {time, address, Lowfee = false} = JSON.parse(decryptedData);
    //console.log(time, address);
    if (isValidTimestamp(time)) {
      const utxos = await utils.walletUtxo(address, Lowfee);
      res.json(utxos);
    } else {
      res.status(400).json({ error: 'Invalid timestamp' });
    }
  } catch (error) {
    console.error('Error in /v1/wallet-utxo:', error.message);
    res.status(500).json({ code: 1, msg: 'Internal server error' });
  }
});

// UTXO拆分
router.post('/v1/split-utxo', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { data } = JSON.parse(decryptedData);
    const { wif, addressType, utxos, splitCount, feeRate, Lowfee = false } = data;

    // 使用线程池执行UTXO拆分操作
    const result = await pool.exec('splitUtxo', [
      wif,
      addressType,
      utxos,
      splitCount,
      feeRate,
      Lowfee
    ]);

    // 确保返回格式与原来保持一致
    if (result) {
      res.json({
        code: 1,
        txid: result
      });
    } else {
      throw new Error('Split UTXO failed');
    }
  } catch (error) {
    console.error('Error in /v1/split-utxo:', error);
    const errorMessage = error.message || JSON.stringify(error);
    
    // 保持与原来相同的错误处理逻辑
    if (errorMessage.includes('insufficient fee')) {
      res.status(500).json({ error: 'insufficient fee' });
    } else if (errorMessage.includes('Fee exceeds maximum')) {
      res.status(500).json({ error: 'Fee exceeds maximum' });
    } else if (errorMessage.includes('bad-txns-inputs-missingorspent')) {
      res.status(500).json({ error: 'bad-txns-inputs-missingorspent' });
    } else if (errorMessage.includes('Insufficient UTXO value to cover transaction fee')) {
      res.status(500).json({ error: 'Insufficient UTXO value to cover transaction fee' });
    } else {
      res.status(500).json({ error: 'Internal server error', details: errorMessage });
    }
  }
});

// 加速
router.post('/v1/accelerate', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const data = JSON.parse(decryptedData);
    
    /*
    console.log('=== 加速API接收到的原始数据 ===');
    console.log('数据类型:', Array.isArray(data) ? '数组' : '单个对象');
    console.log('数据内容:', JSON.stringify(data, null, 2));
    */

    // 检查是否为批量请求
    const transactions = Array.isArray(data) ? data : [data];
     /*
    console.log('处理的交易数量:', transactions.length);
    // 详细检查每个交易的参数
    transactions.forEach((tx, index) => {
      console.log(`=== 交易 ${index + 1} 参数详情 ===`);
      console.log('wif:', tx.wif ? '已提供' : '未提供');
      console.log('feeRate:', tx.feeRate, '类型:', typeof tx.feeRate);
      console.log('initialFee:', tx.initialFee, '类型:', typeof tx.initialFee);
      console.log('Lowfee:', tx.Lowfee, '类型:', typeof tx.Lowfee);
      console.log('txid:', tx.txid);
      console.log('vout:', tx.vout);
      console.log('value:', tx.value);
      console.log('tick:', tx.tick);
      console.log('runes:', tx.runes);
      console.log('mintHexData:', tx.mintHexData);
      console.log('addressType:', tx.addressType);
      console.log('sentCount:', tx.sentCount);
    });
    */
    
    try {
      const results = await utils.accelerateTransaction(transactions);
      res.json(results);
    } catch (error) {
      // 将错误转换为前端期望的格式
      const errorResults = transactions.map((_, index) => ({
        tx: {
          ...transactions[index],
          lastTx: {
            status: "failed"
          }
        },
        index,
        success: false,
        error: error.message || "Internal server error"
      }));
      res.json(errorResults);
    }
  } catch (error) {
    console.error('Error in /v1/accelerate:', error.message);
    res.status(500).json([{
      tx: null,
      index: 0,
      success: false,
      error: error.message || "Internal server error"
    }]);
  }
});

// 获取未确认订单
router.post('/v1/pending-orders', async (req, res) => {
  res.json({});
  /*
  try {
    
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { wif, addressType, utxois } = JSON.parse(decryptedData);
    if (wif.length == 52) {
      const orders = await utils.pending_orders(wif, addressType, utxois);
      const orderDataList = orders.rows.map(order => order.data);
      res.json(orderDataList);
    } else {
      res.json({});
    }
  } catch (error) {
    console.error('获取未确认订单失败:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
  */
});

// 更新已确认订单的状态
router.post('/v1/update-order-status', async (req, res) => {
  res.json({});
  /*
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { utxois, del = 0 } = JSON.parse(decryptedData);
    await utils.update_order_status(utxois, del);
    res.json({});
  } catch (error) {
    console.error('更新订单状态失败:', error.message);
    res.status(500).json({ error: 'Internal server error' });
  }
  */
});

// 检测热门BRC20铸造活动
function checkFastGrowingBrc20() {
  // 从缓存管理器获取默认的mints_hot数据 (Lowfee=false)
  const mintsHotCache = cacheManager.caches.get('mints_hot_false');
  if (!mintsHotCache || !mintsHotCache.data || !mintsHotCache.data.results || !Array.isArray(mintsHotCache.data.results)) {
    return;
  }
  
  const currentTime = Date.now();
  
  // 遍历热门铸造BRC20列表
  mintsHotCache.data.results.forEach(async item => {
    const { tick, total_txs, unique_addresses } = item;
    
    // 初始化跟踪数据
    if (!brc20TrackingData[tick]) {
      brc20TrackingData[tick] = [];
    }
    
    // 记录当前状态
    brc20TrackingData[tick].push({ 
      time: currentTime, 
      total: total_txs 
    });
    
    // 保持最近10次的检测记录
    if (brc20TrackingData[tick].length > 10) {
      brc20TrackingData[tick].shift();
    }
    
    // 检查变化率，只有可能符合条件的才继续处理
    const initialTotal = brc20TrackingData[tick][0].total;
    const change = total_txs - initialTotal;
    
    // 如果变化不足100，很可能不需要推送，直接跳过后续处理
    if (change < 100 && unique_addresses < 2) {
      return;
    }
    
    // 获取当前BRC20的提醒级别
    const existingEntry = fastGrowingBrc20.find(entry => entry.tick === tick);
    const currentLevel = existingEntry ? existingEntry.level : 0;

    // 使用缓存数据获取铭文基础信息 (使用默认的Lowfee=false)
    const cacheKey = `getRuneInfo_${encodeURIComponent(tick.toLowerCase())}_false`;
    const brc20Cache = cacheManager.caches.get(cacheKey);
    
    // 如果没有缓存数据，跳过处理，等待缓存建立
    if (!brc20Cache || !brc20Cache.data) {
      return;
    }
    
    const brc20Data = brc20Cache.data;
    const supply = parseInt(brc20Data.supply || "0");
    const limitPerMint = parseInt(brc20Data.limitPerMint || "0");
    
    // 计算总张数上限 (供应量/每次铸造量)
    const totalMints = limitPerMint > 0 ? Math.floor(supply / limitPerMint) : 0;
    
    // 新规则：只推送张数上限大于900张的BRC20
    if (totalMints <= 900) {
      return;
    }
    
    // 增加筛选条件：内存池数量必须大于500，参与人数必须大于2
    if ((totalMints <= 1500 && total_txs <= 200) || (totalMints > 1500 && total_txs <= 500) || unique_addresses <= 2) {
      return;
    }
    
    let shouldNotify = false;
    let newLevel = currentLevel;
    
    // 对于上限在900-1500张之间的铭文，按照进度比例推送
    if (totalMints > 900 && totalMints <= 1500) {
      const progressPercentage = (change / totalMints) * 100;
      
      // 第三级提醒：进度达到60%
      if (progressPercentage >= 60 && currentLevel < 3) {
        newLevel = 3;
        shouldNotify = true;
      }
      // 第二级提醒：进度达到45%
      else if (progressPercentage >= 45 && currentLevel < 2) {
        newLevel = 2;
        shouldNotify = true;
      }
      // 第一级提醒：进度达到30%
      else if (progressPercentage >= 30 && currentLevel < 1) {
        newLevel = 1;
        shouldNotify = true;
      }
    } 
    // 对于上限大于1500张的铭文，按照绝对数量推送
    else if (totalMints > 1500) {
      // 第三级提醒：总数达到2000张
      if (change >= 2000 && currentLevel < 3) {
        newLevel = 3;
        shouldNotify = true;
      }
      // 第二级提醒：总数达到1000张
      else if (change >= 1000 && currentLevel < 2) {
        newLevel = 2;
        shouldNotify = true;
      }
      // 第一级提醒：总数达到500张
      else if (change >= 500 && currentLevel < 1) {
        newLevel = 1;
        shouldNotify = true;
      }
    }
    
    // 如果需要发送提醒，才去获取最新的详细数据
    if (shouldNotify) {
      try {
        // 获取最新的详细信息
        const encodedTick = encodeURIComponent(tick.toLowerCase());
        const response = await axios.get(
          `http://***********:5002/api/brc20/tick/${encodedTick}/info`,  //http://***********:5002/   http://*************:3344
          { timeout: 5000 }
        );
        
        if (response.status === 200 && response.data && response.data.inscriptionId) {
          // 使用最新获取的数据重新计算
          const latestData = response.data;
          const latestSupply = parseInt(latestData.supply || "0");
          const latestLimitPerMint = parseInt(latestData.limitPerMint || "0");
          const latestMinted = parseInt(latestData.minted || "0");
          
          // 重新计算以确保使用最新数据
          const latestTotalMints = latestLimitPerMint > 0 ? Math.floor(latestSupply / latestLimitPerMint) : 0;
          const latestMintedCount = latestLimitPerMint > 0 ? Math.floor(latestMinted / latestLimitPerMint) : 0;
          
          // 使用最新数据重新检查是否仍然满足推送条件
          let stillShouldNotify = false;
          
          if (latestTotalMints > 900 && latestTotalMints <= 1500) {
            const latestProgressPercentage = (change / latestTotalMints) * 100;
            stillShouldNotify = (newLevel === 3 && latestProgressPercentage >= 60) ||
                               (newLevel === 2 && latestProgressPercentage >= 45) ||
                               (newLevel === 1 && latestProgressPercentage >= 30);
          } else if (latestTotalMints > 1500) {
            stillShouldNotify = (newLevel === 3 && change >= 2000) ||
                               (newLevel === 2 && change >= 1000) ||
                               (newLevel === 1 && change >= 500);
          }
          
          if (stillShouldNotify) {
            // 准备推送数据
            const brc20Info = {
              tick: tick,
              limitPerMint: latestData.limitPerMint || "0",
              supply: latestData.supply || "0",
              minted: latestData.minted || "0",
              total_txs: total_txs,
              unique_addresses: unique_addresses,
              change: change,
              level: newLevel
            };
            
            // 推送电报信息
            sendTelegramNotification(brc20Info);
            
            // 更新或添加到fastGrowingBrc20数组
            if (existingEntry) {
              existingEntry.level = newLevel;
            } else {
              fastGrowingBrc20.push({ tick, level: newLevel });
            }
            
            // 保存数据
            saveDataToFile(fastGrowingBrc20FilePath, fastGrowingBrc20);
          }
        }
      } catch (error) {
        console.error(`获取BRC20 ${tick}最新信息失败:`, error.message);
      }
    }
  });
  
  // 保存跟踪数据
  saveDataToFile(brc20TrackingDataFilePath, brc20TrackingData);
}

// 加载初始数据
brc20TrackingData = loadInitialData(brc20TrackingDataFilePath, brc20TrackingData);
fastGrowingBrc20 = loadInitialData(fastGrowingBrc20FilePath, fastGrowingBrc20);

// 立即获取mempool数据
mempool_blocks();
// 设置定时器，每25秒获取一次
setInterval(mempool_blocks, 25000);

// 立即获取最新区块高度
fetchAndUpdateData();
// 设置定时器，每30秒获取一次最新区块高度
setInterval(fetchAndUpdateData, 30000);

// 设置定期检测
setInterval(checkFastGrowingBrc20, 60000); // 每分钟检测一次

// 在文件末尾添加清理代码
process.on('SIGINT', () => {
  pool.terminate(); // 优雅地终止线程池
  process.exit();
});

module.exports = router;
const fs = require('fs');
const axios = require('axios');
const ini = require('ini');
const CryptoJS = require('crypto-js');
const moment = require('moment-timezone');

const API_ENCRYPTION_KEY = '1726979883';

function encryptData(data) {
  return CryptoJS.AES.encrypt(JSON.stringify(data), API_ENCRYPTION_KEY).toString();
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function getFeeRate() {
  try {
    const response = await axios.get('http://**************:1080/api/v1/fees/recommended');
    return response.data.halfHourFee;
  } catch (error) {
    console.error(`获取费率时出错: ${error.message}`);
    return null;
  }
}

async function getMintableCount(config, feeRate) {
  try {
    // 确保 receiveAddresses 是一个包含单个地址字符串的数组
    let receiveAddresses;
    if (typeof config.receiveAddresses === 'string') {
      receiveAddresses = config.receiveAddresses.replace(/[\[\]']/g, '').split(',');
    } else if (Array.isArray(config.receiveAddresses)) {
      receiveAddresses = config.receiveAddresses.map(addr => addr.replace(/[\[\]']/g, ''));
    } else {
      throw new Error('Invalid receiveAddresses format');
    }

    const data = {
      wif: config.wif,
      feeRate: feeRate,
      maxFee: feeRate,
      orditext: config.orditext,
      inscriptionSize: parseInt(config.inscriptionSize),
      protect: 1000,
      receiveAddresses: receiveAddresses,
      addressType: config.addressType,
      count: parseInt(config.count)
    };

    const encryptedData = encryptData(data);
    const timestamp = Date.now();

    const response = await axios.post(`http://127.0.0.1/api/v1/mintable?t=${timestamp}`, 
      { data: encryptedData },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 60000 // 60 seconds timeout
      }
    );

    return response.data;
  } catch (error) {
    if (error.response) {
      console.error(`获取可铸造数量失败. 状态码: ${error.response.status}`);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('未收到响应. 请求详情:', error.request);
    } else {
      console.error('请求设置时出错:', error.message);
    }
    return null;
  }
}

async function mintInscriptions(config, feeRate) {
  try {
    const tick = JSON.parse(config.orditext).tick;
    
    // 确保 receiveAddresses 是一个包含单个地址字符串的数组
    let receiveAddresses;
    if (typeof config.receiveAddresses === 'string') {
      receiveAddresses = config.receiveAddresses.replace(/[\[\]']/g, '').split(',');
    } else if (Array.isArray(config.receiveAddresses)) {
      receiveAddresses = config.receiveAddresses.map(addr => addr.replace(/[\[\]']/g, ''));
    } else {
      throw new Error('Invalid receiveAddresses format');
    }

    const data = {
      wif: config.wif,
      inscriptionSize: parseInt(config.inscriptionSize),
      protect: 1000,
      orditext: config.orditext,
      tick: tick,
      receiveAddresses: receiveAddresses,
      feeRate: feeRate,
      maxFee: feeRate,
      count: parseInt(config.count),
      activeUtxoi: Date.now(),
      addressType: config.addressType,
      RescueMode: false
    };

    const encryptedData = encryptData(data);
    const timestamp = Date.now();

    const response = await axios.post(`http://127.0.0.1/api/v1/mint?t=${timestamp}`, 
      { data: encryptedData },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 600000 // 10 minutes timeout
      }
    );

    return response.data;
  } catch (error) {
    if (error.response) {
      console.error(`铸造铭文失败. 状态码: ${error.response.status}`);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('未收到响应. 请求详情:', error.request);
    } else {
      console.error('请求设置时出错:', error.message);
    }
    return null;
  }
}

async function main() {
  try {
    console.log('正在读取配置文件...');
    const config = ini.parse(fs.readFileSync('./config.ini', 'utf-8')).DEFAULT;
    config.receiveAddresses = config.receiveAddresses.replace(/[\[\]']/g, '').split(',').map(addr => addr.trim()).filter(addr => addr);
    console.log('配置加载成功。');

    let totalMintedOverall = 0;

    function getBeijingTime() {
      return moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
    }

    while (true) {
      console.log(`\n${getBeijingTime()} - 开始新的铸造周期`);

      // 步骤1：检查费率
      console.log('正在检查当前费率...');
      let feeRate = await getFeeRate();
      while (feeRate === null || feeRate > config.maxfee) {
        if (feeRate === null) {
          console.log('获取费率失败。20秒后重试...');
        } else {
          console.log(`当前费率 (${feeRate}) 高于最大费率 (${config.maxfee})。等待20秒...`);
        }
        await sleep(20000);
        feeRate = await getFeeRate();
      }
      feeRate = Math.max(feeRate, config.minfee);
      console.log(`使用费率: ${feeRate}`);

      // 步骤2：检查可铸造数量
      console.log('正在检查可铸造数量...');
      const mintableData = await getMintableCount(config, feeRate);
      if (!mintableData) {
        console.log('获取可铸造数量失败。1分钟后重试...');
        await sleep(60000);
        continue;
      }
      console.log(`可铸造数量: ${mintableData.maxMintCount}`);
      if (mintableData.maxMintCount < config.mincount) {
        console.log(`可铸造数量 (${mintableData.maxMintCount}) 少于最小要求 (${config.mincount})。等待1分钟...`);
        await sleep(60000);
        continue;
      }

      // 步骤3：铸造铭文
      console.log('开始铸造过程...');
      const mintResult = await mintInscriptions(config, feeRate);
      if (!mintResult) {
        console.log('铸造失败。1分钟后重试...');
        await sleep(60000);
        continue;
      }

      // 处理并记录结果
      const successfulMints = mintResult.filter(result => result.totalCount);
      const totalMinted = successfulMints.reduce((sum, result) => sum + result.totalCount, 0);
      totalMintedOverall += totalMinted;

      console.log(`铸造完成于 ${getBeijingTime()}`);
      console.log(`使用的费率: ${feeRate}`);
      console.log(`本轮铸造的铭文数: ${totalMinted}`);
      console.log(`累计总铸造数量: ${totalMintedOverall}`);
      console.log(`本轮成功的铸造: ${successfulMints.length}`);
      console.log(`本轮失败的铸造: ${mintResult.length - successfulMints.length}`);

      console.log('等待1分钟后开始下一个周期...');
      await sleep(60000);
    }
  } catch (error) {
    console.error(`主循环中出现意外错误: ${error.message}`);
    console.log('1分钟后重启主循环...');
    await sleep(60000);
    main();
  }
}

main();
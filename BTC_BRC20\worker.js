const workerpool = require('workerpool');
const { mintInscriptions, splitutxo } = require('./brc20.js');

function workerMintInscriptions(wif, inscriptionSize, protect, orditext, tick, receiveAddresses, feeRate, maxFee, count, activeUtxoi, addressType, RescueMode, Lowfee = false, runes = "", mintHexData = "") {
  // 构建参数数组，确保参数顺序与 brc20.js 中 mintInscriptions 函数的参数顺序一致
  // mintInscriptions(wif, inscriptionSize, protect, orditext, tick, receiveAddresses, feeRate, maxFee, count, activeUtxoi, addressType, RescueMode, runes, mintHexData, Lowfee)
  const params = [
    wif,
    inscriptionSize,
    parseInt(protect),
    orditext,
    tick,
    receiveAddresses,
    feeRate,
    maxFee,
    count,
    activeUtxoi,
    addressType,
    RescueMode,
    runes,
    mintHexData,
    Lowfee
  ];

  // 将参数展开传递给 mintInscriptions
  return mintInscriptions(...params);
}

function workerSplitUtxo(wif, addressType, utxos, splitCount, feeRate, Lowfee = false) {
  return splitutxo(wif, addressType, utxos, splitCount, feeRate, Lowfee);
}

// 注册 worker 函数
workerpool.worker({
  mintInscriptions: workerMintInscriptions,
  splitUtxo: workerSplitUtxo
}); 
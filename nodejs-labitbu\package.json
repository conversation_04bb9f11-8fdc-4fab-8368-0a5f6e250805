{"name": "nodejs-labitbu", "version": "1.0.0", "description": "Node.js implementation of labitbu minting functionality", "main": "index.js", "scripts": {"start": "node index.js", "test": "node test.js"}, "dependencies": {"@cmdcode/tapscript": "^1.4.6", "axios": "^1.6.2", "bs58": "^6.0.0", "crypto": "^1.0.1", "sharp": "^0.33.4", "canvas": "^2.11.2", "dotenv": "^16.4.5"}, "keywords": ["bitcoin", "taproot", "nft", "labitbu"], "author": "", "license": "MIT"}
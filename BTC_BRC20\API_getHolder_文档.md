# /v1/getHolder API 接口文档

## 接口概述
获取指定 BRC20 代币的持有者信息，支持智能缓存和防重复请求机制。

## 请求信息
- **URL**: `/v1/getHolder`
- **方法**: `POST`
- **Content-Type**: `application/json`

## 请求参数

### 请求体结构
```json
{
  "data": "加密后的数据字符串"
}
```

### 加密前的数据结构
```json
{
  "tick": "代币标识符",
  "height": 区块高度,
  "time": 时间戳
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tick | string | 是 | BRC20 代币标识符，如 "ordi" |
| height | number | 否 | 区块高度，默认为 0 |
| time | number | 是 | 客户端时间戳，用于验证请求有效性 |

### height 参数逻辑
- **height = 0**: 前端无数据，需要全量输出
- **height > 0 且 height ≠ 最新区块高度**: 优先返回对应高度的缓存数据
- **height = 最新区块高度**: 直接返回成功，unused_txes 为空数组（节省流量）

## 响应格式

### 成功响应
```json
{
  "error": null,
  "result": {
    "unused_txes": [
      {
        "wallet": "******************************************",
        "overall_balance": "785000",
        "available_balance": "545000"
      }
    ],
    "block_height": 906291,
    "total": 226,
    "decimals": 18,
    "limit_per_mint": "1000"
  }
}
```

### 错误响应
```json
{
  "error": "错误信息"
}
```

## 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| error | string/null | 错误信息，成功时为 null |
| result.unused_txes | array | 持有者列表 |
| result.unused_txes[].wallet | string | 钱包地址 |
| result.unused_txes[].overall_balance | string | 总余额 |
| result.unused_txes[].available_balance | string | 可用余额 |
| result.block_height | number | 数据对应的区块高度 |
| result.total | number | 持有者总数 |
| result.decimals | number | 代币精度 |
| result.limit_per_mint | string | 每次铸造限制 |

## 请求示例

### JavaScript 示例
```javascript
// 加密函数（需要 crypto-js 库）
function encryptData(data, key = 'ybot_1999eth_x_com_202405101298') {
  return CryptoJS.AES.encrypt(data, key).toString();
}

// 构建请求数据
const requestData = {
  tick: "ordi",
  height: 0,
  time: Date.now()
};

// 加密数据
const encryptedData = encryptData(JSON.stringify(requestData));

// 发送请求
fetch('/v1/getHolder', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    data: encryptedData
  })
})
.then(response => response.json())
.then(data => {
  console.log('持有者信息:', data);
})
.catch(error => {
  console.error('请求失败:', error);
});
```

### cURL 示例
```bash
# 注意：实际使用时需要先加密数据
curl -X POST http://localhost:3000/v1/getHolder \
  -H "Content-Type: application/json" \
  -d '{
    "data": "加密后的数据字符串"
  }'
```

## 缓存策略

### 智能缓存机制
1. **防重复请求**: 同一个 tick 至少间隔 30 秒才能再次请求外部 API
2. **智能缓存使用**: 优先使用 block_height >= 当前最新区块高度的缓存数据
3. **多级降级策略**: API 失败时按优先级返回可用缓存数据

### 缓存键名规则
```
getHolder_{encodedTick}_{block_height}
```

## 错误码说明
| HTTP状态码 | 错误信息 | 说明 |
|------------|----------|------|
| 400 | Missing encrypted data | 缺少加密数据 |
| 400 | Invalid encrypted data | 加密数据无效 |
| 400 | Invalid tick parameter | tick 参数无效 |
| 400 | Invalid timestamp | 时间戳无效（超过5分钟） |
| 500 | Internal server error | 服务器内部错误 |

## 性能优化特性

### 1. 流量节省
- 当 height 等于最新区块高度时，返回空的 unused_txes 数组
- 减少不必要的数据传输

### 2. API 保护
- 防重复请求机制，避免对索引 API 造成过大负担
- 30 秒间隔限制，合理控制请求频率

### 3. 缓存优化
- 智能缓存策略，最大化缓存利用率
- 定期清理过期缓存，避免内存泄漏
- getHolder 缓存保留 5 分钟，平衡性能和数据新鲜度

## 注意事项

1. **时间戳验证**: 客户端时间戳与服务器时间差不能超过 5 分钟
2. **数据加密**: 所有请求数据必须使用指定密钥加密
3. **请求频率**: 建议合理控制请求频率，避免触发防重复请求机制
4. **错误处理**: 建议实现完善的错误处理和重试机制

## 更新日志
- **v1.0**: 初始版本，支持基础持有者查询
- **v1.1**: 添加智能缓存和防重复请求机制
- **v1.2**: 优化缓存策略，添加流量节省功能

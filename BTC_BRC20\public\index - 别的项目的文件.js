/**
 * BTC Alkanes 批量铸造工具 - 前端主文件
 * 重构版本 - 模块化结构，保持原有功能不变
 */

// ==================== 常量定义 ====================
const CONFIG = {
    // 网络配置
    IS_TESTNET: window.env.TESTNET === "true",
    NETWORK_SUFFIX: window.env.TESTNET === "true" ? "_TEST" : "_Main",

    // 加密密钥
    STORAGE_ENCRYPTION_KEY: 'ybot_1999eth_x_com_20240509',
    API_ENCRYPTION_KEY: 'ybot_1999eth_x_com_202405101298',

    // 请求配置
    MAX_REQUESTS_PER_SECOND: 3,
    REQUEST_INTERVAL: 1000 / 3,

    // 默认值
    DEFAULT_INSCRIPTION_SIZE: 330,
    DEFAULT_PROTECT_VALUE: 10000,
    DEFAULT_ADDRESS_TYPE: "P2TR"
};

// ==================== 全局状态管理 ====================
const AppState = {
    // 价格和数据
    btcPrices: 0,
    uprunes: 0,
    runename: null,
    feeData: null,

    // 界面状态
    playsound: true,
    mintchange: false,
    Batchaccele: false,
    loading: false,

    // 交易相关
    utxoList: [],
    currentBlockHeight: null,
    uniqueRunes: new Set(['all']),
    lastCheckMintableTime: 0,

    // 请求队列
    queue: [],
    processing: false,

    // 界面选择
    currentSelectedId: "hot",

    // 时间同步
    timeOffset: null,

    // 图表相关
    prevMemsum: '',
    prevGassize: '',
    myChart: null,
    dataUpdateInterval: null
};

// 全局访问
window.myChart = null;

// ==================== 工具函数模块 ====================
const Utils = {
    // DOM缓存
    _domCache: new Map(),

    // 获取缓存的DOM元素
    $(selector) {
        if (!this._domCache.has(selector)) {
            this._domCache.set(selector, $(selector));
        }
        return this._domCache.get(selector);
    },

    // 基础工具函数
    sleep: ms => new Promise(resolve => setTimeout(resolve, ms)),
    getAPIUrl: () => CONFIG.IS_TESTNET ? "https://mempool.space/signet/" : "https://mempool.space/",
    encrypt: (data, key = CONFIG.STORAGE_ENCRYPTION_KEY) => CryptoJS.AES.encrypt(data, key).toString(),
    decrypt: (data, key = CONFIG.STORAGE_ENCRYPTION_KEY) => {
        if (!data) return "";
        return CryptoJS.AES.decrypt(data, key).toString(CryptoJS.enc.Utf8);
    },

    // 验证函数
    validateBitcoinAddress(address) {
        return /^(bc1|tb1)[a-zA-HJ-NP-Z0-9]{39,59}$/.test(address) &&
               [42, 62].includes(address.length) &&
               ['bc1', 'tb1'].includes(address.slice(0, 3));
    },

    validateWif(wif) {
        return wif && wif.length === 52 && ['5', 'L', 'K', 'c', '9'].some(p => wif.startsWith(p));
    },

    // UI工具函数
    showAlert(message) {
        const modal = this.$('#alertModal');
        this.$('#alertModalBody').html(message);
        modal.modal('show');

        const handleKeypress = (e) => {
            if (e.which === 13) {
                modal.modal('hide');
                $(document).off('keypress', handleKeypress);
            }
        };

        $(document).on('keypress', handleKeypress);
        modal.one('hidden.bs.modal', () => $(document).off('keypress', handleKeypress));
    },

    // 格式化工具
    formatAddress(address) {
        return address && address.length > 42 ?
            `${address.slice(0, 20)}...${address.slice(-20)}` : address;
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 验证 WIF 私钥
    validateWIF(wif) {
        return wif && wif.length === 52 && ['5', 'L', 'K', 'c', '9'].some(p => wif.startsWith(p));
    },

    // 验证地址（别名）
    validateAddress(address) {
        return this.validateBitcoinAddress(address);
    },

    // 格式化数字
    formatNumber(input) {
        if (input === undefined || input === null) {
            return '0';
        }
        const chineseNumbers = ["万", "亿"];
        const numStr = input.toString();

        // 检查输入是否含有中文数字
        if (chineseNumbers.some(char => numStr.includes(char))) {
            return this.parseChineseNumber(numStr);
        } else if (!isNaN(input) && input >= 10000) {
            return this.formatLargeNumber(input);
        } else {
            return numStr;
        }
    },

    // 解析中文数字
    parseChineseNumber(numStr) {
        let result = numStr;
        if (numStr.includes("万")) {
            result = parseFloat(numStr.replace("万", "")) * 10000;
        } else if (numStr.includes("亿")) {
            result = parseFloat(numStr.replace("亿", "")) * 100000000;
        }
        return result.toString();
    },

    // 格式化大数字
    formatLargeNumber(input) {
        const num = parseFloat(input);
        if (num >= 100000000) {
            return (num / 100000000).toFixed(2) + "亿";
        } else if (num >= 10000) {
            return (num / 10000).toFixed(2) + "万";
        } else {
            return num.toString();
        }
    }
};

// 为了兼容性，保留原函数名
const sleep = Utils.sleep;
const getAPIUrl = Utils.getAPIUrl;
const en1999_20240924 = Utils.encrypt;
const de1999_20240924 = Utils.decrypt;
const validateBitcoinAddress = Utils.validateBitcoinAddress;
const showAlert = Utils.showAlert;
const formatNumber = Utils.formatNumber.bind(Utils);
const validateWIF = Utils.validateWIF.bind(Utils);

// ==================== 时间同步模块 ====================
const TimeSync = {
    /**
     * 获取服务器时间并校正本地时间
     */
    async calibrateTime() {
        try {
            const response = await fetch('/api/v1/server_time');
            const data = await response.json();
            if (data.code === 0) {
                const serverTime = data.data;
                const localTime = Date.now();
                AppState.timeOffset = serverTime - localTime;
                localStorage.setItem('timeOffset', AppState.timeOffset);
            }
        } catch (error) {
            console.error('Error fetching server time:', error);
        }
    },

    /**
     * 获取校正后的时间戳
     * @returns {number} 校正后的时间戳
     */
    getSyncedTimestamp() {
        const localTime = Date.now();
        if (AppState.timeOffset !== null) {
            return localTime + AppState.timeOffset;
        } else {
            return localTime;
        }
    },

    /**
     * 初始化时间同步
     */
    initializeTimeSync() {
        const savedOffset = localStorage.getItem('timeOffset');
        if (savedOffset !== null) {
            AppState.timeOffset = parseInt(savedOffset, 10);
        } else {
            this.calibrateTime();
        }
    }
};

// 为了兼容性，保留原函数名
const calibrateTime = TimeSync.calibrateTime.bind(TimeSync);
const getSyncedTimestamp = TimeSync.getSyncedTimestamp.bind(TimeSync);
const initializeTimeSync = TimeSync.initializeTimeSync.bind(TimeSync);

// 初始化时间同步
TimeSync.initializeTimeSync();

// ==================== 存储管理模块 ====================
const StorageManager = {
    // 存储键名缓存
    _keyCache: new Map(),

    getStorageKey(key) {
        if (!this._keyCache.has(key)) {
            this._keyCache.set(key, key + CONFIG.NETWORK_SUFFIX);
        }
        return this._keyCache.get(key);
    },

    // 批量获取存储值
    getStorageValues(keys) {
        const result = {};
        keys.forEach(key => {
            const storageKey = this.getStorageKey(key);
            const value = localStorage.getItem(storageKey);
            result[key] = value ? Utils.decrypt(value) : null;
        });
        return result;
    },

    // 批量设置存储值
    setStorageValues(values) {
        Object.entries(values).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
                localStorage.setItem(this.getStorageKey(key), Utils.encrypt(value.toString()));
            }
        });
    },

    loadParams() {
        // 批量获取存储值
        const stored = this.getStorageValues([
            'wif', 'inscriptionSize', 'protect', 'mintHexData', 'runes',
            'receiveAddress', 'singleReceiveAddress', 'addressType',
            'walletAddress', 'walletBalance', 'btcPrices', 'mintFeeSplit'
        ]);

        // 设置默认值
        const params = {
            ...stored,
            inscriptionSize: stored.inscriptionSize || CONFIG.DEFAULT_INSCRIPTION_SIZE.toString(),
            protect: stored.protect || CONFIG.DEFAULT_PROTECT_VALUE,
            addressType: stored.addressType || CONFIG.DEFAULT_ADDRESS_TYPE,
            mintFeeSplit: stored.mintFeeSplit !== null ? stored.mintFeeSplit === 'true' : true // 默认开启
        };

        // URL参数处理
        if (AppState.btcPrices === 0) {
            const urlParams = new URLSearchParams(window.location.search);
            if (!urlParams.get('runeid') && !urlParams.get('runes')) {
                // 使用存储的值
            } else {
                params.mintHexData = params.runes = "";
            }
        }

        // 更新状态
        AppState.btcPrices = parseInt(params.btcPrices) || 0;

        // 设置符文信息
        if (params.runes && params.mintHexData && params.mintHexData.includes(":")) {
            const elements = {
                mintHexData: $('#mintHexData'),
                runes: $('#runes'),
                runeName: $('.rune-name'),
                runeQuantity: $('.rune-quantity'),
                runeInfo: $('#runeInfo')
            };

            elements.mintHexData.val(params.mintHexData);
            elements.runes.val(params.runes);
            elements.runeName.html(`代币ID: ${params.mintHexData}`);
            elements.runeQuantity.html(`点击刷新查看进度`);
            elements.runeInfo.css('display', 'flex');
            AppState.runename = params.runes;
            elements.runes.trigger('change');
        }

        // 批量设置界面值
        const uiElements = {
            '#walletAddress': params.walletAddress,
            '#walletBalance': params.walletBalance,
            '#inscriptionSize': params.inscriptionSize,
            '#protect': params.protect,
            '#receiveAddress': params.receiveAddress || '',
            '#singleReceiveAddress': params.singleReceiveAddress || '',
            '#feeRate': 0,
            '#maxFee': 0,
            '#addressType': params.addressType,
            '#wif': params.wif || ''
        };

        Object.entries(uiElements).forEach(([selector, value]) => {
            const element = $(selector);
            if (element.is('input, select, textarea')) {
                element.val(value);
            } else {
                element.text(value);
            }
        });

        // 设置 mintFeeSplit 复选框状态
        $('#mintFeeSplit').prop('checked', params.mintFeeSplit);

        if (params.receiveAddress && window.updateAddressCount) {
            window.updateAddressCount();
        }
    },

    async saveParams() {
        console.log("保存");
        limitInscriptionSize();

        // 批量获取表单值
        const formValues = {
            wif: $('#wif').val().trim(),
            inscriptionSize: $('#inscriptionSize').val().trim(),
            protect: $('#protect').val(),
            singleReceiveAddress: $('#singleReceiveAddress').val().trim(),
            receiveAddress: $('#receiveAddress').val().trim(),
            feeRate: $('#feeRate').val().trim(),
            maxFee: $('#maxFee').val().trim(),
            addressType: $('#addressType').val(),
            mintFeeSplit: $('#mintFeeSplit').prop('checked')
        };

        const mintHexData = $('#mintHexData').val().trim();
        const runes = $('#runes').val().trim();

        // 处理符文数据
        if (runes.length > 0 && mintHexData.includes(":")) {
            AppState.runename = runes;
            formValues.runes = runes;
            formValues.mintHexData = mintHexData;
        }

        // 批量保存
        this.setStorageValues(formValues);
    }
};

// 为了兼容性，保留原函数名
const loadParams = StorageManager.loadParams.bind(StorageManager);
const saveParams = StorageManager.saveParams.bind(StorageManager);

// ==================== 网络管理模块 ====================
const NetworkManager = {
    // 请求队列
    queue: [],
    processing: false,
    maxRequestsPerSecond: CONFIG.MAX_REQUESTS_PER_SECOND,
    requestInterval: CONFIG.REQUEST_INTERVAL,

    /**
     * 添加请求到队列
     * @param {Function} requestFunction 请求函数
     * @returns {Promise} 请求结果
     */
    addToQueue(requestFunction) {
        return new Promise((resolve, reject) => {
            this.queue.push({
                request: requestFunction,
                resolve,
                reject
            });

            if (!this.processing) {
                this.processQueue();
            }
        });
    },

    /**
     * 处理请求队列
     */
    async processQueue() {
        if (this.processing || this.queue.length === 0) {
            return;
        }

        this.processing = true;

        while (this.queue.length > 0) {
            const { request, resolve, reject } = this.queue.shift();

            try {
                const result = await request();
                resolve(result);
            } catch (error) {
                reject(error);
            }

            // 控制请求频率
            if (this.queue.length > 0) {
                await Utils.sleep(this.requestInterval);
            }
        }

        this.processing = false;
    },

    /**
     * 清空队列
     */
    clearQueue() {
        this.queue.forEach(({ reject }) => {
            reject(new Error('Queue cleared'));
        });
        this.queue = [];
        this.processing = false;
    },

    /**
     * 获取队列状态
     */
    getQueueStatus() {
        return {
            queueLength: this.queue.length,
            processing: this.processing
        };
    }
};

// ==================== 剪贴板管理模块 ====================
const ClipboardManager = {
    /**
     * 复制文本到剪贴板
     * @param {string} text 要复制的文本
     */
    async copyToClipboard(text) {
        try {
            // 优先使用现代 Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
            } else {
                // 降级到传统方法
                const $textArea = $('<textarea>')
                    .css({
                        position: 'fixed',
                        opacity: '0',
                        left: '-9999px'
                    })
                    .val(text)
                    .appendTo('body');

                $textArea[0].select();
                document.execCommand('copy');
                $textArea.remove();
            }
        } catch (error) {
            console.error('复制失败:', error);
            throw error;
        }
    },

    /**
     * 显示复制成功通知
     */
    showCopyNotification() {
        const $notification = $('<div>')
            .addClass('copy-notification')
            .text('已复制到剪切板，快分享给您的朋友吧！')
            .appendTo('body');

        setTimeout(() => {
            $notification.css('opacity', '0');
            setTimeout(() => {
                $notification.remove();
            }, 500);
        }, 1500);
    }
};

// ==================== 主题管理模块 ====================
const ThemeManager = {
    $darkModeToggle: null,
    $darkModeCSS: null,

    init() {
        const $darkModeToggle = $('#darkModeToggle');
        const $darkModeCSS = $('<link>')
            .attr({
                rel: 'stylesheet',
                href: 'dark-mode.css?01'
            })
            .prop('disabled', true)
            .appendTo('head');

        this.$darkModeToggle = $darkModeToggle;
        this.$darkModeCSS = $darkModeCSS;

        // 绑定事件
        $darkModeToggle.on('change', () => {
            const isDarkModeEnabled = $darkModeToggle.prop('checked');
            this.toggleDarkMode(isDarkModeEnabled);
            localStorage.setItem('darkMode', isDarkModeEnabled ? 'true' : 'false');
        });

        // 页面加载时检查用户之前的主题选择
        $(() => {
            const darkModeEnabled = localStorage.getItem('darkMode') === 'true';
            $darkModeToggle.prop('checked', darkModeEnabled);
            this.toggleDarkMode(darkModeEnabled);
        });
    },

    toggleDarkMode(enabled) {
        if (this.$darkModeCSS) {
            this.$darkModeCSS.prop('disabled', !enabled);
        }
        $('body').toggleClass('dark-mode', enabled);

        // 更新导航栏样式
        $('.navbar').toggleClass('navbar-dark bg-dark', enabled);

        // 更新导航栏链接样式
        $('.navbar-link').toggleClass('text-light', enabled);

        // 更新下拉菜单样式
        $('.dropdown-menu').toggleClass('bg-dark dropdown-menu-dark', enabled);

        // 更新钱包图标样式
        $('.wallet-icon').toggleClass('text-light', enabled);
    }
};

// 为了兼容性，保留原函数名
const copyToClipboard = ClipboardManager.copyToClipboard.bind(ClipboardManager);
const showCopyNotification = ClipboardManager.showCopyNotification.bind(ClipboardManager);

// 初始化主题管理器
ThemeManager.init();

// ==================== 移动端菜单管理模块 ====================
const MobileMenuManager = {
    /**
     * 初始化移动端菜单
     */
    init() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuToggle && mobileMenu) {
            // 汉堡菜单按钮点击事件
            mobileMenuToggle.addEventListener('click', () => {
                this.toggleMenu(mobileMenuToggle, mobileMenu);
            });

            // 点击菜单外部时关闭菜单
            document.addEventListener('click', (event) => {
                this.handleOutsideClick(event, mobileMenuToggle, mobileMenu);
            });

            // 点击菜单链接时关闭菜单
            this.bindMenuLinks(mobileMenu, mobileMenuToggle);
        }
    },

    /**
     * 切换菜单显示状态
     * @param {HTMLElement} toggle 汉堡菜单按钮
     * @param {HTMLElement} menu 移动端菜单
     */
    toggleMenu(toggle, menu) {
        // 切换汉堡菜单按钮的动画状态
        toggle.classList.toggle('active');

        // 切换移动端菜单的显示状态
        if (menu.style.display === 'block') {
            menu.style.display = 'none';
        } else {
            menu.style.display = 'block';
        }
    },

    /**
     * 处理点击菜单外部的事件
     * @param {Event} event 点击事件
     * @param {HTMLElement} toggle 汉堡菜单按钮
     * @param {HTMLElement} menu 移动端菜单
     */
    handleOutsideClick(event, toggle, menu) {
        if (!toggle.contains(event.target) && !menu.contains(event.target)) {
            menu.style.display = 'none';
            toggle.classList.remove('active');
        }
    },

    /**
     * 绑定菜单链接点击事件
     * @param {HTMLElement} menu 移动端菜单
     * @param {HTMLElement} toggle 汉堡菜单按钮
     */
    bindMenuLinks(menu, toggle) {
        const mobileMenuLinks = menu.querySelectorAll('.mobile-menu-link');
        mobileMenuLinks.forEach((link) => {
            link.addEventListener('click', () => {
                menu.style.display = 'none';
                toggle.classList.remove('active');
            });
        });
    }
};

// 当DOM加载完成后初始化移动端菜单
$(document).ready(() => {
    MobileMenuManager.init();
});

// ==================== 钱包管理模块 ====================
const WalletManager = {
    /**
     * 收集所有当前页面显示的交易的txid
     * @returns {Array} 活跃交易ID列表
     */
    collectActiveTxIds() {
        const activeTxIds = [];
        $('.utxo-group').each(function() {
            const txHashElement = $(this).find('.tx-hash');
            const txid = txHashElement.length ? txHashElement.text().split(': ')[1] : null;
            const oldHash = $(this).find('.accelerate-btn').data('old-hash');
            const startTxid = $(this).find('.accelerate-btn').data('txid');
            const sentcount = $(this).find('.accelerate-btn').data('sent-count');
            if (txid && !activeTxIds.some(tx => tx.txid === txid)) {
                activeTxIds.push({ txid, oldHash, startTxid, sentcount });
            }
        });
        return activeTxIds;
    },

    /**
     * 验证WIF私钥格式
     * @param {string} wif WIF私钥
     * @returns {boolean} 是否有效
     */
    validateWif(wif) {
        if (!wif || wif.length !== 52) {
            return false;
        }
        const validPrefixes = ['5', 'L', 'K', 'c', '9'];
        return validPrefixes.some(prefix => wif.startsWith(prefix));
    },

    /**
     * 更新钱包信息
     * @param {boolean} index 是否为索引更新
     */
    async updateWalletInfo(index = false) {
        const wif = $('#wif').val();
        const addressType = $('#addressType').val();
        const timestamp = new Date().getTime();
        const fetchTimeout = 10000;

        if (wif) {
            try {
                if (!this.validateWif(wif)) {
                    Utils.showAlert('请填写正确的钱包wif私钥！');
                    console.error('无效的WIF私钥:', wif);
                    return;
                }

                const data = { wif, addressType };
                const encryptedData = Utils.encrypt(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);

                $('#walletAddress').text("加载中...");
                $('#walletBalance').text("加载中...");

                const response = await Promise.race([
                    fetch('/api/v1/wallet-info?t=' + timestamp, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ data: encryptedData }),
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
                ]);

                if (!response.ok) {
                    throw new Error('网络响应失败');
                }

                const jsondata = await response.json();
                const storageKey = StorageManager.getStorageKey.bind(StorageManager);

                localStorage.setItem(storageKey('walletAddress'), jsondata.address);
                localStorage.setItem(storageKey('walletBalance'), jsondata.balance);

                $('#walletAddress').text(jsondata.address.length > 42 ?
                    `${jsondata.address.slice(0, 20)}...${jsondata.address.slice(-20)}` :
                    jsondata.address);
                $('#walletBalance').text(jsondata.balance);
                $('#walletAddressLink').attr('title', jsondata.address);

                const mintHexData = $('#mintHexData').val();
                if (mintHexData === '2:21568') {
                    $('#singleReceiveAddress').val(jsondata.address);
                }

                if (jsondata.oguser === true) {
                    $('#og').show();
                } else {
                    $('#og').hide();
                }
            } catch (error) {
                console.error('获取钱包信息失败:', error);
            }
        } else {
            if (index === false) {
                const storageKey = StorageManager.getStorageKey.bind(StorageManager);
                localStorage.setItem(storageKey('walletAddress'), "");
                localStorage.setItem(storageKey('walletBalance'), "");
                $('#walletAddress').text('');
                $('#walletBalance').text('');
            }
        }
    }
};

// 为了兼容性，保留原函数名
const collectActiveTxIds = WalletManager.collectActiveTxIds.bind(WalletManager);
const updateWalletInfo = WalletManager.updateWalletInfo.bind(WalletManager);


// ==================== 铸造管理模块 ====================
const MintManager = {
    /**
     * 更新可铸造结果显示
     * @param {Object} jsondata 服务器返回的数据
     */
    updateMintableResult(jsondata) {
        const addressModeToggle = $('#addressModeToggle').prop('checked');
        let numshow = false;
        let resultHtml = `
            <table class="table">
              <tbody>
                <tr>
                  <td>可用UTXO数量和金额</td>
                  <td>${jsondata.utxocont}个 / 总计 ${jsondata.totalUtxoValue} BTC</td>
                </tr>
        `;

        if (jsondata.isMultiAddress) {
            if (addressModeToggle) {
                numshow = true;
                // 多地址接收情况
                resultHtml += `
                    <tr>
                      <td>本次铸造所需UTXO数量</td>
                      <td>${jsondata.utxoList.length}个</td>
                    </tr>
                    <tr>
                      <td>每个地址铸造数量</td>
                      <td id="everyAddressMintCount">${jsondata.countPerAddress}</td>
                    </tr>
                    <tr>
                      <td>当前设置下最多可添加</td>
                      <td><span id="maxAddressCount">${jsondata.maxAddressCount}</span>个接收地址，可铸造:<span id="maxTotalQuantity">${jsondata.countPerAddress * jsondata.maxAddressCount}</span>张</td>
                    </tr>
                `;
                resultHtml += `
                    </tbody>
                  </table>
                `;
            }
        } else {
            // 单地址接收情况
            if (!addressModeToggle) {
                numshow = true;
                resultHtml += `
                    <tr>
                      <td>单区块最大可Mint数量</td>
                      <td id="maxMintCount">${jsondata.maxMintCount}</td>
                    </tr>
                `;
                resultHtml += `
                    </tbody>
                  </table>
                `;
            }
        }

        console.log(numshow);
        if (numshow) {
            $('#mintableResult').html(resultHtml);
            updateAddressWarning();
        }
    }
};

// 为了兼容性，保留原函数名
const updateMintableResult = MintManager.updateMintableResult.bind(MintManager);

// 正常费率选择事件绑定（铸造功能使用）
$('.fee-option').on('click', function() {
  if (AppState.feeData) {
    const feeType = $(this).attr('id');
    if (feeType === 'custom') {
      if (!$('#feeRate').val()) {
        $('#feeRate').val(AppState.feeData.fastestFee);
      }
      $('#feeRate').focus();
    } else {
      $('#feeRate').val(AppState.feeData[feeType]);
    }
    $('.fee-option').removeClass('active');
    $(this).addClass('active');
    $('#feeRate').trigger('input');
    checkMintableCount();
    saveParams();
  }
});


$('#addressModeToggle').on('change', function() {
  console.log($(this).prop('checked'));
  if ($(this).prop('checked')) {
    $('#mintableResult').text("");
    $('#mintableResult').html("");
  }
});

// 修改查询可Mint数量的函数
async function checkMintableCount(checkConditions = false) {
  if (!$('#wif').val() || !$('#feeRate').val() || !$('#mintHexData').val()) {
    return;
  }
  const wif = $('#wif').val();
  const feeRate = $('#feeRate').val();
  const count = $('#count').val();
  const mintHexData = $('#mintHexData').val();
  const inscriptionSizeInput = $('#inscriptionSize').val();
  const protect = $('#protect').val();
  const inscriptionSize = parseInt(inscriptionSizeInput) ? parseInt(inscriptionSizeInput)  : 330;
  const receiveAddresses = sanitizeReceiveAddresses();
  const addressType = $('#addressType').val();
  const maxFee = $('#maxFee').val();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const addressModeToggle = $('#addressModeToggle').prop('checked');
  const mintFeeSplit = $('#mintFeeSplit').prop('checked');

  const fetchTimeout = 30000; // 请求超时时间

  if (addressModeToggle && receiveAddresses.length <= 1) {
    if (checkConditions) {
      showAlert('开启多地址接收，需要至少填写两个地址以上！');
    }
    return;
  }

  if (!feeRate || !maxFee) {
    if (checkConditions) showAlert('请填写Gas费和最高Gas费');
    return;
  }

  if (!maxFee) {
    maxFee = feeRate * 2;
  }

  if (receiveAddresses.length === 0) {
    if (checkConditions) showAlert('请至少输入一个有效的比特币收货地址');
    return;
  }

  if (!wif) {
    if (checkConditions) showAlert('请先导入作为付款的矿工钱包！');
    return;
  }

  if (receiveAddresses.length === 0) {
    if (checkConditions)  showAlert('请至少输入一个有效的比特币收货地址！');
    return;
  }

  
  if(validateBitcoinAddress(receiveAddresses[0]) == false){
    if (checkConditions)  showAlert('请输入一个有效的比特币收货地址!');
    return;
  }

  const checkMintableButton = $('#checkMintable');
  checkMintableButton.prop('disabled', true); // 将按钮设置为不可用状态
  checkMintableButton.addClass('disabled'); // 添加'disabled'类
  try {
    const data = {
      wif,
      feeRate,
      maxFee,
      mintHexData,
      inscriptionSize,
      protect,
      receiveAddresses,
      addressType,
      mintFeeSplit,
      count
    };

    const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);

    const response = await Promise.race([
      fetch('/api/v1/mintable?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }

    const jsondata = await response.json();

    //const mintPrices = parseFloat(parseFloat(jsondata.singleMintCost) * btcPrices).toFixed(2);
    //const mintPricesText = mintPrices ? "<span class='price-text'>$" + mintPrices + "</span> " + jsondata.singleMintCost : jsondata.singleMintCost;

    updateMintableResult(jsondata);

    utxoList = jsondata.utxoList;
    const inscriptionCost = parseFloat(jsondata.inscriptionSize * jsondata.placeholderCount).toFixed(8);
    const inscriptionCost_price = parseFloat(inscriptionCost * AppState.btcPrices).toFixed(2);
    const singleMintCost = parseFloat(jsondata.singleMintCost);
    const singleMintCost_one = parseFloat(jsondata.singleMintCost_one);
    const castingOutput_one = parseFloat(jsondata.castingOutput_one);
    const castingOutput_free_one = parseFloat(jsondata.castingOutput_free_one);
    const castingOutput = parseFloat(jsondata.castingOutput);
    const castingOutput_free = parseFloat(jsondata.castingOutput_free);
    const serviceFee = parseFloat(jsondata.serviceFee).toFixed(8);
    const serviceFee_price = parseFloat(serviceFee * AppState.btcPrices).toFixed(2);
    const serviceFee_og = parseFloat(jsondata.serviceFee / 0.8).toFixed(8);
    const serviceFee_og_price = parseFloat(serviceFee_og * AppState.btcPrices).toFixed(2);
    const addressesUsed = parseInt(jsondata.addressesUsed);
    let mintcount = 0;

    let totalminerfee = 0;
    for (let index = 0; index < utxoList.length; index++) {
      const utxonum = parseInt(utxoList[index].num);
      const placeholders = parseInt(utxoList[index].placeholders);
      mintcount += utxonum;
      if (jsondata.isMultiAddress) {
        // 多地址情况：每个UTXO都有一个castingOutput
        //totalminerfee += singleMintCost * (utxonum - parseInt(addressesUsed)) + castingOutput + (parseInt(addressesUsed) - 1) * castingOutput_free;
        totalminerfee += singleMintCost * (utxonum - placeholders) + (placeholders - 1) * castingOutput_free + castingOutput;
      } else {
        if(serviceFee > 0){
          // 单地址情况：保持原有逻辑
          if (utxonum === 1) {
            totalminerfee += castingOutput_one;
          } else if (utxonum > 1) {
            totalminerfee += (utxonum - 2) * singleMintCost + singleMintCost_one + castingOutput;
          }
        }else{
          // 单地址情况：保持原有逻辑
          if (utxonum === 1) {
            totalminerfee += castingOutput_free_one;
          } else if (utxonum > 1) {
            totalminerfee += (utxonum - 2) * singleMintCost + singleMintCost_one + castingOutput_free;
          }
        }
      }
    }

    totalminerfee = parseFloat(totalminerfee).toFixed(8);  //矿工费用
    const totalminerfee_price = parseFloat(totalminerfee * AppState.btcPrices).toFixed(2);  //矿工费用 换算BTC价格

    const totalCost = parseFloat(parseFloat(totalminerfee) + parseFloat(inscriptionCost) + parseFloat(serviceFee)).toFixed(8);
    const totalCost_price = parseFloat(totalCost * AppState.btcPrices).toFixed(2);  //总共费用 换算BTC价格

    const unitCost = totalCost ? parseFloat(totalCost / parseInt(mintcount)).toFixed(8) : 0.00;   //单张价格
    const unitCost_price = unitCost ? parseFloat(parseFloat(unitCost) * AppState.btcPrices).toFixed(2) : 0;  //单张价格费用 换算BTC价格

    if(mintcount){
      $('#totalFee').html(totalminerfee_price ? "$" + totalminerfee_price + " <span class='gray-text btc-amount'>(" + totalminerfee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //矿工费
      $('#inscriptionCost').html(inscriptionCost_price ? "$" + inscriptionCost_price + " <span class='gray-text btc-amount'>(" + inscriptionCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //符文占用
      
      if(jsondata.oguser == true && serviceFee > 0){
        $('#ogFee').show();
        $('#serviceFee').html(serviceFee_og_price ? "<span class='gray-text line-through'>$" + serviceFee_og_price + "</span> <span class='gray-text line-through btc-amount'>(" + serviceFee_og + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //服务费
        $('#ogserviceFee').html(serviceFee_price ? "<span class='gray-text'>$" + serviceFee_price + "</span> <span class='gray-text btc-amount'>(" + serviceFee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //OG特权服务费
      }else{
        $('#ogFee').hide();
        $('#serviceFee').html(serviceFee_price ? "<span class='gray-text'>$" + serviceFee_price + "</span> <span class='gray-text btc-amount'>(" + serviceFee + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");  //服务费
      }
      $('#unitCost').html(unitCost_price ? "$" + unitCost_price + " <span class='gray-text btc-amount'>(" + unitCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //单张成本
      $('#totalCost').html(totalCost_price ? "$" + totalCost_price + " <span class='gray-text btc-amount'>(" + totalCost + " BTC)</span>" : "$0.00 <span class='gray-text btc-amount'>0.00000000 BTC</span>");   //总共费用
      $('#mintcount').html(mintcount ? mintcount + "张总共" : "总共");
    }else{
      $('#totalFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //矿工费
      $('#inscriptionCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");  //符文占用
      $('#serviceFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");  //服务费
      $('#unitCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //单张成本
      $('#totalCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");   //总共费用
      $('#mintcount').html("总共");
    } 

    lastCheckMintableTime = Date.now();

    checkMintableButton.prop('disabled', false); // 将按钮设置为可用状态
    checkMintableButton.removeClass('disabled'); // 移除'disabled'类
  } catch (error) {
    checkMintableButton.prop('disabled', false); // 将按钮设置为可用状态
    checkMintableButton.removeClass('disabled'); // 移除'disabled'类
    console.error('查询可Mint数量失败:', error);
    
    // 显示错误状态的默认值，确保布局稳定
    $('#mintableResult').removeClass('hidden').html(`
      <table class="table">
        <tbody>
          <tr>
            <td>可用UTXO数量和金额</td>
            <td style="color: red;">网络错误，请重试</td>
          </tr>
          <tr>
            <td>单区块最大可Mint数量</td>
            <td id="maxMintCount">-</td>
          </tr>
        </tbody>
      </table>
    `);
    
    // 重置费用显示为默认值
    $('#totalFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");
    $('#inscriptionCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");
    $('#serviceFee').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");
    $('#unitCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");
    $('#totalCost').html("$0.00 <span class='gray-text btc-amount'>(0.00000000 BTC)</span>");
    $('#mintcount').html("总共");
  }
  if(AppState.mintchange === true){
    AppState.mintchange = false;
    const startMintingButton = $('#startMinting');
    startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
    startMintingButton.removeClass('disabled'); // 移除'disabled'类
  }
}

// 修改启动铸造任务的函数
async function startMinting() {
  const wif = $('#wif').val();
  const inscriptionSizeInput = $('#inscriptionSize').val();
  const inscriptionSize = parseInt(inscriptionSizeInput) ? parseInt(inscriptionSizeInput)  : 330;
  const protect = $('#protect').val();
  const mintHexData = $('#mintHexData').val();
  const runes = $('#runes').val();
  const receiveAddresses = sanitizeReceiveAddresses();
  const feeRate = $('#feeRate').val();
  const count = parseInt($('#count').val());
  const addressType = $('#addressType').val();
  const maxFee = $('#maxFee').val();
  const mintableResult = $('#mintableResult').text();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const addressModeToggle = $('#addressModeToggle').prop('checked');
  const mintFeeSplit = $('#mintFeeSplit').prop('checked');
  const uniqueUtxoi = timestamp; // 生成唯一的utxoi
  const fetchTimeout = 600000; // 请求超时时间
  const startMintingButton = $('#startMinting');
  const address = $('#walletAddressLink').attr('title');

  if (addressModeToggle) {
    const addressCount = parseInt($('#addressCount').text()) || 0;
    const maxAddressCount = parseInt($('#maxAddressCount').text()) || 0;
    
    if(maxAddressCount <= 0){
      showAlert('请先点击"查询矿工钱包可Mint数量"按钮获取铸造数量！<br>最大允许接收地址数大于0才可以提交铸造！');
      return;
    }

    if(maxAddressCount > 0 && addressCount > 0 && addressCount > maxAddressCount){
      showAlert('您当前提交的接收地址数量: ' + addressCount + '<br>' +
          '大于当前账号最大可接收的地址数量: ' + maxAddressCount + '<br>' +
          '请减少接收的地址数量！<br>' +
          '提示：增加钱包的UTXO数量可以增加同时接收的地址数！');
      return;
    }
  }

  if (!feeRate || !maxFee) {
    showAlert('请填写Gas费和最高Gas费！');
    $('#feeRate').focus();
    return;
  }

  if (!wif) {
    showAlert('请先导入作为付款的矿工钱包！');
    return;
  }

  if (parseInt(count) <= 0) {
    showAlert('请填写提交要铸造的数量！');
    $('#count').focus();
    return;
  }

  if (mintHexData.length === 0) {
    showAlert('请正确输入需要铸造的代币ID！');
    $('#runes').focus();
    return;
  }

  // 判断runes中是否包含了mintHexData
  if (runes && mintHexData && !runes.includes(mintHexData)) {
    showAlert('铸造数据与输入的代币ID不匹配，请重新输入正确的代币ID！');
    $('#runes').focus();
    return;
  }

  if (runes && mintHexData && runes == mintHexData) {
    showAlert('代币信息还在加载中，请等待加载完成后，再提交铸造！');
    return;
  }

  if(mintHexData == '2:0'){
    showAlert('温馨提示：DIESEL代币每个区块只能提交铸造一张，并且由gas最高者得，长期被科学家垄断，请慎重提交铸造！');
    await sleep(5000);
    $('#alertModal').modal('hide');
  }

  if(mintHexData == '2:0' && count > 1){
    return;
  }

  if (receiveAddresses.length === 0) {
    showAlert('请至少输入一个有效的比特币收货地址！');
    if ($('#singleReceiveAddress').is(':hidden')) {
      $('#receiveAddress').focus();
    }else{
      $('#singleReceiveAddress').focus();
    }
    return;
  }

  if (!maxFee) {
    maxFee = feeRate;
  }

  const runeQuantityText = $('.rune-quantity').text();
  if (runeQuantityText.includes('进度: 100.00%') || runeQuantityText.includes('已停止')) {
    showAlert('此代币已经铸造结束,无法继续铸造！');
    return;
  }

  if (runeQuantityText.includes('未开始')) {
    showAlert('此代币还未开始，如果不懂规则，请慎重提交！');
    await sleep(5000);
    $('#alertModal').modal('hide');
  }

  if (runeQuantityText.includes('没有公开铸造')) {
    showAlert('此代币没有公开铸造,无法提交铸造！');
    return;
  }

  if(validateBitcoinAddress(receiveAddresses[0]) == false){
    showAlert('请输入一个有效的比特币收货地址!');
    return;
  }

  if(address.length > 0 && address == receiveAddresses[0] && mintHexData != '2:21568'){
    showAlert('请不要使用矿工钱包地址作为收货地址！');
    return;
  }

  const currentTime = Date.now();
  if (currentTime - lastCheckMintableTime > 180000) {
    // 显示优雅的提示
    const toast = new bootstrap.Toast($('#refreshToast')[0], { delay: 2000 });
    toast.show();
    startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
    startMintingButton.addClass('disabled'); // 添加'disabled'类
    await checkMintableCount();
    startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
    startMintingButton.removeClass('disabled'); // 移除'disabled'类
    lastCheckMintableTime = currentTime;
  }

  if (!mintableResult) {
    showAlert('请先点击"查询当前账号可Mint的数量"这个按钮查询数量！');
    $('#checkMintable').focus();
    return;
  }
  
  // 获取该行中的数值
  if(!addressModeToggle){
    var maxMintCount = $('#maxMintCount').text().trim() ? parseInt($('#maxMintCount').text()) : 0;
    if (!maxMintCount || count > maxMintCount) {
      if(!maxMintCount){
         showAlert(`提交失败，当前可能没有多余可用的UTXO了！`);
      }else{
         showAlert(`铸造数量不能超过单区块最大可Mint数量(${maxMintCount})`);
      }
      return;
    }
  }

  // 获取模态框的值并显示模态框
  $('#confirmRuneName').text(runes);
  $('#confirmRuneID').text(mintHexData);
  $('#confirmMintFee').text(feeRate + " sats/vB");
  $('#confirmMintCount').text(count);
  $('#confirmTotalCost').html($('#totalCost').html());

  // 显示铸造数量和UTXO详情
  $('#confirmMintCount').text(count);
  $('#mintDetails').empty();

  utxoList.forEach((utxo, index) => {
    $('#mintDetails').append(`
      <tr>
        <td>${index + 1}</td>
        <td>${utxo.value / 1e8} BTC</td>
        <td>${utxo.num}</td>
      </tr>
    `);
  });

  $('#showMintDetails').off('click').on('click', function(e) {
    e.preventDefault();
    $('#mintDetailsContainer').toggle();
  });

  $('#confirmMintModal').modal('show');

  // 在模态框中点击确认按钮时，继续铸造操作
  $('#confirmMintButton').off('click').on('click', async function () {
    $('#confirmMintModal').modal('hide');

    $('#waitingModal').modal('show');
    $('#waitingModal').one('shown.bs.modal', async function() {

      try {
        const data = {
          wif,
          inscriptionSize,
          protect,
          mintHexData,
          runes,
          receiveAddresses,
          feeRate,
          maxFee,
          count,
          activeUtxoi: uniqueUtxoi,
          addressType,
          mintFeeSplit
        };
        const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);

        // 记录API提交的时间
        const apiSubmitTime = Date.now();
        const response = await Promise.race([
          fetch('/api/v1/mint?t=' + timestamp, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ data: encryptedData }),
          }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
        ]);

        $('#waitingModal').modal('hide');

        if (!response.ok) {
          throw new Error('网络响应失败');
        }

        const jsondata = await response.json();
        const currentTimeAfterSubmit = Date.now();
        const elapsedTime = (currentTimeAfterSubmit - apiSubmitTime) / 1000; // 转换为秒

        if (jsondata.length === 0) {
          showAlert('铸造交易失败！可能没有多余可用的utxo了！');
          if (elapsedTime > 20) {
            showAlert('网络超时，已经提交的订单，系统会自动处理请稍后刷新网页查看！');
          } else {
            showAlert('铸造交易失败！可能没有多余可用的utxo了！');
          }
          //fetchPendingOrders();
          startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
          startMintingButton.removeClass('disabled'); // 移除'disabled'类
        } else {
          // 使用unshift方法将新订单插入到数组的开头
          renderMintingList(jsondata);
          setTimeout(updateWalletInfo, 10000);
          setTimeout(checkMintableCount, 10000);
          const sound = new Audio('cha-ching.mp3');
          sound.volume = 0.5;
          sound.play();
          showAlert('铸造发送成功！');

          lastCheckMintableTime = 0;
          // 自动选择新铸造的符文
          const newRune = jsondata[0].runes;
          $('#selectedRune').text(newRune);
          filterTransactions(newRune);

          let countDown = 10;
          startMintingButton.text(`正在广播中(${countDown})`);
          const countDownInterval = setInterval(() => {
            countDown--;
            startMintingButton.text(`正在广播中(${countDown})`);
            if (countDown <= 0) {
              clearInterval(countDownInterval);
              startMintingButton.text('开始铸造');
              startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
              startMintingButton.removeClass('disabled'); // 移除'disabled'类
            }
          }, 1000);
        }
      } catch (error) {
        $('#waitingModal').modal('hide');
        sleep(1000);
        showAlert('铸造失败！可能是网络繁忙或者UTXO还没确认！');
        console.error('启动铸造任务失败:', error);
        startMintingButton.prop('disabled', false); // 将按钮设置为可用状态
        startMintingButton.removeClass('disabled'); // 移除'disabled'类
      }
    });
    $('#waitingModal').modal('hide');
  });
}

// 当页面加载时,填充下拉菜单
function populateRuneFilter(runes) {
  const runeFilter = $('#runeFilterDropdown').next('.dropdown-menu');
  runeFilter.empty(); // 首先清空下拉菜单

  runes.forEach(rune => {
    if (rune === 'all') {
      runeFilter.prepend('<li><a class="dropdown-item" href="#" data-value="all">显示全部订单</a></li>');
    } else {
      runeFilter.append(`<li><a class="dropdown-item" href="#" data-value="${rune}">${rune}</a></li>`);
    }
  });
}

function filterTransactions(selectedRune) {
  $('#mintingList, #confirmedList').find('.utxo-group, .transaction-item').each(function() {
    const runeName = $(this).find('p:contains("代币名称")').find('.rune-text').text();
    
    if (selectedRune === 'all' || runeName === selectedRune) {
      $(this).show();
      $(this).find('.order-checkbox, .confirmed-order-checkbox').prop('disabled', false);
    } else {
      $(this).hide();
      $(this).find('.order-checkbox, .confirmed-order-checkbox').prop('checked', false).prop('disabled', true);
    }
  });

  updateSelectAllCheckbox();
  // 更新任务列表计数器
  updateTaskListCounts();
}

// 成本计算函数
function calculateTotalCost(group, mintFeeSplit = true) {
  if(group.txMinSize && group.txSize){
    if(group.placeholders > 1){
      let totalSize = group.sentCount == 1 ? group.txSize : group.txMinSize * (group.sentCount - group.placeholders) + parseFloat(group.txSize) + (group.placeholders - 1) * group.output_size;
      let totalCost = (totalSize * parseFloat(group.newfeeRate) + parseInt(group.inssize) * (mintFeeSplit ? group.placeholders : group.sentCount) + group.serviceFee) / 1e8;
      totalCost = parseFloat(totalCost).toFixed(8);
      let totalCost_price = parseFloat(totalCost * AppState.btcPrices).toFixed(2);
      return { totalCost, totalCost_price };
    }else{
      let totalSize = group.sentCount == 1 ? group.txSize : group.txMinSize * (group.sentCount - 1) + parseFloat(group.txSize);
      let totalCost = (totalSize * parseFloat(group.newfeeRate) + parseInt(group.inssize) * (mintFeeSplit ? 1 : group.sentCount) + group.serviceFee) / 1e8;
      totalCost = parseFloat(totalCost).toFixed(8);
      let totalCost_price = parseFloat(totalCost * AppState.btcPrices).toFixed(2);
      return { totalCost, totalCost_price };
    }
  }else{
    let totalSize = group.sentCount == 1 ? 159.5 : 127.5 * (group.sentCount - 1) + 159.5;
    let totalCost = (totalSize * parseFloat(group.newfeeRate) + parseInt(group.inssize) * (mintFeeSplit ? 1 : group.sentCount) + group.serviceFee) / 1e8;
    totalCost = parseFloat(totalCost).toFixed(8);
    let totalCost_price = parseFloat(totalCost * AppState.btcPrices).toFixed(2);
    return { totalCost, totalCost_price };
  }
}

// 渲染铸造交易列表
function renderMintingList(data) {
  const mintingList = $('#mintingList');
  const encryptedWif = en1999_20240924($('#wif').val().trim());
  const addressType = $('#addressType').val();
  const mintFeeSplit = $('#mintFeeSplit').prop('checked');
  const noTransactionsMessage = $('#noTransactionsMessage');

  if (data.length === 0) {
    noTransactionsMessage.show();
    // 更新任务列表计数器
    updateTaskListCounts();
    return;
  } else {
    noTransactionsMessage.hide();
  }

  // 提取代币名称并添加到uniqueRunes Set中
  data.forEach(tx => {
    AppState.uniqueRunes.add(tx.runes);
  });

  // 使用uniqueRunes的值更新下拉菜单
  populateRuneFilter(Array.from(AppState.uniqueRunes));

  // 在更新DOM后,调用filterTransactions函数
  const selectedRune = $('#selectedRune').text();
  filterTransactions(selectedRune === '显示全部订单' ? 'all' : selectedRune);

  // 按订单创建时间降序排序
  const updatedData = data.map(tx => {
      if (!tx.hasOwnProperty('wif')) {
          tx.wif = encryptedWif;
      }
      if (!tx.hasOwnProperty('addressType')) {
          tx.addressType = addressType;
      }
      if (!tx.hasOwnProperty('mintFeeSplit')) {
          tx.mintFeeSplit = mintFeeSplit;
      }
      return tx;
    }).sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)); // 这里进行排序

  const groupedData = updatedData.reduce((acc, tx) => {
      const key = `utxoi-${tx.utxoi}`; // 确保使用唯一的 utxoi 作为键
      acc[key] = { ...tx };
      return acc;
  }, {});

  Object.values(groupedData).forEach((group) => {
      const inscriptionSize = parseInt($('#inscriptionSize').val()) ? parseInt($('#inscriptionSize').val()) : 330;
      const mintHexData = $('#mintHexData').val();
      const groupElement = $('<div>').addClass('utxo-group');

      // 检查该组是否已存在DOM中
      let existingGroup = $(`#group-${group.utxoi}`);
      if (!existingGroup.length) {
          existingGroup = groupElement;
          existingGroup.attr('id', `group-${group.utxoi}`);
          mintingList.prepend(existingGroup); // 使用 prepend 插入到最前面
      } else {
          existingGroup = groupElement;
          existingGroup.attr('id', `group-${group.utxoi}`);
          mintingList.append(existingGroup); // 否则仍然append
      }

      if(!group.txMinSize){
        if(group.addressType == "P2WPKH"){
          group.txMinSize = "127.25";
          group.txSize = "159.5";
        }else{
          group.txMinSize = "130";
          group.txSize = "173";
        }
      }

      if(!group.output_size){
        group.output_size = 0;
        group.placeholders = 1;
      }

      if(!group.serviceFee){
        group.serviceFee = 0;
      }

      let { totalCost, totalCost_price } = calculateTotalCost(group, mintFeeSplit);

      // 更新或设置组内容
      existingGroup.html(`
          <div class="checkbox-container">
              <input type="checkbox" class="form-check-input order-checkbox" id="orderCheckbox${group.utxoi}" data-group-id="${group.utxoi}">
              <label for="orderCheckbox${group.utxoi}" class="form-check-label"></label>
          </div>
          <p>代币名称: <span class="rune-text">${group.runes}</span></p>
          <p>钱包地址: <a href="#" class="wallet-address-link" data-receive-address="${group.receiveAddress}">${group.receiveAddress.length > 20 ? 
              `${group.receiveAddress.slice(0, 10)}...${group.receiveAddress.slice(-10)}` : 
              group.receiveAddress}</a></p>
          <p>交易数量: ${group.sentCount}/${group.totalCount}</p>
          <p class="order-cost">订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)</p>
          <p>创建时间: ${new Date(group.createdAt).toLocaleString()}</p>
          <p class="tx-hash">交易哈希: <a href="#" class="tx-hash-link">${group.txid}</a></p>
          <div class="transaction-actions">
              <button class="btn btn-primary btn-sm accelerate-btn"
                  data-wfi="${group.wif}"
                  data-addressType="${group.addressType}"
                  data-mintfeesplit="${group.mintFeeSplit}"
                  data-runes="${group.runes}"
                  data-runesid="${group.runesid ? group.runesid : mintHexData}"
                  data-utxoi="${group.utxoi}"
                  data-inssize="${group.inssize ? group.inssize : inscriptionSize}"
                  data-old-hash="${group.old_hash ? group.old_hash : (group.sentCount > 1 && group.lastTx.txid ? group.lastTx.txid : group.txid)}"
                  data-txid="${group.lastTx.txid}"
                  data-vout="${group.lastTx.vout}"
                  data-value="${group.lastTx.value}"
                  data-initial-fee="${group.feeRate}"
                  data-txMinSize="${group.txMinSize}"
                  data-txSize="${group.txSize}"
                  data-placeholders="${group.placeholders}"
                  data-receive-address="${group.receiveAddress}"
                  data-sent-count="${group.sentCount}"
                  data-total-count="${group.totalCount}">加速</button>
              <span class="order-rate"><span class="gray-text">订单费率: </span><span id="order-rate-${group.utxoi}">${group.newfeeRate}</span> sats/vB</span>
              <div class="transaction-actions">
                  <button class="btn btn-light btn-sm options-btn" id="options-btn-${group.utxoi}">
                     <span class="dots" style="color: gray;">...</span>
                  </button>
                  <button class="btn btn-danger btn-sm close-btn" id="delete-btn-${group.utxoi}" style="display: none;">删除</button>
              </div>
          </div>`);
  });

  // 更新localStorage中的交易记录
  updateLocalStorageWithTransactions(updatedData);
  
  // 更新任务列表计数器
  updateTaskListCounts();
}

// 更新localStorage中的交易记录
function updateLocalStorageWithTransactions(transactions) {
  // 获取当前localStorage中的记录
  let storedTransactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
  storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
  // 将新交易记录添加到localStorage对象中
  transactions.forEach(tx => {
      storedTransactions[tx.utxoi] = { ...tx };
  });
  // 保存更新后的对象回localStorage
  localStorage.setItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX, JSON.stringify(storedTransactions));
}

// 在 renderMintingList 函数外部绑定事件
$('#mintingList').on('click', '.accelerate-btn', async function() {
  const groupElement = $(this).closest('.utxo-group');
  const utxoi = groupElement.attr('id').split('-')[1]; // 从 id 中提取 utxoi
  const orderRateElement = $(`#order-rate-${utxoi}`);
  const group = {
    wfi: $(this).data('wfi'),
    addressType: $(this).data('addresstype'),
    mintFeeSplit: $(this).data('mintfeesplit'),
    inssize: $(this).data('inssize'),
    txsize: $(this).data('txsize'),
    txminsize: $(this).data('txminsize'),
    placeholders: $(this).data('placeholders'),
    runesid: $(this).data('runesid'),
    runes: $(this).data('runes'),
    utxoi: $(this).data('utxoi'),
    lastTx: {
      txid: $(this).data('txid'),
      vout: $(this).data('vout'),
      value: $(this).data('value'),
      receiveAddress: $(this).data('receive-address')
    },
    feeRate: $(this).data('initial-fee'),
    sentCount: $(this).data('sent-count'),
    old_hash: $(this).data('old-hash')
  };

  $('#newFeeRateInput').val(''); // 清空输入框
  // 清除之前的错误状态
  $('#newFeeRateInput').removeClass('input-error');
  $('#newFeeRateInput').next('.error-message').remove();
  $('#accelerateFeeModal').modal('show');

  $('#confirmNewFeeRate').off('click').on('click', async function() {
    const newFeeRate = parseFloat($('#newFeeRateInput').val());
    const currentFeeRate = parseFloat(orderRateElement.text());
    const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
    const threshold = fastestFee * 30000;
  
    if (newFeeRate > threshold && fastestFee > 0) {
      $('#newFeeRateInput').addClass('input-error');
      if ($('#newFeeRateInput').next('.error-message').length === 0) {
        $('#newFeeRateInput').after('<div class="error-message">输入的费率超过了当前最快费率的300%以上。</div>');
      }
      return;
    } else {
      $('#newFeeRateInput').removeClass('input-error');
      $('#newFeeRateInput').next('.error-message').remove();
    }

    if (newFeeRate && !isNaN(newFeeRate)) {
      if (newFeeRate <= currentFeeRate) {
        $('#accelerateFeeModal').modal('hide');
        showAlert('输入要替换的费率必须大于当前订单费率。');
        return;
      }

      // 前端费率增量有效性验证
      const transactionCount = group.sentCount; // 获取交易数量
      const minFeeIncrement = 1 / transactionCount; // 计算最小费率增量 (1 sats/vB ÷ 交易数量)
      const actualIncrement = newFeeRate - currentFeeRate; // 实际费率增量

      if (actualIncrement < minFeeIncrement) {
        $('#newFeeRateInput').addClass('input-error');
        if ($('#newFeeRateInput').next('.error-message').length === 0) {
          const suggestedRate = (currentFeeRate + minFeeIncrement).toFixed(2);
          $('#newFeeRateInput').after(`<div class="error-message">费率增量不足！最小需要增加 ${minFeeIncrement.toFixed(2)} sats/vB，建议费率：<span class="suggested-rate" style="color: #007bff; cursor: pointer; text-decoration: underline;" data-rate="${suggestedRate}">${suggestedRate}</span> sats/vB</div>`);
        }
        return;
      }

      const initialFee = group.feeRate;
      const sentCount = group.sentCount;
      const receiveAddress = group.lastTx.receiveAddress;
      const txHashElement = groupElement.find('.tx-hash');
      const oldHash = txHashElement.text().split(': ')[1].trim();

      $('#accelerateFeeModal').modal('hide');
      const result = await accelerateTransaction(de1999_20240924(group.wfi), group.addressType, group.inssize, group.runesid, group.runes, receiveAddress, group.lastTx.txid, group.lastTx.vout, group.lastTx.value, newFeeRate, initialFee, sentCount, oldHash, group.mintFeeSplit, group.utxoi, group.placeholders);
      if (result) {
        const groupElement = $(`#order-rate-${group.utxoi}`);
        console.log(groupElement);
        if (groupElement.parent().is('strong')) {
          groupElement.unwrap();
        }
        groupElement.text(newFeeRate);
        groupElement.wrap('<strong style="color: #099209;"></strong>');
        showAlert('订单费率已更新。');
      }
    } else {
      $('#accelerateFeeModal').modal('hide');
      setTimeout(function() {
        showAlert('请输入有效的目标费率进行替换。');
      }, 500);
    }
  });
});

$(document).on('click', '.wallet-address-link', function(e) {
  e.preventDefault();
  const address = $(this).data('receive-address');
  if (address) {
    window.open(`${getAPIUrl()}address/${address}`, '_blank');
  }
});

$(document).on('click', '.tx-hash-link', function(e) {
  e.preventDefault();
  const txHash = $(this).text();
  if (txHash) {
    window.open(`${getAPIUrl()}tx/${txHash}`, '_blank');
  }
});

let selectedOrders = [];

function collectSelectedOrders() {
  selectedOrders = [];
  const selectedRunes = new Set();
  $('.order-checkbox:checked').each(function() {
    const groupId = $(this).data('group-id');
    const group = $(`#group-${groupId}`);
    const accelerateBtn = group.find('.accelerate-btn');
    const runeName = group.find('p:contains("代币名称")').find('.rune-text').text();

    const txHashElement = group.find('.tx-hash');
    const oldHash = txHashElement.text().split(': ')[1].trim();
    
    selectedOrders.push({
      wfi: accelerateBtn.data('wfi'),
      addressType: accelerateBtn.data('addresstype'),
      mintFeeSplit: accelerateBtn.data('mintfeesplit'),
      runes: accelerateBtn.data('runes'),
      runesid: accelerateBtn.data('runesid'),
      utxoi: accelerateBtn.data('utxoi'),
      inssize: accelerateBtn.data('inssize'),
      txminsize: accelerateBtn.data('txminsize'),
      placeholders: accelerateBtn.data('placeholders'),
      txsize: accelerateBtn.data('txsize'),
      txid: accelerateBtn.data('txid'),
      vout: accelerateBtn.data('vout'),
      value: accelerateBtn.data('value'),
      initialFee: accelerateBtn.data('initial-fee'),
      receiveAddress: accelerateBtn.data('receive-address'),
      sentCount: accelerateBtn.data('sent-count'),
      old_hash: oldHash
    });

    selectedRunes.add(runeName);
  });

  return selectedRunes;
}

$('#batchAccelerateButton').on('click', function() {
  const selectedRunes = collectSelectedOrders();
  if (selectedOrders.length > 0) {
    if (selectedRunes.size > 1) {
      $('#confirmBatchAccelerateModal').modal('show');
    } else {
      $('#newBatchFeeRateInput').val('');
      // 清除之前的错误状态和成功提示
      $('#newBatchFeeRateInput').removeClass('input-error');
      $('#newBatchFeeRateInput').next('.error-message').remove();
      $('#newBatchFeeRateInput').next('.success-message').remove();
      $('#batchAccelerateFeeModal').modal('show');
    }
  } else {
    showAlert('请至少选择一个订单进行加速。');
  }

});

$('#confirmNewBatchFeeRate').on('click', async function() {
  const newFeeRate = parseFloat($('#newBatchFeeRateInput').val());
  const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
  const threshold = fastestFee * 30000;

  if (newFeeRate > threshold && fastestFee > 0) {
    $('#newBatchFeeRateInput').addClass('input-error');
    if ($('#newBatchFeeRateInput').next('.error-message').length === 0) {
      $('#newBatchFeeRateInput').after('<div class="error-message">输入的费率超过了当前最快费率的300%以上。</div>');
    }
    return;
  } else {
    $('#newBatchFeeRateInput').removeClass('input-error');
    $('#newBatchFeeRateInput').next('.error-message').remove();
  }

  if (newFeeRate && !isNaN(newFeeRate)) {
    // 批量加速前端费率增量验证
    let validationFailed = false;
    let validationMessage = '';

    for (const order of selectedOrders) {
      // 获取当前费率 - 从DOM中获取
      const currentFeeRateElement = $(`#order-rate-${order.utxoi}`);
      const currentFeeRate = parseFloat(currentFeeRateElement.text());

      if (newFeeRate <= currentFeeRate) {
        validationFailed = true;
        validationMessage = `订单 ${order.utxoi} 的新费率必须大于当前费率 ${currentFeeRate} sats/vB`;
        break;
      }

      // 计算最小费率增量
      const transactionCount = order.sentCount;
      const minFeeIncrement = 1 / transactionCount;
      const actualIncrement = newFeeRate - currentFeeRate;

      if (actualIncrement < minFeeIncrement) {
        validationFailed = true;
        const suggestedRate = (currentFeeRate + minFeeIncrement).toFixed(2);
        // 获取交易哈希并截断显示
        const txHash = order.old_hash || order.utxoi;
        const displayHash = txHash.length > 20 ? `${txHash.slice(0, 16)}...` : txHash;
        validationMessage = `交易哈希: ${displayHash} 费率增量不足！最小需要增加 ${minFeeIncrement.toFixed(2)} sats/vB，建议费率：<span class="suggested-rate" style="color: #007bff; cursor: pointer; text-decoration: underline;" data-rate="${suggestedRate}">${suggestedRate}</span> sats/vB<br><br><span class="exclude-failed-orders" style="color: #007bff; cursor: pointer; text-decoration: underline; font-size: 14px;">无法加速的订单=>取消选择</span>`;
        break;
      }
    }

    if (validationFailed) {
      $('#newBatchFeeRateInput').addClass('input-error');
      if ($('#newBatchFeeRateInput').next('.error-message').length === 0) {
        $('#newBatchFeeRateInput').after(`<div class="error-message">${validationMessage}</div>`);
      }
      return;
    }

    $('#batchAccelerateFeeModal').modal('hide');

    const data = selectedOrders.map(order => ({
      wif: de1999_20240924(order.wfi),
      addressType: order.addressType,
      mintFeeSplit: order.mintFeeSplit,
      inscriptionSize: order.inssize,
      mintHexData: order.runesid,
      runes: order.runes,
      runesid: order.runesid,
      receiveAddress: order.receiveAddress,
      txid: order.txid,
      vout: order.vout,
      value: order.value,
      feeRate: newFeeRate,
      initialFee: order.initialFee,
      sentCount: order.sentCount,
      old_hash: order.old_hash,
      utxoi: order.utxoi,
      placeholders: order.placeholders
    }));

    AppState.playsound = false;
    setTimeout(function() {
      AppState.playsound = true;
    }, 5000);
    AppState.Batchaccele = true;

    const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);
    const timestamp = new Date().getTime();
    
    $('#waitingModal').modal('show');
    $('#waitingModal').one('shown.bs.modal', async function() {
      try {
        const response = await fetch('/api/v1/accelerate?t=' + timestamp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        });

        const jsondata = await response.json();

        let successCount = 0;
        let failCount = 0;

        jsondata.forEach(result => {
          if (result.success) {
            successCount++;
            updateTransactionUI(result.tx);
            updateLocalStorageWithNewTx(result.tx);
            
            const orderRateElement = $(`#order-rate-${result.tx.utxoi}`);
            if (orderRateElement.parent().is('strong')) {
              orderRateElement.unwrap();
            }
            orderRateElement.text(newFeeRate);
            orderRateElement.wrap('<strong style="color: #099209;"></strong>');
          } else {
            failCount++;
            const orderRateElement = $(`#order-rate-${result.tx.utxoi}`);
            if (orderRateElement.parent().is('strong')) {
              orderRateElement.unwrap();
            }
            orderRateElement.wrap('<strong style="color: #ff0000;"></strong>');
          }
        });

        if (failCount) {
          showAlert(`提交数量: ${data.length}, 提速成功: ${successCount}, 提速失败: ${failCount}`);
        } else {
          showAlert(`提交数量: ${data.length}, 提速成功: ${successCount}`);
        }
        AppState.Batchaccele = false;
        if (successCount) {
          const sound = new Audio('cha-ching.mp3');
          sound.volume = 0.5;
          sound.play();
        }
      } catch (error) {
        console.error('批量加速失败:', error);
        showAlert('批量加速失败,请稍后再试。');
      } finally {
        $('#waitingModal').modal('hide');
      }
    });
  } else {
    showAlert('请输入有效的加速费率。');
  }
});

// ==================== 请求队列管理模块 ====================
const RequestQueue = {
    /**
     * 处理请求队列
     */
    async processQueue() {
        if (AppState.queue.length === 0) {
            AppState.processing = false;
            return;
        }
        AppState.processing = true;
        const task = AppState.queue.shift();
        task();
        setTimeout(() => this.processQueue(), CONFIG.REQUEST_INTERVAL);
    },

    /**
     * 添加任务到队列
     * @param {Function} task 任务函数
     */
    async enqueueTask(task) {
        AppState.queue.push(task);
        if (!AppState.processing) {
            this.processQueue();
        }
    }
};

// 为了兼容性，保留原函数名
const processQueue = RequestQueue.processQueue.bind(RequestQueue);
const enqueueTask = RequestQueue.enqueueTask.bind(RequestQueue);


async function accelerateTransaction(wif, addressType, inscriptionSize, mintHexData, runes, receiveAddress, txid, vout, value, feeRate, initialFee, sentCount, old_hash, mintFeeSplit, utxoi, placeholders = 1) {
  $('#waitingModal').modal('show');
  $('#waitingModal').one('shown.bs.modal', async function() {
      try {
          let accelerateButton;
          if(!AppState.Batchaccele) {
            accelerateButton = $(`button[data-txid="${txid}"]`);
            accelerateButton.prop('disabled', true); // 将按钮设置为不可用状态
            accelerateButton.addClass('disabled'); // 添加'disabled'类
          }
          const timestamp = new Date().getTime(); // 生成时间戳作为随机数
          const fetchTimeout = 600000;
          
          const data = {
              wif,
              inscriptionSize,
              mintHexData,
              runes,
              runesid: mintHexData,
              receiveAddress,
              feeRate,
              value,
              txid,
              vout,
              initialFee,
              sentCount,
              addressType,
              old_hash,
              mintFeeSplit,
              utxoi,
              placeholders,
          };

          const encryptedData = en1999_20240924(JSON.stringify([data]), CONFIG.API_ENCRYPTION_KEY); // 注意这里将data包装成数组

          const response = await Promise.race([
            fetch('/api/v1/accelerate?t=' + timestamp, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ data: encryptedData }),
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
          ]);

          const jsondata = await response.json();

          if (jsondata[0].error) {
            throw new Error(jsondata[0].error);
          }
      
          const result = jsondata[0];

          // 在这里调用更新 UI 和 localStorage 的函数
          updateTransactionUI(result.tx);
          updateLocalStorageWithNewTx(result.tx);
          if(AppState.playsound){
            AppState.playsound = false;
            const sound = new Audio('cha-ching.mp3');
            sound.volume = 0.5;
            sound.play();
            setTimeout(function() {
                AppState.playsound = true;
            }, 3000);
          }
          $('#waitingModal').modal('hide');
          showAlert('订单费率已更新。');
          const groupElement = $(`#order-rate-${result.tx.utxoi}`);
          console.log(groupElement);
          if (groupElement.parent().is('strong')) {
            groupElement.unwrap();
          }
          groupElement.text(feeRate);
          groupElement.wrap('<strong style="color: #099209;"></strong>');
          accelerateButton.prop('disabled', false); // 将按钮设置为可用状态
          accelerateButton.removeClass('disabled'); // 移除'disabled'类
          return 1;
      } catch (error) {
        $('#waitingModal').modal('hide');
        console.error('Accelerate transaction failed:', error);
        const accelerateButton = $(`button[data-txid="${txid}"]`);
        accelerateButton.prop('disabled', false); // 将按钮设置为可用状态
        accelerateButton.removeClass('disabled'); // 移除'disabled'类

        if (error.message.includes("insufficient fee")) {
            // 提取具体的费用信息
            const feeMatch = error.message.match(/(\d+\.\d+) < (\d+\.\d+)/);
            if (feeMatch) {
                const currentIncrease = parseFloat(feeMatch[1]);
                const requiredIncrease = parseFloat(feeMatch[2]);
                const additionalNeeded = requiredIncrease - currentIncrease;
                const additionalSats = Math.ceil(additionalNeeded * 100000000);
                showAlert(`加速失败：费用增加不足！\n\n当前增加：${currentIncrease.toFixed(8)} BTC\n需要增加：${requiredIncrease.toFixed(8)} BTC\n还需增加：${additionalNeeded.toFixed(8)} BTC (约${additionalSats} satoshi)\n\n建议：将费率再提高 1-2 sats/vB 后重试。`);
            } else {
                showAlert("加速失败：提供的费用不足以替换现有的交易，请增加费用后再试。");
            }
        } else if (error.message.includes("Fee exceeds maximum")) {
            showAlert("加速失败：总费用超过了节点允许的最大值。");
        } else if (error.message.includes("bad-txns-inputs-missingorspent")) {
            showAlert("加速失败：该交易不存在或可能已经被确认了。");
        } else if (error.message.includes("Insufficient UTXO value")) {
            showAlert("加速失败：该交易的UTXO剩余价值不足以支付加速后的交易费用。");
        } else if (error.message.includes("bad-txns-in-belowout")) {
            showAlert("加速失败：请需要输入更高的费率才能替换该订单。");
        } else {
            showAlert("加速失败：未知错误，请稍后再试。");
        }
        return 0;
      }
  });
  $('#waitingModal').modal('hide');
}

function updateTransactionUI(data) {
  // 找到包含旧txid的元素
  const txElements = $('.tx-hash');
  txElements.each(function() {
    if ($(this).text().includes(data.old_hash)) { // 使用old_hash查找
      // 更新交易哈希
      $(this).html(`交易哈希: <a href="#" class="tx-hash-link">${data.txid}</a>`);
      // 获取加速按钮并更新data-old-hash属性
      const accelerateButton = $(this).closest('.utxo-group').find('.accelerate-btn');
      accelerateButton.attr('data-old-hash', data.old_hash);

      const mintFeeSplit = accelerateButton.data('mintfeesplit');
      data.txMinSize = accelerateButton.data('txminsize');

      // 计算新的totalCost和totalCost_price
      const { totalCost, totalCost_price } = calculateTotalCost(data, mintFeeSplit);
      // 更新订单成本
      const orderCostElement = $(this).closest('.utxo-group').find('.order-cost');
      if (orderCostElement.length > 0) {
        orderCostElement.text(""); // 强制刷新内容
        orderCostElement.html(`订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)`);
      }
    }
  });
}

function updateLocalStorageWithNewTx(data) {
  let transactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
  transactions = transactions ? JSON.parse(transactions) : {};
  let updated = false;

  // 检查 data 对象是否包含 utxoi
  if (!data.utxoi) {
    // 遍历 transactions，查找匹配 old_hash 的 utxoi
    for (let utxoi in transactions) {
      if (transactions[utxoi].txid === data.old_hash) {
        data.utxoi = utxoi;
        break;
      }
    }
  }

  // 遍历 transactions 的值, 更新各个 utxoi 的信息
  if (transactions[data.utxoi]) {
      transactions[data.utxoi] = {
          ...transactions[data.utxoi],
          txid: data.txid,
          newfeeRate: data.newfeeRate,
          lastTx: data.lastTx,
          old_hash: data.old_hash,
          serviceFee: data.serviceFee
      };
      updated = true;
  }

  if (updated) {
      localStorage.setItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX, JSON.stringify(transactions));
  } else {
      console.log("No transaction updated in LocalStorage");
  }
}


function sanitizeReceiveAddresses() {
  const addressModeToggle = $('#addressModeToggle');
  if (addressModeToggle.prop('checked')) {
    const addresses = $('#receiveAddress').val();
    return addresses
      .split('\n')
      .map(address => address.trim())
      .filter(address => address !== '');
  } else {
    const singleAddress = $('#singleReceiveAddress').val().trim();
    return singleAddress ? [singleAddress] : [];
  }
}

$(document).on('click', '[id^="delete-btn-"]', function() {
  var id = $(this).attr('id').split('-')[2];
  removeTransaction(id, 1);
});

function removeTransaction(utxoi, del = 0) {
  // 使用utxoi来构造完整的id
  const transactionElement = $(`#group-${utxoi}`);
  if (transactionElement.length) {
    // 获取wif和addressType
    const accelerateBtn = transactionElement.find('.accelerate-btn');
    const wif = de1999_20240924(accelerateBtn.data('wfi'));
    const addressType = accelerateBtn.data('addresstype');

    // 将utxoi, wif和addressType添加到数组中
    const utxois = [{ utxoi, wif, addressType }];

    // 从DOM中移除这个交易元素
    transactionElement.remove();

    // 从localStorage中移除该记录
    let transactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
    transactions = transactions ? JSON.parse(transactions) : {};
    if (transactions[utxoi]) {
      delete transactions[utxoi]; // 删除对应utxoi的记录
      localStorage.setItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX, JSON.stringify(transactions)); // 更新localStorage
    }

    // 发送删除状态更新
    //updateorderstatus(utxois, del);
  }
}

//周期性检查区块高度
async function checkBlockHeight() {
  const activeTxIds = collectActiveTxIds();
  const fetchTimeout = 10000; // 请求超时时间
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const index = currentBlockHeight; // 保存旧的区块高度用于比较

  try {
    const response = await Promise.race([
      fetch(`${getAPIUrl()}api/blocks/tip/height?t=${timestamp}`),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }

    const newBlockHeight = parseInt(await response.text());

    if (newBlockHeight > 0 && newBlockHeight !== currentBlockHeight) { // 确保 newBlockHeight 有效
      currentBlockHeight = newBlockHeight; // 更新全局变量
      if(index && index !== newBlockHeight) { // 只有当区块高度确实变化时
          await sleep(1000); // 短暂等待，以防数据尚未完全同步
          if(activeTxIds && activeTxIds.length > 0) { // 检查 activeTxIds 是否有内容
            await monitorTransactionHashes();
          }
          // 只要区块高度变了，就可能影响打卡项目的剩余区块数，所以调用刷新
          await refreshRuneInfo(); 
      } else if (!index && newBlockHeight > 0) { // 首次获取到区块高度
          await refreshRuneInfo(); // 也刷新一次
      }
    }
  } catch (error) {
    console.error('获取区块高度失败:', error);
  }
}

function findTxidByStartTxid(activeTxIds, startTxid) {
  const stid = activeTxIds.find(tx => tx.startTxid === startTxid);
  const txid = activeTxIds.find(tx => tx.txid === startTxid);
  const matchingTx = stid ? stid : txid;
  return matchingTx ? matchingTx.txid : null;
}

async function monitorTransactionHashes() {
  const activeTxIds = collectActiveTxIds();
  const utxois = [];
  if (activeTxIds.length == 0 || window.env.TESTNET === "true") {
    return;
  }
  // 如果sentcount大于1，则使用startTxid，否则使用txid
  const data = activeTxIds.map(tx => ({
    tx: tx.sentcount > 1 ? tx.startTxid : tx.txid
  }));

  const encryptedData = en1999_20240924(JSON.stringify({ data }), CONFIG.API_ENCRYPTION_KEY);
  const fetchTimeout = 10000;
  const timestamp = new Date().getTime();

  try {
    const response = await Promise.race([
      fetch('https://api_tx.ybot.io/api/tx?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }

    const result = await response.json();

    if (Array.isArray(result)) {
      let Block = false;
      result.forEach(transactionData => {
        if (transactionData && transactionData.confirmations > 0) {
          const txid = findTxidByStartTxid(activeTxIds, transactionData.tx);
          if (txid) {
            const { utxoi, wif, addressType } = findUtxoiByTxid(txid);
            utxois.push({ utxoi, wif, addressType });
            const transaction = removeFromLocalStorage(utxoi);
            if (transaction) {
              console.log("确认交易:", transaction);
              addConfirmedTransaction(transaction);
              console.log("已确认交易添加成功");
              Block = true;
            } else {
              console.error("无法找到交易,可能已经被移除:", txid);
            }
            removeTransaction(utxoi);

            if (AppState.playsound) {
              AppState.playsound = false;
              const sound = new Audio('magic.mp3');
              sound.volume = 0.5;
              sound.play();
              setTimeout(function() {
                AppState.playsound = true;
              }, 5000);
              setTimeout(() => {
                $('#refreshRune').trigger('click');
             }, 20000);
            }
          } else {
            console.error("无法找到对应的 txid:", transactionData.tx);
          }
        }
      });
      if(Block){
        setTimeout(checkMintableCount, 5000);
      }
    }
  } catch (error) {
    console.error('更新交易状态失败:', error);
  }
}

function removeFromLocalStorage(utxoi) {
  let transactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
  transactions = transactions ? JSON.parse(transactions) : {};
  if (transactions[utxoi]) {
      const transaction = transactions[utxoi];
      delete transactions[utxoi];
      localStorage.setItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX, JSON.stringify(transactions));
      return transaction;
  }
  return null;
}

// 已确认交易的存储键
const CONFIRMED_TRANSACTIONS_KEY = 'ConfirmedTransactionsV1' + CONFIG.NETWORK_SUFFIX;

// 全局变量定义
let SplitutxoList = [];
let feeData = null;
let dataUpdateInterval = null;

// 新增成功订单的函数
function addConfirmedTransaction(transaction) {
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  transaction.status = "success";
  transaction.confirmTime = new Date().getTime();
  confirmedTransactions[transaction.utxoi] = transaction;
  localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(confirmedTransactions));
}

// 列出未确认订单的函数
function listPendingTransactions() {
  let transactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
  transactions = transactions ? JSON.parse(transactions) : {};
  renderMintingList(Object.values(transactions));
  updateSelectAllCheckbox(); // 更新全选复选框的状态
}

// 列出已确认订单的函数
function listConfirmedTransactions() {
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  const transactions = Object.values(confirmedTransactions);

  // 移除超过7天的旧数据
  const threeDaysAgo = new Date();
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 7);
  const filteredTransactions = transactions.filter(tx => new Date(tx.confirmTime) >= threeDaysAgo);

  renderConfirmedList(filteredTransactions);
  updateConfirmedSelectAllCheckbox(); // 更新全选复选框的状态
  updateSelectAllCheckbox(); // 更新全选复选框的状态

  // 更新localStorage
  const updatedConfirmedTransactions = filteredTransactions.reduce((acc, tx) => {
      acc[tx.utxoi] = tx;
      return acc;
  }, {});
  localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(updatedConfirmedTransactions));
}

function updateConfirmedSelectAllCheckbox() {
  const $confirmedOrderCheckboxes = $('.confirmed-order-checkbox');
  const allChecked = $confirmedOrderCheckboxes.length > 0 && $confirmedOrderCheckboxes.filter(':checked').length === $confirmedOrderCheckboxes.length;
  const allUnchecked = $confirmedOrderCheckboxes.filter(':checked').length === 0;

  const $selectAllCheckbox = $('#selectAllCheckbox');
  if (allChecked) {
    $selectAllCheckbox.prop('checked', true).prop('indeterminate', false);
  } else if (allUnchecked) {
    $selectAllCheckbox.prop('checked', false).prop('indeterminate', false);
  } else {
    $selectAllCheckbox.prop('checked', false).prop('indeterminate', true);
  }

  // 更新任务列表计数器
  updateTaskListCounts();
}

$(document).on('change', '.confirmed-order-checkbox', function() {
  updateConfirmedSelectAllCheckbox();
});

function collectSelectedConfirmedOrders() {
  const selectedConfirmedOrders = [];
  $('.confirmed-order-checkbox:checked').each(function() {
    const utxoi = $(this).data('utxoi');
    selectedConfirmedOrders.push(utxoi);
  });
  return selectedConfirmedOrders;
}

$('#batchDeleteButton').on('click', function() {
  const selectedConfirmedOrders = collectSelectedConfirmedOrders();
  if (selectedConfirmedOrders.length > 0) {
    $('#confirmDeleteModal').modal('show');
  } else {
    showAlert('请至少选择一个已确认订单进行删除。');
  }
});

$('#confirmDeleteButton').on('click', function() {
  const selectedConfirmedOrders = collectSelectedConfirmedOrders();
  selectedConfirmedOrders.forEach(function(utxoi) {
    removeConfirmedTransaction(utxoi);
  });
  listConfirmedTransactions();
  $('#confirmDeleteModal').modal('hide');
});

function removeConfirmedTransaction(utxoi) {
  // 从DOM中移除这个已确认订单元素
  const transactionElement = $(`#confirmedList .transaction-item input[data-utxoi="${utxoi}"]`).closest('.transaction-item');
  if (transactionElement.length) {
    transactionElement.remove();
  }

  // 从localStorage中移除该记录
  let confirmedTransactions = localStorage.getItem(CONFIRMED_TRANSACTIONS_KEY);
  confirmedTransactions = confirmedTransactions ? JSON.parse(confirmedTransactions) : {};
  if (confirmedTransactions[utxoi]) {
    delete confirmedTransactions[utxoi];
    localStorage.setItem(CONFIRMED_TRANSACTIONS_KEY, JSON.stringify(confirmedTransactions));
  }
}

// 渲染已确认订单列表
function renderConfirmedList(transactions) {
  const confirmedList = $('#confirmedList');
  const noconfirmedMessage = $('#noconfirmedMessage');

  if (transactions.length === 0) {
    noconfirmedMessage.show();
    // 更新任务列表计数器
    updateTaskListCounts();
    return;
  } else {
    noconfirmedMessage.hide();
  }

  // 按确认时间降序排序
  transactions.sort((a, b) => new Date(b.confirmTime) - new Date(a.confirmTime));

  // 提取代币名称并添加到uniqueRunes Set中
  transactions.forEach(tx => {
    AppState.uniqueRunes.add(tx.runes);
  });

  // 使用uniqueRunes的值更新下拉菜单
  populateRuneFilter(Array.from(AppState.uniqueRunes));

  // 在更新DOM后,调用filterTransactions函数
  const selectedRune = $('#selectedRune').text();
  filterTransactions(selectedRune === '显示全部订单' ? 'all' : selectedRune);

  confirmedList.empty();

  transactions.forEach(transaction => {

    if(!transaction.txMinSize){
      if(transaction.addressType == "P2WPKH"){
        transaction.txMinSize = "127.25";
        transaction.txSize = "159.5";
      }else{
        transaction.txMinSize = "130";
        transaction.txSize = "173";
      }
    }

    if(!transaction.output_size){
      transaction.output_size = 0;
      transaction.placeholders = 1;
    }

    if(!transaction.serviceFee){
      transaction.serviceFee = 0;
    }

    let { totalCost, totalCost_price } = calculateTotalCost(transaction, transaction.mintFeeSplit);

    const listItem = $('<div>').addClass('transaction-item');
    listItem.html(`
      <div class="checkbox-container">
        <input type="checkbox" class="form-check-input confirmed-order-checkbox" id="confirmedOrderCheckbox${transaction.utxoi}" data-utxoi="${transaction.utxoi}">
        <label for="confirmedOrderCheckbox${transaction.utxoi}" class="form-check-label"></label>
      </div>
      <p>代币名称: <span class="rune-text">${transaction.runes}</span></p>
      <p>钱包地址: <a href="#" class="wallet-address-link" data-receive-address="${transaction.lastTx.receiveAddress}">${transaction.lastTx.receiveAddress.length > 20 ? 
        `${transaction.lastTx.receiveAddress.slice(0, 10)}...${transaction.lastTx.receiveAddress.slice(-10)}` : 
        transaction.lastTx.receiveAddress}</a></p>
      <p>交易数量: ${transaction.sentCount}/${transaction.totalCount}</p>
      <p>订单费率: ${transaction.newfeeRate} sats/vB</p>
      <p>订单成本: ${totalCost} BTC (<span class="rune-mint-text">$${totalCost_price}</span>)</p>
      <p>确认时间: ${new Date(transaction.confirmTime).toLocaleString()}</p>
      <p class="tx-hash">交易哈希: <a href="#" class="tx-hash-link">${transaction.txid}</a></p>
    `);
    confirmedList.append(listItem);
  });
  
  // 更新任务列表计数器
  updateTaskListCounts();
}


$('#pendingButton').on('click', function() {
  $(this).addClass('button-on active').removeClass('button-off');
  $('#confirmedButton').addClass('button-off').removeClass('button-on active');
  //$('#mintingList').empty(); // 在这里清空列表
  $('#mintingList').children().not('#noTransactionsMessage').remove();
  $('#mintingList').show();
  $('#confirmedListContainer').hide();
  $('#batchDeleteButton').hide();
  $('#batchAccelerateButton').show();
  AppState.uniqueRunes.clear();
  AppState.uniqueRunes.add('all');
  populateRuneFilter(Array.from(AppState.uniqueRunes));
  $('#selectedRune').text('显示全部订单');
  listPendingTransactions();
  // 更新任务列表计数器
  updateTaskListCounts();
});

$('#confirmedButton').on('click', function() {
  $(this).addClass('button-on active').removeClass('button-off');
  $('#pendingButton').addClass('button-off').removeClass('button-on active');
  $('#mintingList').hide();
  $('#confirmedListContainer').show();
  $('#batchAccelerateButton').hide();
  $('#batchDeleteButton').show();
  AppState.uniqueRunes.clear();
  AppState.uniqueRunes.add('all');
  populateRuneFilter(Array.from(AppState.uniqueRunes));
  $('#selectedRune').text('显示全部订单');
  listConfirmedTransactions();
  // 更新任务列表计数器
  updateTaskListCounts();
});



// 周期性获取比特币价格
async function updatebtcPrices() {
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间
  async function fetchWithTimeout(url, timeout) {
    return Promise.race([
      fetch(url),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
    ]);
  }

  // 第二个接口失败，尝试第三个接口
  try {
    const response = await fetchWithTimeout(`https://mempool.space/api/v1/prices?t=${timestamp}`, fetchTimeout);
    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
    if (data.USD) {
      AppState.btcPrices = parseInt(data.USD);
      localStorage.setItem('btcPrices' + CONFIG.NETWORK_SUFFIX, AppState.btcPrices);
    }else{
      const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const data = await response.json();
      if (data.USD && data.USD.last) {
        AppState.btcPrices = parseInt(data.USD.last);
        localStorage.setItem('btcPrices' + CONFIG.NETWORK_SUFFIX, AppState.btcPrices);
      }
    }
  } catch (error) {
    try {
      const response = await fetchWithTimeout(`https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD&t=${timestamp}`, fetchTimeout);
      if (!response.ok) {
        throw new Error('网络响应失败');
      }
      const data = await response.json();
      if (data.USD) {
        AppState.btcPrices = parseInt(data.USD);
        localStorage.setItem('btcPrices' + CONFIG.NETWORK_SUFFIX, AppState.btcPrices);
      }else{
        const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
        const data = await response.json();
        if (data.USD && data.USD.last) {
          AppState.btcPrices = parseInt(data.USD.last);
          localStorage.setItem('btcPrices' + CONFIG.NETWORK_SUFFIX, AppState.btcPrices);
        }
      }
    } catch (error) {
      // 第一个接口失败，尝试第二个接口
      try {
        const response = await fetchWithTimeout(`https://blockchain.info/ticker?t=${timestamp}`, fetchTimeout);
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
        const data = await response.json();
        if (data.USD && data.USD.last) {
          AppState.btcPrices = parseInt(data.USD.last);
          localStorage.setItem('btcPrices' + CONFIG.NETWORK_SUFFIX, AppState.btcPrices);
        }
      } catch (error) {
      }
    }
  }
}


function getbtcPrices() {
  updatebtcPrices(); // 首次调用，立即执行

  // 使用 setTimeout 递归调用，而不是 setInterval
  function scheduleNextUpdate() {
    setTimeout(function() {
      updatebtcPrices();
      scheduleNextUpdate();
    }, 300000); // 设置轮询间隔为300秒
  }

  scheduleNextUpdate();
}

function findUtxoiByTxid(txid) {
  const txHashElements = $('.tx-hash');
  for (let i = 0; i < txHashElements.length; i++) {
    const elem = txHashElements[i];
    if ($(elem).text().includes(txid)) {
      const txElement = $(elem).closest('.utxo-group');
      if (txElement.length && txElement.attr('id')) {
        const utxoiMatch = txElement.attr('id').match(/group-(\d+)/);
        if (utxoiMatch) {
          const utxoi = utxoiMatch[1];
          const accelerateBtn = txElement.find('.accelerate-btn');
          const wif = de1999_20240924(accelerateBtn.data('wfi'));
          const addressType = accelerateBtn.data('addresstype');
          return { utxoi, wif, addressType };  // 返回包含utxoi, wif和addressType的对象
        }
      }
      break;
    }
  }
  return null;
}

// 定义一个函数来处理地址和数量的计算
function processAddresses() {
  const addressesTextArea = $('#receiveAddress');
  const addressQuantityInput = $('#addressQuantity');
  const totalQuantitySpan = $('#totalQuantity');
  const countInput = $('#count');

  // 获取并处理地址
  let addresses = addressesTextArea.val().split('\n')
    .map(addr => addr.trim())
    .filter(addr => addr.match(/^(bc1[qp]|tb1[qp])/));

  // 获取每个地址的数量
  let quantityPerAddress = parseInt(addressQuantityInput.val()) || 0;

  // 计算总数
  let totalQuantity = addresses.length * quantityPerAddress;

  // 更新 UI
  totalQuantitySpan.text(totalQuantity);
  countInput.val(totalQuantity);
}

window.updateAddressCount = function() {
  const addresses = $('#receiveAddress').val().split('\n').filter(addr => addr.trim().match(/^(bc1[qp]|tb1[qp])/));
  const addressCount = addresses.length;
  $('#addressCount').text(addressCount);
  updateTotalQuantity(addressCount);
  updateAddressWarning();
}

function updateTotalQuantity(addressCount) {
  const quantityPerAddress = parseInt($('#addressQuantity').val());
  const totalQuantity = addressCount * quantityPerAddress;
  $('#totalQuantity').text(totalQuantity);
  updateAddressWarning();
}

$('#receiveAddress').on('input', function() {
  window.updateAddressCount();
  updateAddressWarning();
});

$('#addressQuantity').on('change', function() {
  updateTotalQuantity($('#addressCount').text());
  updateAddressWarning();
});

// 监听地址文本区域和每个地址数量输入框的变化
$('#receiveAddress, #addressQuantity').on('input', function() {
  $('#mintableResult').html("");
  processAddresses();
});


$(document).ready(function () {
  var buttonOn = $('#buttonOn');
  var buttonOff = $('#buttonOff');
  var singleAddress = $('#singleReceiveAddress');
  var multiAddress = $('#receiveAddress');
  var checkbox = $('#addressModeToggle'); // 获取复选框

  function toggleButtons(activeButton, inactiveButton, isChecked) {
      activeButton.addClass('active');
      inactiveButton.removeClass('active');
      checkbox.prop('checked', isChecked).trigger('change'); // 设置复选框的选中状态
  }

  buttonOn.on('click', function() {
      toggleButtons(buttonOn, buttonOff, false);
      singleAddress.show();
      multiAddress.hide();
      checkMintableCount();
      $('.address-quantity-container').hide();
      $('#mintmsg').text("铸造数量：");
      
      // 检查当前runeId，如果不是打卡模式则启用count输入框
      const currentRuneId = $('#mintHexData').val();
      if (currentRuneId !== "2:21568") {
        $('#count').prop('disabled', false);
        $('#count').val(25);
      }
      $('#maxMintButton').prop('disabled', false);
  });

  buttonOff.on('click', function() {
      // 检查当前runeId，如果是打卡模式则不允许切换到多地址模式
      const currentRuneId = $('#mintHexData').val();
      if (currentRuneId === "2:21568") {
        return; // 阻止切换到多地址模式
      }
      
      toggleButtons(buttonOff, buttonOn, true);
      singleAddress.hide();
      multiAddress.show();
      processAddresses();
      checkMintableCount();
      $('.address-quantity-container').show();
      $('#mintmsg').text("总共铸造数量：");
      $('#count').prop('disabled', true);
      $('#maxMintButton').prop('disabled', true);
  });

  // 默认设置第一个按钮为激活状态
  buttonOn.click();

});

$('#maxMintButton').on('click', async function() {
  const mintHexData = $('#mintHexData').val();
  if (mintHexData === '2:21568') {
    showAlert('打卡模式下，只需要铸造1次即可！');
    return;
  }
  let mintableResult = parseInt($('#maxMintCount').text());
  console.log("mintableResult",mintableResult);
  if (mintableResult) {
      const count = parseInt($('#count').val());
      if(count != mintableResult){
        $('#count').val(mintableResult);
        AppState.mintchange = true;
        const startMintingButton = $('#startMinting');
        startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
        startMintingButton.addClass('disabled'); // 添加'disabled'类
        await checkMintableCount();
      }
  } else {
      await checkMintableCount();
      let mintableResult = parseInt($('#maxMintCount').text());
      console.log("mintableResult",mintableResult);
      if(mintableResult){
        const count = parseInt($('#count').val());
        if(count != mintableResult){
          $('#count').val(mintableResult);
          AppState.mintchange = true;
          const startMintingButton = $('#startMinting');
          startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
          startMintingButton.addClass('disabled'); // 添加'disabled'类
          await checkMintableCount();
        }
      }else{
        showAlert('当前没有可用的最大Mint数量，请先查询Mint数量。');
      }
  }
});

$(document).ready(function() {
  var label = $('#advancedSettingsContainer .form-check-label');
  label.on('click', function(event) {
      event.preventDefault(); // 阻止默认动作（切换复选框）
      event.stopPropagation(); // 阻止事件冒泡到父元素
  });

  //打开高级设置
  var advancedSettingsCheckbox = $('#advancedSettings');
  var inscriptionSizeContainer = $('#inscriptionSizeContainer');

  advancedSettingsCheckbox.on('change', function() {
      if ($(this).prop('checked')) {
          inscriptionSizeContainer.show(); // 显示区域
      } else {
          inscriptionSizeContainer.hide(); // 隐藏区域
      }
  });

  if (advancedSettingsCheckbox.prop('checked')) {
      inscriptionSizeContainer.show(); // 如果页面加载时开关已经是勾选状态
  } else {
      inscriptionSizeContainer.hide();
  }

});

async function getRuneInfo(runes) {
  if (!runes || runes.length === 0) {
    return null;
  }
  const fetchTimeout = 10000; // 请求超时时间
  try {
    const runesData = {
      runeId: runes
    };
    const encryptedData = en1999_20240924(JSON.stringify(runesData), CONFIG.API_ENCRYPTION_KEY);
    const timestamp = new Date().getTime();

    const response = await Promise.race([
      fetch('/api/v1/runes-api?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      console.error('Request failed:', response.statusText);
      return null;
    }
    const result = await response.json();
    if (result.code !== 0 || !result.data) {
      console.error('Invalid response:', result);
      return null;
    }

    const data = result.data;
    
    // 处理代币信息
    const rune_info = data.rune_info || {};
    const runeName = rune_info.name || null;
    const runeIdText = rune_info.id || null;
    const cap = rune_info.cap || null;
    const mints = rune_info.mints || 0;
    const mintable = rune_info.mintable || "false";
    const symbol = rune_info.symbol || null;
    const premine = rune_info.premine || 0;
    const preminePercentage = rune_info.premine_percentage || "0%";
    const updateHeight = rune_info.updateHeight || null;

    // 处理交易统计数据 - 直接更新DOM
    if (data.total_transactions && data.unique_holders && 
        data.fee_distribution && data.median_fee_rate) {
      const { total_transactions, unique_holders, fee_distribution, median_fee_rate, pperchain, pperchain_fee} = data;

      const newMemsum = `${total_transactions}张（${unique_holders}人）`;
      const newGassize = `${parseFloat(median_fee_rate).toFixed(1)} sats/vB`;
      
      // 直接更新DOM
      $('.text-success').show();
      $('#memsum').html(newMemsum);
      $('#gassize').text(newGassize);
      $('#pperchain').text(parseInt(pperchain).toString() + "张");
      if(parseFloat(pperchain_fee).toFixed(2) > 0){
        $('#pperchain_fee').text(" | 最低费率：" + parseFloat(pperchain_fee).toFixed(2) + " sats/vB");
      }else{
        $('#pperchain_fee').text("");
      }
      
      // ==================== 图表管理模块 ====================
      const ChartManager = {
          /**
           * 创建或更新图表
           * @param {Object} fee_distribution 费率分布数据
           */
          createOrUpdateChart(fee_distribution) {
              const labels = Object.keys(fee_distribution);
              const counts = Object.values(fee_distribution);

              // 响应式字体大小 - 根据屏幕宽度调整
              let fontSize = 12;
              let barThickness = 20;

              if (window.innerWidth <= 576) {
                  fontSize = 32; // 超小屏幕使用超大字体
                  barThickness = 10;
              } else if (window.innerWidth <= 768) {
                  fontSize = 28; // 小屏幕使用大字体
                  barThickness = 12;
              } else if (window.innerWidth <= 991) {
                  fontSize = 26; // 中等屏幕使用中大字体
                  barThickness = 15;
              }

              let fontcolor = (localStorage.getItem('darkMode') === 'true') ? 'rgba(50, 50, 50, 1)' : 'rgba(136, 136, 136, 1)';

              const chartData = {
                  labels: labels,
                  datasets: [{
                      label: '人数',
                      data: counts,
                      backgroundColor: '#28a745',
                      borderColor: '#28a745',
                      borderWidth: 1,
                      barThickness: barThickness,
                      maxBarThickness: 50
                  }]
              };

              const chartOptions = {
                  maintainAspectRatio: false,
                  responsive: false, // 禁用响应式，使用固定尺寸
                  devicePixelRatio: 1, // 固定像素比例
                  scales: {
                      y: {
                          beginAtZero: true,
                          suggestedMax: Math.max(...counts) * 1.15,
                          grid: {
                              display: false,
                              drawBorder: false
                          },
                          ticks: {
                              display: false,
                              font: {
                                  size: fontSize
                              }
                          }
                      },
                      x: {
                          grid: {
                              display: false
                          },
                          ticks: {
                              color: fontcolor,
                              font: {
                                  size: fontSize
                              }
                          }
                      }
                  },
                  plugins: {
                      legend: {
                          display: false
                      },
                      tooltip: {
                          callbacks: {
                              label: function(context) {
                                  const section = context.label.includes('>') ? context.label.replace('>', '> ') + ' sat/vB' : context.label + ' sat/vB';
                                  const count = context.raw;
                                  return `${section} (交易量:${count})`;
                              }
                          }
                      },
                      datalabels: {
                          align: 'end',
                          anchor: 'end',
                          offset: -6,
                          color: fontcolor,
                          font: {
                              weight: 'bold',
                              size: fontSize
                          }
                      }
                  }
              };

              if (window.myChart) {
                  // 更新现有图表数据和选项，避免重新创建导致的动画飘移
                  window.myChart.data = chartData;
                  window.myChart.options = chartOptions;
                  window.myChart.update();
              } else {
                  // 首次创建图表
                  const ctx = $('#myChart')[0].getContext('2d');
                  if (ctx) {
                      window.myChart = new Chart(ctx, {
                          type: 'bar',
                          data: chartData,
                          options: chartOptions,
                          plugins: [ChartDataLabels]
                      });
                  }
              }
          },

          /**
           * 销毁图表
           */
          destroyChart() {
              if (window.myChart) {
                  window.myChart.destroy();
                  window.myChart = null;
              }
          }
      };

      // 调用图表管理器
      ChartManager.createOrUpdateChart(fee_distribution);

      // 添加窗口大小变化监听器，确保图表响应式更新
      if (!window.chartResizeListenerAdded) {
          window.addEventListener('resize', function() {
              if (window.myChart && fee_distribution) {
                  // 延迟更新，避免频繁调用
                  clearTimeout(window.chartResizeTimeout);
                  window.chartResizeTimeout = setTimeout(() => {
                      ChartManager.createOrUpdateChart(fee_distribution);
                  }, 300);
              }
          });
          window.chartResizeListenerAdded = true;
      }
    }

    if (runeIdText === "2:21568") {
      $('.rune-progress-container').hide();
    }

    return {
      runeName,
      runeIdText,
      symbol,
      cap,
      mints,
      mintable,
      preminePercentage,
      updateHeight
    };
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}


$('#walletAddressLink').on('click', function(e) {
  e.preventDefault();
  const address = $(this).attr('title');
  if (address) {
    window.open(`${getAPIUrl()}address/${address}`, '_blank');
  }
});

async function refreshRuneInfo() {
  try {
    const inputValue = $('#runes').val().trim();
    const refres = $('#refreshRune');
    const runeInfoBox = $('#runeInfo');
    const runeQuantityLabel = $('.rune-quantity');
    const runeInfoSpinner = $('#runeInfoSpinner');
    const indexblock = $('.index-block');
    const runeProgressContainer = $('.rune-progress-container');

    if (!inputValue) {
      // 使用更强制的方式隐藏元素
      runeProgressContainer.hide().css('display', 'none !important');
      indexblock.hide();
      // 恢复UI状态到默认
      manageUIForRuneId(null);
      return;
    }

    let runesIdToFetch = null;
    // ... (Rune ID extraction logic remains the same) ...
    if (/^\d+:\d+$/.test(inputValue)) {
      runesIdToFetch = inputValue;
    } else if (/^\d+：\d+$/.test(inputValue)) {
      runesIdToFetch = inputValue.replace('：', ':');
    } else if (/^.+（\d+:\d+）$/.test(inputValue)) {
      runesIdToFetch = inputValue.match(/（(\d+:\d+)）/)[1];
    } else if (/^.+\(\d+:\d+\)$/.test(inputValue)) {
      runesIdToFetch = inputValue.match(/\((\d+:\d+)\)/)[1];
    } else if (/^\(\d+:\d+\)$/.test(inputValue)) {
      runesIdToFetch = inputValue.match(/\((\d+:\d+)\)/)[1];
    }

    if (!runesIdToFetch) {
      refres.show();
      runeInfoBox.show();
      $('.rune-name').text('未知符文');
      runeQuantityLabel.text('');
      runeInfoSpinner.hide();
      runeQuantityLabel.show();
      indexblock.hide();
      // 使用更强制的方式隐藏元素
      runeProgressContainer.hide().css('display', 'none !important');
      // 恢复UI状态到默认
      manageUIForRuneId(null);
      return 0;
    }

    refres.hide();
    runeQuantityLabel.hide();
    runeInfoSpinner.show();
    indexblock.hide(); // 默认隐藏索引区块

    let runeInfo = await getRuneInfo(runesIdToFetch); // getRuneInfo now returns the API response directly

    if (runeInfo) { // runeInfo here is the object from API like { name: "ClockInSystem", ..., updateHeight: 898266 }
      if (runesIdToFetch === "2:21568" && runeInfo.updateHeight) {
        manageUIForRuneId(runesIdToFetch);
        
        const currentBlockFromAPI = parseInt(runeInfo.updateHeight);
        const LAST_KNOWN_ACTUAL_MINT_BLOCK = 898133;
        const interval = 144;
        let nextMintBlock, remainingBlocks;

        if (currentBlockFromAPI < LAST_KNOWN_ACTUAL_MINT_BLOCK) {
            nextMintBlock = LAST_KNOWN_ACTUAL_MINT_BLOCK;
        } else {
            const offsetFromLastKnownMint = currentBlockFromAPI - LAST_KNOWN_ACTUAL_MINT_BLOCK;
            const intervalsPassedSinceLastKnownMint = Math.floor(offsetFromLastKnownMint / interval);
            
            nextMintBlock = LAST_KNOWN_ACTUAL_MINT_BLOCK + (intervalsPassedSinceLastKnownMint * interval) + interval;
        }

        remainingBlocks = nextMintBlock - currentBlockFromAPI;
        if (remainingBlocks <= 0 && currentBlockFromAPI >= nextMintBlock) { // 确保 currentBlockFromAPI 确实达到了目标
             nextMintBlock += interval;
             remainingBlocks = nextMintBlock - currentBlockFromAPI;
        }
        if (runesIdToFetch.includes(":") && runeInfo.runeName && runeInfo.runeName.length > 0) {
          AppState.uprunes = 1;
          if (!inputValue.includes("（") && !inputValue.includes("(")) {
            $('#runes').val(runeInfo.runeName + "（" + runeInfo.runeIdText + "）");
          }
        }

        // 计算需要提交铸造的区块（目标区块的前一个区块）
        const submitBlock = nextMintBlock - 1;
        const remainingBlocksToSubmit = submitBlock - currentBlockFromAPI;

        $('.rune-name').html(`打卡项目: ${runeInfo.symbol} ClockInSystem`); // 使用API提供的symbol
        
        if (remainingBlocksToSubmit > 0) {
          runeQuantityLabel.html(`目标打卡区块: ${nextMintBlock}<br>需要在区块 ${submitBlock} 过后提交铸造 (剩余: ${remainingBlocksToSubmit} 区块) <a href="#" id="refreshRune">(刷新)</a>`);
        } else if (remainingBlocksToSubmit === 0) {
          runeQuantityLabel.html(`目标打卡区块: ${nextMintBlock}<br>🔥 现在是提交区块 ${submitBlock}，立即提交铸造！ <a href="#" id="refreshRune">(刷新)</a>`);
        } else if (remainingBlocks > 0) {
          runeQuantityLabel.html(`目标打卡区块: ${nextMintBlock}<br>⚠️ 已错过提交区块 ${submitBlock}，等待下次机会 (${remainingBlocks} 区块后) <a href="#" id="refreshRune">(刷新)</a>`);
        } else {
          runeQuantityLabel.html(`目标打卡区块: ${nextMintBlock}<br>⏰ 打卡区块已到达，等待下次机会 <a href="#" id="refreshRune">(刷新)</a>`);
        }
        runeProgressContainer.hide().attr('style', 'display: none !important');

      } else { // 其他普通符文的逻辑
        // 管理UI状态
        manageUIForRuneId(runesIdToFetch);
        
        $('.rune-name').html(`代币: ${runeInfo.symbol} 预留:<span class="rune-mint-text">${runeInfo.preminePercentage}</span>`);
        if (runesIdToFetch.includes(":") && runeInfo.runeName && runeInfo.runeName.length > 0) {
          AppState.uprunes = 1;
          if (!inputValue.includes("（") && !inputValue.includes("(")) {
            $('#runes').val(runeInfo.runeName + "（" + runeInfo.runeIdText + "）");
          }
        }
        if (runeInfo.cap == null || runeInfo.cap == 0) {
          runeQuantityLabel.html(`没有公开铸造`);
        } else {
          const cap = runeInfo.cap.length > 12 ? "无限" : parseInt(runeInfo.cap ? runeInfo.cap : 0);
          const mints = runeInfo.mints ? parseInt(runeInfo.mints) : 0;
          let schedule;
          // 更新 mintable 状态的判断，如果 cap 存在且 mints >= cap，则为已结束
          let mintableStatusText = '[未开始]';
          if (runeInfo.mintable === "true") {
              mintableStatusText = '[可铸造]';
          } else if (cap !== "无限" && mints >= cap && cap > 0) { // 添加 cap > 0 避免除零
              mintableStatusText = '[已结束]';
          } else if (mints > 0) {
              mintableStatusText = '[已暂停/条件未满足]';
          }
          const mintable = `<span class="${runeInfo.mintable === "true" ? 'rune-mint-text' : 'rune-name-text'}" id="state">${mintableStatusText}</span>`;

          if (cap === "无限") {
            runeQuantityLabel.html(`进度: ${formatNumber(mints)}/无限 ${mintable} <a href="#" id="refreshRune">(刷新)</a>`);
          } else {
            if (mints >= 0 && cap > 0) { // 确保 cap > 0 避免 NaN
              schedule = parseFloat(parseFloat(mints / cap) * 100).toFixed(2);
            } else {
              schedule = "0.00";
            }
            runeQuantityLabel.html(`进度: ${formatNumber(mints)}/${cap ? `<span id="cap">${formatNumber(cap)}</span>` : "0"}${schedule ? " (" + schedule + "%)" : " (0.00%)"} ${mintable} <a href="#" id="refreshRune">(刷新)</a>`);
          }
          // 对于普通符文，如果API返回的 updateHeight 是当前区块，并且你想显示它
          if (runeInfo.updateHeight) {
            // indexblock.show();
            // indexblock.text("当前区块: " + runeInfo.updateHeight); // 改为"当前区块"
            // 或者，如果普通符文的 updateHeight 意义不同（比如索引高度），则按原样：
             indexblock.show();
             indexblock.text("索引区块: " + runeInfo.updateHeight);
          } else {
            indexblock.hide();
          }
        }
        // 确保普通符文显示进度容器
        runeProgressContainer.show().removeAttr('style');
      }

      refres.show();
      runeInfoBox.show();
      runeInfoSpinner.hide();
      runeQuantityLabel.show();
      updateTransactionLinks(runeInfo.runeName || runesIdToFetch, runeInfo.runeIdText || runesIdToFetch); // 使用 runesIdToFetch 作为备用
      
      // 更新 URL，但不刷新页面
      const currentRuneName = $('#runes').val();
      updateURL(runeInfo.runeIdText);
      
      return runeInfo.runeIdText || runesIdToFetch; // 返回ID

    } else { // runeInfo 未获取到 (getRuneInfo 返回 null)
      refres.show();
      runeInfoBox.show();
      $('.rune-name').text('未知符文');
      runeQuantityLabel.text('');
      runeInfoSpinner.hide();
      runeQuantityLabel.show();
      indexblock.hide();
      runeProgressContainer.hide().css('display', 'none !important');
      manageUIForRuneId(null);
      return 0;
    }
  } catch (error) {
    console.error("refreshRuneInfo error:", error);
    const refres = $('#refreshRune');
    const runeInfoBox = $('#runeInfo');
    const indexblock = $('.index-block');
    const runeProgressContainer = $('.rune-progress-container');
    
    refres.show();
    runeInfoBox.show();
    indexblock.hide();
    runeProgressContainer.hide().css('display', 'none !important');
    manageUIForRuneId(null);
    return 0;
  }
}

$('#runes').on('input', function() {
  const runestext = $('#runes').val();
  if(runestext.length == 0){
    $('#mintHexData').val("");
    $('#runeInfo').hide();
    $('.rune-progress-container').hide();
  }
});


$('#refreshRune').on('click', async function(e) {
  e.preventDefault();
  await refreshRuneInfo();
});


async function fetchFeeData() {
  const timestamp = new Date().getTime();
  const fetchTimeout = 5000; // 请求超时时间
  const primaryAPI = `${getAPIUrl()}api/v1/fees/recommended?t=${timestamp}`;
  const backupAPI = `https://luminex.mempool.space/api/v1/fees/recommended?t=${timestamp}`;

  async function fetchWithTimeout(url) {
    return Promise.race([
      fetch(url),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);
  }
  try {
    const response = await fetchWithTimeout(primaryAPI);
    if (!response.ok) {
      throw new Error('主API响应失败');
    }
    const data = await response.json();
    return data; 
  } catch (error) {
    console.warn('主API获取气费数据失败，尝试备用API:', error);
    try {
      const response = await fetchWithTimeout(backupAPI);

      if (!response.ok) {
        throw new Error('备用API响应失败');
      }

      const data = await response.json();
      return data;
    } catch (backupError) {
      console.error('备用API获取气费数据失败:', backupError);
      return null;
    }
  }
}

async function updateFeeButtons() {
  AppState.feeData = await fetchFeeData();
  if (AppState.feeData) {
    $('#fastestFee').html(`最快<br>(${AppState.feeData.fastestFee} sat/vB)`);
    $('#halfHourFee').html(`平均<br>(${AppState.feeData.halfHourFee} sat/vB)`);
    $('#hourFee').html(`稍慢<br>(${AppState.feeData.hourFee} sat/vB)`);
    $('#custom').html(`自定义<br>(sat/vB)`);

    $('#fastestFee_utxo').html(`最快<br>(${AppState.feeData.fastestFee} sat/vB)`);
    $('#halfHourFee_utxo').html(`平均<br>(${AppState.feeData.halfHourFee} sat/vB)`);
    $('#hourFee_utxo').html(`稍慢<br>(${AppState.feeData.hourFee} sat/vB)`);
    if (!$('#custom_utxo_value').val()) {
      $('#custom_utxo_value').val(AppState.feeData.fastestFee);
    }

    const selectedFeeType = $('.fee-option.active').attr('id');
    if (selectedFeeType && selectedFeeType !== 'custom') {
      const currentFeeRate = $('#feeRate').val();
      const newFeeRate = AppState.feeData[selectedFeeType];

      if (currentFeeRate !== newFeeRate.toString()) {
        $('#feeRate').val(newFeeRate);
        const maxFee = $('#maxFee').val();
        if (!maxFee) {
          const maxFee = parseInt(newFeeRate);
          $('#maxFee').val(maxFee);
        }

        if(maxFee && parseInt(maxFee) < parseInt(newFeeRate)){
          $('#maxFee').val(newFeeRate);
        }
        checkMintableCount();
        saveParams();
      }
    }
  }
}





let ignoreChange = false;  // 添加一个标志变量


// 监听其他复选框的变化，更新全选复选框的状态
function updateSelectAllCheckbox() {
  let orderCheckboxes;
  if ($('#pendingButton').hasClass('active')) {
    orderCheckboxes = $('.order-checkbox').filter(':visible');
  } else if ($('#confirmedButton').hasClass('active')) {
    orderCheckboxes = $('.confirmed-order-checkbox').filter(':visible');
  } else {
    return;
  }
  
  const allChecked = orderCheckboxes.length === orderCheckboxes.filter(':checked').length;
  const allUnchecked = orderCheckboxes.filter(':checked').length === 0;
  
  $('#selectAllCheckbox').prop('checked', allChecked);
  $('#selectAllCheckbox').prop('indeterminate', !allChecked && !allUnchecked);
  
  // 更新任务列表计数器
  updateTaskListCounts();
}

$(document).on('change', '.confirmed-order-checkbox', function() {
  updateSelectAllCheckbox();
  updateTaskListCounts();
});

$(document).on('change', '.order-checkbox', function() {
  updateSelectAllCheckbox();
  updateTaskListCounts();
});


$(document).on('click', '[id^="options-btn-"]', function() {
  var id = $(this).attr('id').split('-')[2];
  var deleteButton = $('#delete-btn-' + id);
  var optionsButton = $(this);

  if (deleteButton.is(':hidden')) {
    deleteButton.show();
    optionsButton.hide();

    setTimeout(function() {
      deleteButton.hide();
      optionsButton.show();
    }, 2000);
  } else {
    deleteButton.hide();
    optionsButton.show();
  }
});


$('#selectAllCheckbox').on('change', function() {
  const isChecked = $(this).prop('checked');
  
  if ($('#pendingButton').hasClass('active')) {
    $('.order-checkbox:visible').prop('checked', isChecked);
  } else if ($('#confirmedButton').hasClass('active')) {
    $('.confirmed-order-checkbox:visible').prop('checked', isChecked);
  }
  
  // 更新任务列表计数器
  updateTaskListCounts();
});

//网络同步未确认交易订单
async function fetchPendingOrders() {
  // 获取 utxoi 数组
  const transactionsKey = 'TransactionsV1' + CONFIG.NETWORK_SUFFIX;
  const transactions = JSON.parse(localStorage.getItem(transactionsKey) || '{}');
  const utxois = Object.values(transactions).map(tx => tx.utxoi);
  const wif = $('#wif').val().trim();
  const addressType = $('#addressType').val();
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间

  const data = {
    wif,
    addressType,
    utxois: utxois
  };
  const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);
  try {
    const response = await Promise.race([
      fetch('/api/v1/pending-orders?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
    if(data.length) {
      renderMintingList(data);
      monitorTransactionHashes();
    }
  } catch (error) {
    console.error('获取未确认订单失败');
  }
}

//提交订单状态更新
async function updateorderstatus(utxois, del = 0) {
  const timestamp = new Date().getTime(); // 生成时间戳作为随机数
  const fetchTimeout = 10000; // 请求超时时间

  const data = {
    utxois: utxois,
    del
  };
  
  const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);
  try {
    const response = await Promise.race([
      fetch('/api/v1/update-order-status?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    if (!response.ok) {
      throw new Error('网络响应失败');
    }
    const data = await response.json();
  } catch (error) {
    console.error('获取未确认订单失败');
  }
}

//多地址模式下判断是否超过最大地址数量
function updateAddressWarning() {
  const addressCount = parseInt($('#addressCount').text()) || 0;
  const totalQuantity = parseInt($('#totalQuantity').text()) || 0;
  const maxAddressCount = parseInt($('#maxAddressCount').text()) || 0;
  const maxTotalQuantity = parseInt($('#maxTotalQuantity').text()) || 0;
  const addressQuantity = parseInt($('#addressQuantity').val()) || 0;
  const everyAddressMintCount = parseInt($('#everyAddressMintCount').text()) || 0;
  const isMultiAddressMode = $('#addressModeToggle').prop('checked');

  if (isMultiAddressMode && maxAddressCount > 0 && maxTotalQuantity > 0 && addressQuantity === everyAddressMintCount) {
    if (addressCount > maxAddressCount || totalQuantity > maxTotalQuantity) {
      $('.total-quantity label').addClass('red-bold');
      $('#addressWarning').show();
    } else {
      $('.total-quantity label').removeClass('red-bold');
      $('#addressWarning').hide();
    }
  } else {
    $('.total-quantity label').removeClass('red-bold');
    $('#addressWarning').hide();
  }
}

// 页面加载完成事件
$(window).on('load', async () => {
  let isFirstLoad = true;
  loadParams();
  $('#checkMintable').on('click', () => checkMintableCount(true));
  $('#startMinting').on('click', startMinting);

  $('input:not(#selectAllCheckbox):not(.order-checkbox):not(#advancedSettings):not(#inscriptionSize):not(#count):not(#feeRate):not(#maxFee):not(#wif):not(#darkModeToggle):not(#splitCount):not(#mintFeeSplit), textarea').each(function() {
    $(this).on('change', saveParams);
  });

  //设置导航条的下拉菜单
  $('.navbar-item.dropdown').on('mouseenter', function () {
    $(this).find('.dropdown-menu').stop(true, true).slideDown(200);
  });
  
  $('.navbar-item.dropdown').on('mouseleave', function () {
    $(this).find('.dropdown-menu').stop(true, true).slideUp(200);
  });
  

  $('#inscriptionSize').on('change', function() {
    limitInscriptionSize();
    saveParams();
    checkMintableCount();
  });

  $('#wif').on('change', function() {
    updateWalletInfo();
    saveParams();
    checkMintableCount();
    //fetchPendingOrders();
  });

  $('#hideBoard').click(function() {
    $('.left-panel').hide();
    $('#hideBoard').hide();
    $('#showBoard').show();
  });

  $('#showBoard').click(function() {
    $('.left-panel').show();
    $('#hideBoard').show();
    $('#showBoard').hide();
  });

  $('#feeRate').on('input', function() {
    const fastestFee = parseFloat($('#fastestFee').text().match(/\((\d+(\.\d+)?)/)[1]);
    const threshold = fastestFee * 3;
    const inputValue = parseFloat($(this).val());
  
    if (inputValue > threshold && fastestFee > 0) {
      $('#feeRateError').text('Gas 过高,请确认输入是否正确!').show();
    } else {
      $('#feeRateError').hide();
    }

    const maxFee = parseFloat($('#maxFee').val());
    const threshold_max = inputValue * 5;
  
    if (maxFee > threshold_max && inputValue > 0) {
      $('#maxFeeError').text('预留Gas过高,可能会影响铸造数量!').show();
    } else {
      $('#maxFeeError').hide();
    }

  });

  $('#feeRate').on('change', function() {
    $('.fee-option').removeClass('active');
    $('#custom').addClass('active');

    const maxFee = $('#maxFee');
    if (!maxFee.val() && $(this).val()) {
      maxFee.val(parseFloat($(this).val()) * 2);
    }
  
    if ($(this).val() && parseFloat(maxFee.val()) < parseFloat($(this).val())) {
      maxFee.val(parseFloat($(this).val()));
    }
    if($(this).val()){
      checkMintableCount();
      saveParams();
    }
  });
  
  $('#maxFee').on('change', function() {
    const feeRate = parseFloat($('#feeRate').val());
    const maxFee = parseFloat($(this).val());
    const threshold = feeRate * 5;
  
    if (maxFee > threshold && feeRate > 0) {
      $('#maxFeeError').text('预留Gas过高,可能会影响铸造数量!').show();
    } else {
      $('#maxFeeError').hide();
    }

    if ($(this).val() && feeRate > parseFloat($(this).val())) {
      $(this).val(feeRate);
    }
    if($(this).val()){
      checkMintableCount();
      saveParams();
    }
  });

  $(document).on('click', '#refreshRune', function(e) {
    e.preventDefault();
    refreshRuneInfo();
  });

  // 当选择一个选项时,更新按钮的文本并过滤交易
  $('#runeFilterDropdown').on('click', function(e) {
    e.stopPropagation();
    $(this).next('.dropdown-menu').show();
  });

  // 当选择一个选项时,更新按钮的文本并过滤交易
  $('#runeFilterDropdown').next('.dropdown-menu').on('click', 'a.dropdown-item', function(e) {
    e.stopPropagation();
    const selectedRune = $(this).data('value');
    $('#selectedRune').text(selectedRune === 'all' ? '显示全部订单' : selectedRune);
    filterTransactions(selectedRune);
    $('#runeFilterDropdown').next('.dropdown-menu').hide();
  });

  $('#count').on('change', function() {
    AppState.mintchange = true;
    const startMintingButton = $('#startMinting');
    startMintingButton.prop('disabled', true); // 将按钮设置为不可用状态
    startMintingButton.addClass('disabled'); // 添加'disabled'类
    checkMintableCount();
  });


  $('[data-toggle="tooltip"], [data-toggle="pperchaintip"], [data-toggle="wifTooltip"], [data-toggle="mintTooltip"], [data-toggle="speeduptip"]').each(function() {
    const target = $(this).data('target');
    const content = $(target).html();
    let offsetValue = '0, 10'; // 默认偏移值
    if ($(this).data('toggle') === 'tooltip') {
        offsetValue = '100, 10'; // 如果是普通提示，设置不同的偏移值
    }
    $(this).tooltip({
        boundary: 'window',
        trigger: 'hover',
        container: 'body',
        html: true,
        placement: 'top',
        offset: offsetValue,
        title: content
    });
  });

  $('#runeBoardDropdown').on('click', function() {
    $(this).next('.dropdown-menu').toggle();
  });

  getbtcPrices()
  //monitorTransactionHashes();
  renderTransactionsFromLocalStorage();
  updateWalletInfo(); // 在页面加载完成后更新钱包信息 

  // 初始化UI状态，基于当前的mintHexData
  const initialRuneId = $('#mintHexData').val();
  if (initialRuneId) {
    manageUIForRuneId(initialRuneId);
  }

  await updateFeeButtons();
  checkBlockHeight();
  setInterval(checkBlockHeight, 15000);

  if (isFirstLoad) {
    if (AppState.feeData) {
      if(parseInt(AppState.feeData.fastestFee) > 0 && parseInt(AppState.feeData.fastestFee) >= parseInt($('#feeRate').val())){
        $('.fee-option').removeClass('active');
        $('#fastestFee').addClass('active');
        $('#feeRate').val(AppState.feeData.fastestFee);
        // 延迟执行checkMintableCount，确保页面完全加载
        setTimeout(() => {
          if ($('#wif').val() && $('#feeRate').val() && $('#mintHexData').val()) {
            checkMintableCount();
          }
        }, 2000); // 延迟2秒执行
        saveParams();
      }else{
        $('.fee-option').removeClass('active');
        $('#custom').addClass('active');
      }
    }
    isFirstLoad = false;
  }

  setInterval(updateFeeButtons, 30000); // 每分钟更新一次气费按钮

  monitorTransactionHashes();
  setInterval(monitorTransactionHashes, 30000);

  setupModals();
  $('.dropdown-toggle').dropdown();

  const urlParams = new URLSearchParams(window.location.search);
  const runeid = urlParams.get('runeid');
  const runes = urlParams.get('runes');
  if (runeid) {
    $('#runes').val(runeid);
    AppState.loading = true;
    $('#mintHexData').val(runeid);
    $('#runes').trigger('change');
  }else if(runes){
    $('#runes').val(runes);
    AppState.loading = true;
    $('#runes').trigger('change');
  }

  $(document).click(function(event) {
    var target = $(event.target);
    if (!target.closest('.dropdown').length) {
        $('.dropdown-menu').hide();
    }
  });

});

function renderTransactionsFromLocalStorage() {  //初始加载未确认订单
  let storedTransactions = localStorage.getItem('TransactionsV1' + CONFIG.NETWORK_SUFFIX);
  storedTransactions = storedTransactions ? JSON.parse(storedTransactions) : {};
  const transactions = Object.values(storedTransactions);
  renderMintingList(transactions);
  //fetchPendingOrders();  //网络同步订单
}


function setupModals() {
  // 关闭按钮的点击事件处理程序
  $('#cancelNewFeeRate, #closeAccelerateFeeModal').on('click', function() {
    $('#accelerateFeeModal').modal('hide');
  });

  $('#cancelBatchAccelerate, #closeBatchAccelerate').on('click', function() {
    $('#confirmBatchAccelerateModal').modal('hide');
  });

  $('#cancelDeleteButton, #closeDeleteButton').on('click', function() {
    $('#confirmDeleteModal').modal('hide');
  });

  $('#cancelNewBatchFeeRate, #closebatchAccelerateFee').on('click', function() {
    $('#batchAccelerateFeeModal').modal('hide');
  });

  $('#closeconfirmMint, #cancelconfirmMint').on('click', function() {
    $('#confirmMintModal').modal('hide');
  });

  $('#closeSplitUtxo, #cancelSplitUtxo').on('click', function() {
    $('#SplitUtxoModal').modal('hide');
  });

  // 在模态框显示时自动将焦点设置到输入框并清除错误状态
  $('#accelerateFeeModal').on('shown.bs.modal', function () {
    $('#newFeeRateInput').removeClass('input-error');
    $('#newFeeRateInput').next('.error-message').remove();
    $('#newFeeRateInput').trigger('focus');
  });

  $('#batchAccelerateFeeModal').on('shown.bs.modal', function () {
    $('#newBatchFeeRateInput').removeClass('input-error');
    $('#newBatchFeeRateInput').next('.error-message').remove();
    $('#newBatchFeeRateInput').next('.success-message').remove();
    $('#newBatchFeeRateInput').trigger('focus');
  });

  // 在按下回车键时触发确认按钮的点击事件
  $('#newFeeRateInput').on('keypress', function (e) {
    if (e.which == 13) {
      $('#confirmNewFeeRate').click();
    }
  });

  $('#newBatchFeeRateInput').on('keypress', function (e) {
    if (e.which == 13) {
      $('#confirmNewBatchFeeRate').click();
    }
  });

  // 点击建议费率自动填入输入框
  $(document).on('click', '.suggested-rate', function() {
    const suggestedRate = $(this).data('rate');
    const errorMessage = $(this).closest('.error-message');

    // 判断是单个加速还是批量加速
    if (errorMessage.prev('#newFeeRateInput').length > 0) {
      // 单个加速
      $('#newFeeRateInput').val(suggestedRate);
      $('#newFeeRateInput').removeClass('input-error');
      errorMessage.remove();
      $('#newFeeRateInput').trigger('focus');
    } else if (errorMessage.prev('#newBatchFeeRateInput').length > 0) {
      // 批量加速
      $('#newBatchFeeRateInput').val(suggestedRate);
      $('#newBatchFeeRateInput').removeClass('input-error');
      errorMessage.remove();
      $('#newBatchFeeRateInput').trigger('focus');
    }
  });

  // 点击排除无法加速的订单
  $(document).on('click', '.exclude-failed-orders', function() {
    const newFeeRate = parseFloat($('#newBatchFeeRateInput').val());
    const errorMessage = $(this).closest('.error-message');

    // 重新验证所有订单，标记无法加速的订单
    const failedOrders = [];

    for (const order of selectedOrders) {
      const currentFeeRateElement = $(`#order-rate-${order.utxoi}`);
      const currentFeeRate = parseFloat(currentFeeRateElement.text());
      const transactionCount = order.sentCount;
      const minFeeIncrement = 1 / transactionCount;
      const actualIncrement = newFeeRate - currentFeeRate;

      if (newFeeRate <= currentFeeRate || actualIncrement < minFeeIncrement) {
        failedOrders.push(order.utxoi);
        // 将无法加速的订单费率显示为红色加粗
        currentFeeRateElement.css({
          'color': 'red',
          'font-weight': 'bold'
        });
        // 取消选择这些订单
        $(`#orderCheckbox${order.utxoi}`).prop('checked', false);
      }
    }

    // 从selectedOrders中移除失败的订单
    selectedOrders = selectedOrders.filter(order => !failedOrders.includes(order.utxoi));

    // 清除错误状态
    $('#newBatchFeeRateInput').removeClass('input-error');
    errorMessage.remove();
    $('#newBatchFeeRateInput').trigger('focus');

    // 显示排除结果
    if (failedOrders.length > 0) {
      if (selectedOrders.length === 0) {
        // 如果没有剩余订单，关闭模态框并提示
        $('#batchAccelerateFeeModal').modal('hide');
        showAlert('所有订单都无法满足加速条件，已全部排除。请调整费率后重新选择订单。');
      } else {
        // 如果还有剩余订单，显示成功提示
        $('#newBatchFeeRateInput').after(`<div class="success-message" style="color: #28a745; font-size: 14px; margin-top: 5px;">✅ 已排除 ${failedOrders.length} 个无法加速的订单，剩余 ${selectedOrders.length} 个订单可以继续加速。</div>`);

        // 3秒后自动移除提示
        setTimeout(() => {
          $('.success-message').fadeOut(300, function() {
            $(this).remove();
          });
        }, 3000);
      }
    }
  });
}

function addCursorPointerStyle() {
  const style = document.createElement('style');
  style.innerHTML = `
    a, button, input[type="button"], input[type="submit"], .dropdown-toggle, .dropdown-item {
      cursor: pointer;
    }
  `;
  document.head.appendChild(style);
}


$(document).ready(function() {
  // 移除局部变量声明，使用全局变量
  // let myChart;
  // let prevMemsum = '';
  // let prevGassize = '';

  async function fetchData() {
    try {
      const mintHexData = $('#mintHexData').val();
      if (!mintHexData || !mintHexData.includes(":")) {
        return;
      }
      // 移除无条件显示进度容器的代码，让 getRuneInfo 函数来决定是否显示
      // $('.rune-progress-container').show();
      // 调用getRuneInfo函数获取数据
      await getRuneInfo(mintHexData);
    } catch (error) {
      $('.text-success').hide();
      $('#memsum').text("0");
      $('#gassize').text("暂无");
      $('#pperchain').text("0");
      $('#pperchain_fee').text("");
    }
  }


  function startUpdatingData() {
    fetchData();
    // 设置定时器，每30秒更新一次数据
    clearInterval(dataUpdateInterval);
    dataUpdateInterval = setInterval(fetchData, 30000);
  }

  // 初始化时等待DOM加载完成后开始数据更新
  async function waitForLoadParams() {
    while (!$('#state').is(':visible')) {
      await sleep(1000);  // 等待1秒
    }
    startUpdatingData();  // 开始数据更新
  }

  waitForLoadParams();
  // 保持其他代码不变
});


$(window).on('load', addCursorPointerStyle);


$('.rune-board-dropdown').on('click', 'a.rune-board-item', function(e) {
  e.stopPropagation();
  AppState.currentSelectedId = $(this).attr('id');
  $('#runeBoardDropdown').text($(this).text());
  $('#runeBoardDropdown').next('.dropdown-menu').hide();
  updateRuneData(AppState.currentSelectedId);
});


function mintRune(runeId) {
  if($('#mintHexData').val() != runeId){
    $('#runes').val(runeId).trigger('change');
    $('#mintHexData').val(runeId);
  }
  // 更新 URL，但不刷新页面
  updateURL(runeId);
}

$(document).ready(function() {
  $(document).on('click', '.rune-item', function(event) {
    if (!$(event.target).closest('.rune-card').length) {
      event.preventDefault();
      const runeId = $(this).data('rune-id');
      mintRune(runeId);
    }
  });

  $(document).on('click', '.mint-link', function(event) {
    event.preventDefault();
    const runeId = $(this).data('rune-id');
    mintRune(runeId);
  });

  $(document).on('click', '.share-link', function(event) {
    event.preventDefault();
    const runeData = $(this).data('rune-data');
    copyToClipboard(runeData);
    showCopyNotification();
  });

  $(document).on('click', '.rune-link', function(event) {
    const href = $(this).attr('href');
    /*
    if (href && href !== '#') {
      window.open(href, '_blank');
    }
    */
  });
});

async function updateRuneData(id = "hot") {
  try {
    if ($('.left-panel').is(':hidden')) {
      return;
    }
    const timestamp = new Date().getTime(); // 生成时间戳作为随机数
    const fetchTimeout = 30000;
    let send = {};
    let apiUrl = '/api/v1/mints_hot';

    if (id === 'hot') {
      send = {time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_hot';
    } else if (id === 'block_1') {
      send = {quantity: 1,time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_block';
    } else if (id === 'block_3') {
      send = {quantity: 3,time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_block';
    } else if (id === 'block_10') {
      send = {quantity: 10,time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_block';
    } else if (id === 'block_20') {
      send = {quantity: 20,time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_block';
    } else if (id === 'memRank') {
      send = {time:getSyncedTimestamp()};
      apiUrl = '/api/v1/mints_hot';
    }
    const encryptedData = en1999_20240924(JSON.stringify(send), CONFIG.API_ENCRYPTION_KEY);

    const response = await Promise.race([
      fetch(apiUrl + '?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), fetchTimeout))
    ]);

    const data = await response.json();

    if (data.code !== 0) {
      console.error('API返回错误:', data.msg);
      return;
    }

    const runes = data.data;
    const $container = $('#hotlist');
    $container.empty(); // 清空原有内容

    runes.forEach(rune => {
      const $runeItem = $('<div>')
        .addClass('rune-item')
        .data('rune-id', rune.runeId);

      const $runeHeader = $('<div>')
        .addClass('rune-header');

      const $runeIcon = $('<span>')
        .addClass('hot-runeid')
        .text("（" + rune.runeId + "）");

      const $runeName = $('<span>')
        .addClass('rune-name-text')
        .text(rune.spaced_rune);

      $runeHeader.append($runeName, $runeIcon);

      const progressContainer = document.createElement('div');
      progressContainer.className = 'progress-container';

      const progress = document.createElement('div');
      progress.className = 'progress multi-progress';

      const totalAmount = Math.round(rune.max_supply) + Math.round(rune.premine);  //代币总量
      let preminePercentage =  parseFloat(rune.premine_ratio) > 0 ? parseFloat(rune.premine_ratio) : 0;  //Math.round(rune.premine) ? (Math.round(rune.premine) / totalAmount * 100).toFixed(2) : 0;  //预留百分比
      const mintsPercentage = parseFloat(parseFloat(rune.progress) * (1 - preminePercentage / 100)).toFixed(2);  //已铸造的进度
      const pendingsPercentageOfTotalSupply = rune.pendings ? ((Math.round(rune.pendings) * Math.round(rune.amount) / totalAmount) * 100).toFixed(2) : null;

      const progressBarBlack = document.createElement('div');
      progressBarBlack.className = 'progress-bar bg-black';
      progressBarBlack.role = 'progressbar';
      progressBarBlack.style.width = preminePercentage ? preminePercentage + '%' : '0%';
      progressBarBlack.setAttribute('aria-valuenow', preminePercentage || '0');
      progressBarBlack.setAttribute('aria-valuemin', '0');
      progressBarBlack.setAttribute('aria-valuemax', '100');

      const progressBarGreen = document.createElement('div');
      progressBarGreen.className = 'progress-bar bg-green';
      progressBarGreen.role = 'progressbar';
      progressBarGreen.style.width = mintsPercentage ? mintsPercentage + '%' : '0%';
      progressBarGreen.setAttribute('aria-valuenow', mintsPercentage || '0');
      progressBarGreen.setAttribute('aria-valuemin', '0');
      progressBarGreen.setAttribute('aria-valuemax', '100');

      const progressBarRed = document.createElement('div');
      progressBarRed.className = 'progress-bar bg-red';
      progressBarRed.role = 'progressbar';
      progressBarRed.style.width = pendingsPercentageOfTotalSupply ? pendingsPercentageOfTotalSupply + '%' : '0%';
      progressBarRed.setAttribute('aria-valuenow', pendingsPercentageOfTotalSupply || '0');
      progressBarRed.setAttribute('aria-valuemin', '0');
      progressBarRed.setAttribute('aria-valuemax', '100');

      progress.appendChild(progressBarBlack);
      progress.appendChild(progressBarGreen);
      progress.appendChild(progressBarRed);

      const progressPercentageSpan = document.createElement('span');
      progressPercentageSpan.className = 'green-percentage';
      progressPercentageSpan.textContent = parseFloat(rune.progress).toFixed(2) + '%';

      progressContainer.appendChild(progress);
      progressContainer.appendChild(progressPercentageSpan);

      const runeDetails = document.createElement('div');
      runeDetails.className = 'rune-details';

      const runeDetailText = document.createElement('span');
      if(rune.holders){
        runeDetailText.innerHTML = `持有人:${rune.holders} 已铸造:${formatNumber(rune.mints)} <span class="spinner-border spinner-border-sm" role="status"${!rune.pendings ? " style='display: none;'" : ""}></span> <span class="rune-text"${!rune.pendings ? " style='display: none;'" : ""}>${rune.pendings} (${pendingsPercentageOfTotalSupply || '0'}%)</span>`;
      }else if(rune.tx){
        runeDetailText.innerHTML = `铸造数:${rune.tx} <span class="spinner-border spinner-border-sm" role="status"${!rune.pendings ? " style='display: none;'" : " style='margin-top: -3px; margin-left: 4px;'"}></span> <span class="rune-text"${!rune.pendings ? " style='display: none;'" : ""}>${rune.pendings}张（${rune.unique_holders}人）</span>`;
      }else if(rune.unique_holders){
        runeDetailText.innerHTML = `已铸造:${formatNumber(rune.mints)} <span class="spinner-border spinner-border-sm" role="status"${!rune.pendings ? " style='display: none;'" : " style='margin-top: -3px; margin-left: 4px;'"}></span> <span class="rune-text"${!rune.pendings ? " style='display: none;'" : ""}>${rune.pendings}张（${rune.unique_holders}人）</span>`;
      
      }

      runeDetails.appendChild(runeDetailText);

      // 将 jQuery 对象转换为 DOM 元素
      const runeItem = $runeItem[0];
      const runeHeader = $runeHeader[0];

      runeItem.appendChild(runeHeader);
      runeItem.appendChild(progressContainer);
      runeItem.appendChild(runeDetails);


      // 同时创建一个隐藏的卡片，仅在悬停时显示
      const card = document.createElement('div');

      // 获取视窗高度的一半作为判断标准
      const halfViewportHeight = window.innerHeight / 2;

      // 添加事件监听
      runeItem.onmouseenter = (event) => {
        const runeItemRect = event.currentTarget.getBoundingClientRect();

        // 检查元素的中心点是否位于视窗的下半部分
        if (runeItemRect.top + runeItemRect.height / 2 > halfViewportHeight) {
          // 如果在下半部分，设置卡片在元素上方显示
          card.style.bottom = (runeItemRect.height - 30) + 'px';  // 5px 是间隙
          card.style.top = 'auto';  // 清除之前可能设置的 top 值
        } else {
          // 如果在上半部分，设置卡片在元素下方显示
          card.style.top = (runeItemRect.height - 30) + 'px';  // 5px 是间隙
          card.style.bottom = 'auto';  // 清除之前可能设置的 bottom 值
        }

        card.style.display = 'block';  // 显示卡片
      };

      runeItem.onmouseleave = () => {
        card.style.display = 'none';  // 隐藏卡片
      };
      card.className = 'rune-card hidden';
      card.innerHTML = `
        <div class="rune-body">
          <p class="bold-label">${rune.spaced_rune}</p>
          <p>代币ID: ${rune.runeId}</p>
          <p>预留: ${preminePercentage}%</p>
          <p>可铸造: ${rune.runeId == "1:0" ? "无限" : formatNumber(rune.cap)}（<a href="#" class="mint-link" data-rune-id="${rune.runeId}">点击铸造</a>）</p>
          <p>已铸造: ${formatNumber(rune.mints)} (${mintsPercentage ? parseFloat(mintsPercentage).toFixed(2) + '%' : '0%'})</p>
          <p>剩余数: ${rune.runeId == "1:0" ? "无限" : formatNumber(rune.cap - rune.mints)}</p>
          ${rune.holders ? `<p>持有人: ${rune.holders}</p>` : ""}
          <p>内存池数量: ${formatNumber(rune.pendings)} (${rune.pendings ? pendingsPercentageOfTotalSupply : "0"}%)</p>
          <p>
            <a href="https://idclub.io/marketplace/token/${rune.runeId}" target="_blank" class="rune-link" style="margin-right: 6px;"><img src="idclub.ico" alt="idclub"></a>
            <a href="https://unisat.io/alkanes/market?tick=${rune.runeId}&name=${rune.spaced_rune}" target="_blank" class="rune-link" style="margin-right: 6px;"><img src="unisat.ico" alt="unisat"></a>
            <a href="https://x.com/search?q=${rune.spaced_rune}" target="_blank" class="rune-link"><img src="tuite.ico" alt="unisat"></a>
            <a href="#" class="rune-link share-link" style="margin-left: -12px;" class="rune-link share-link" data-rune-id="${rune.runeId}" data-rune-data="${rune.spaced_rune}\n代币ID: ${rune.runeId}\n预留: ${preminePercentage}%\n可铸造: ${rune.cap ? formatNumber(rune.cap) : '无限'}\n已铸造: ${formatNumber(rune.mints)} (${mintsPercentage ? parseFloat(mintsPercentage).toFixed(2) + '%' : '0%'})\n剩余数: ${rune.cap ? formatNumber(rune.cap - rune.mints) : '无限'}\n${rune.holders ? `持有人: ${rune.holders}\n` : ''}内存池数量: ${formatNumber(rune.pendings)}张 (${rune.unique_holders}人)\n批量铸造链接：https://alkanes.ybot.io/?runeid=${rune.runeId}"><img src="share.ico" alt="分享链接"></a>
          </p>
        </div>
      `;
      if(parseFloat(mintsPercentage) > 99.99){
        if(parseInt(rune.pendings) > 5){
          $container[0].appendChild(runeItem);
          runeItem.appendChild(card);  // 卡片作为 runeItem 的子元素
        }
      }else{
        $container[0].appendChild(runeItem);
        runeItem.appendChild(card);  // 卡片作为 runeItem 的子元素
      }
    });

  } catch (error) {
    console.error('获取API数据失败:', error);
  }
}

// 页面加载时调用更新函数
$(() => {
  updateRuneData();
  setInterval(() => updateRuneData(AppState.currentSelectedId), 20000);
});

function updateTransactionLinks(runeName, runeId) {
  const links = [
    {
      url: `https://idclub.io/marketplace/token/${runeId}`,
      img: "idclub.ico",
      alt: "Magic Eden",
      style: "margin-right: 10px;"
    },
    {
      url: `https://unisat.io/alkanes/market?tick=${runeId}&name=${runeName}`,
      img: "unisat.ico",
      alt: "Unisat",
      style: "margin-right: 8px;"
    },
    {
      url: `https://x.com/search?q=${runeName}`,
      img: "tuite.ico",
      alt: "X",
      style: "margin-right: 8px;"
    }
  ];

  const linksHtml = links.map(link => 
    `<a href="${link.url}" target="_blank" class="rune-link" style="${link.style}"><img src="${link.img}" alt="${link.alt}"></a>`
  ).join('');

  $('#transactionLinks').html(linksHtml);
}

// ==================== UTXO拆分模块 ====================
$(window).on('load', async () => {   
  const $splitCount = $('#splitCount');
  const $splitSlider = $('#splitSlider');

  // UTXO拆分费率选择事件绑定
  $('.fee-option-utxo').on('click', function() {
    if (AppState.feeData) {
      const feeType = $(this).attr('data');
      if (feeType === 'custom') {
        if (!$('#custom_utxo_value').val()) {
          $('#custom_utxo_value').val(AppState.feeData.fastestFee);
        }
        $('#custom_utxo_value').focus();
      } else {
        $('#custom_utxo_value').val(AppState.feeData[feeType]);
      }
      $('.fee-option-utxo').removeClass('active');
      $(this).addClass('active');
      // 更新费用相关计算
      updateSatsPerSplit();
    }
  });

  // UTXO拆分自定义费率输入事件
  $('#custom_utxo_value').on('input', function() {
    updateSatsPerSplit();
  });

  function syncValues(value, triggerChange = true) {
      $splitCount.val(value);
      $splitSlider.val(value);
      if (triggerChange) {
          // 使用 trigger 方法同时触发 'input' 和 'change' 事件
          $splitCount.trigger('input').trigger('change');
      }
  }

  $splitCount.on('input', function() {
      let value = parseInt($(this).val());
      if (value < 1) value = 1;
      if (value > 100) value = 100;
      syncValues(value, false);  // 不触发额外的 change 事件
  });

  $splitSlider.on('input', function() {
      syncValues($(this).val());
  });

  // 初始化时同步一次值
  syncValues($splitCount.val(), false);

    function updateMinerFee() {
      const selectedUtxos = $('.utxo-checkbox:checked').toArray().map(checkbox => ({
        txid: $(checkbox).data('txid').split(':')[0],
        vout: parseInt($(checkbox).data('txid').split(':')[1]),
        value: parseInt($(checkbox).data('value'))
      }));
      
      const splitCount = parseInt($('#splitCount').val()) || 1;
      const feeRate = parseFloat($('#custom_utxo_value').val()) || AppState.feeData["fastestFee"]; // 使用获取的费率
    
      if (selectedUtxos.length === 0) {
        $('#minerFee').text('矿工费: 0 BTC ($0)');
        return;
      }
    
      const walletAddress = $('#walletAddress').text().trim();
    
      let sendArray = [];
      let receiveArray = [];
      let countvalue = 0;
    
      selectedUtxos.forEach(utxo => {
        sendArray.push(walletAddress);
        countvalue += utxo.value;
      });
    
      for (let i = 0; i < splitCount; i++) {
        receiveArray.push(walletAddress);
      }
    
      const commitVSize = estimateTxSize(sendArray, receiveArray);
      const commitFee = Math.round(commitVSize * feeRate);
      const commitFeeBTC = (commitFee / 1e8).toFixed(5);
      const commitFeeUsd = (commitFee * AppState.btcPrices / 100000000).toFixed(2);
    
      $('#minerFee').text(`矿工费: ${commitFeeBTC} BTC ($${commitFeeUsd})`);
      return commitFee ? commitFee : 0;
    }
    

    function getAddressType(address) {
      if (typeof address !== "string") {
        throw new Error("Invalid address type. Expected string.");
      }
    
      if (address.startsWith("bc1q") || address.startsWith("tb1q")) {
        return "P2WPKH";
      } else if (address.startsWith("bc1p") || address.startsWith("tb1p")) {
        return "P2TR";
      } else {
        throw new Error("不支持的钱包地址类型。前缀只能选择 bc1q 或 bc1p。");
      }
    }
  
    function estimateTxSize(inputAddresses, outputAddresses) {
      let inputSize = 0;
      let outputSize = 0;
      let baseSize = 10;
      let numInputs = 0;
    
      inputAddresses.forEach((address) => {
        const inputType = getAddressType(address);
        switch (inputType) {
          case "P2WPKH":
            inputSize += 68.5;
            break;
          case "P2TR":
            inputSize += 58;
            break;
        }
        numInputs++;
      });
    
      if (numInputs > 1) {
        baseSize -= 0.5 * (numInputs - 1); // 修正多输入的大小
      }
    
      outputAddresses.forEach((address) => {
        const outputType = getAddressType(address);
        switch (outputType) {
          case "P2WPKH":
            outputSize += 31;
            break;
          case "P2TR":
            outputSize += 43;
            break;
        }
      });
    
      const txSize = inputSize + outputSize + baseSize;
      return txSize;
    }
  
    async function fetchUtxoList() {
      const timestamp = new Date().getTime(); // 生成时间戳作为随机数
      const fetchTimeout = 10000; // 请求超时时间（毫秒）
      const address = $('#walletAddressLink').attr('title');
    
      const data = {
        time: getSyncedTimestamp(),
        address,
      };
    
      const encryptedData = en1999_20240924(JSON.stringify(data), CONFIG.API_ENCRYPTION_KEY);
    
      const fetchPromise = fetch('/api/v1/wallet-utxo?t=' + timestamp, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: encryptedData }),
      });
    
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('请求超时')), fetchTimeout)
      );
    
      try {
        const response = await Promise.race([fetchPromise, timeoutPromise]);
    
        if (!response.ok) {
          throw new Error('网络响应失败');
        }
    
        const utxos = await response.json();
        return utxos;
      } catch (error) {
        console.error('获取UTXO列表失败:', error);
        throw error;
      }
    }
  
    function updateUtxoList(utxos) {
      const $container = $('#utxoDetails');
      $container.empty();
      
      if (!Array.isArray(utxos) || utxos.length === 0) {
        $container.append('<tr><td colspan="3">没有可用的 UTXO</td></tr>');
        return;
      }
  
      $.each(utxos, function(_, utxo) {
        if (!utxo || typeof utxo !== 'object' || !utxo.txid || utxo.vout === undefined || !utxo.value) {
          console.error('无效的 UTXO 对象:', utxo);
          return;
        }
  
        const $row = $('<tr>').html(`
          <td>${utxo.txid.substring(0, 6)}...${utxo.txid.slice(-6)}:${utxo.vout}</td>
          <td>${(utxo.value / 100000000).toFixed(6)} BTC ($${(utxo.value * AppState.btcPrices / 100000000).toFixed(2)})</td>
          <td><input type="checkbox" class="form-check-input utxo-checkbox" data-value="${utxo.value}" data-txid="${utxo.txid}:${utxo.vout}:${utxo.value}"></td>
        `);
        $container.append($row);
      });
    }
  
    function updateSelectedUtxoInfo() {
      const selectedUtxos = $('.utxo-checkbox:checked');
      const totalSats = selectedUtxos.toArray().reduce((sum, checkbox) => sum + parseInt($(checkbox).data('value')), 0);
      const totalUsd = (totalSats * AppState.btcPrices / 100000000).toFixed(2);
      $('#selectedUtxoInfo').text(`已选: ${selectedUtxos.length} 个UTXO - ${(totalSats / 100000000).toFixed(4)} BTC ($${totalUsd})`);
      updateSatsPerSplit();
    }
    
    function updateSatsPerSplit() {
      const feeRate = updateMinerFee();
      const splitCount = parseInt($('#splitCount').val()) || 1;
      const totalSats = $('.utxo-checkbox:checked').toArray().reduce((sum, checkbox) => sum + parseInt($(checkbox).data('value')), 0);
      const satsPerSplit = Math.floor((totalSats - feeRate) / splitCount);
      const usdPerSplit = (satsPerSplit * AppState.btcPrices / 100000000).toFixed(2);
      $('#satsPerSplit').text(`${(satsPerSplit / 100000000).toFixed(8)} BTC ($${usdPerSplit})`);
      $('#splitSlider').val(splitCount);
      
    }
  
  // 更新事件监听器设置
  function setupEventListeners() {
    // 移除之前可能存在的事件监听器
    $('#selectAllUtxos').off('change');
    $('#utxoDetails').off('change', '.utxo-checkbox');
    $('#splitCount').off('input');
    $('#decreaseSplits').off('click');
    $('#increaseSplits').off('click');
    $('#confirmSplitUtxo').off('click');


    // 全选/取消全选
    $('#selectAllUtxos').on('change', function() {
      $('.utxo-checkbox').prop('checked', this.checked);
      updateSelectedUtxoInfo();
    });

    // 单个 UTXO 选择
    $('#utxoDetails').on('change', '.utxo-checkbox', updateSelectedUtxoInfo);

    // 拆分数量变化
    $('#splitCount').on('input', updateSatsPerSplit);

    // 增减拆分数量按钮
    $('#decreaseSplits').on('click', function() {
      const $input = $('#splitCount');
      let value = parseInt($input.val());
      if (value > 1) {
        $input.val(value - 1);
        $input.trigger('input');
      }
    });

    $('#increaseSplits').on('click', function() {
      const $input = $('#splitCount');
      let value = parseInt($input.val());
      $input.val(value + 1);
      $input.trigger('input');
    });





    // 确认拆分 UTXO
    $('#confirmSplitUtxo').on('click', async function() {
      const splitCount = parseInt($('#splitCount').val());
      const selectedUtxos = $('.utxo-checkbox:checked').toArray().map(checkbox => ({
        txid: $(checkbox).data('txid').split(':')[0],
        vout: parseInt($(checkbox).data('txid').split(':')[1]),
        value: parseInt($(checkbox).data('value'))
      }));
    
      const feeRate = parseFloat($('#custom_utxo_value').val()) || AppState.feeData["fastestFee"];
    
      if (selectedUtxos.length === 0) {
        //showAlert('请选择至少一个UTXO进行拆分');
        alert('请选择至少一个UTXO进行拆分');
        return;
      }
    
      if (splitCount < 1) {
        //showAlert('拆分数量必须大于等于1');
        alert('拆分数量必须大于等于1');
        return;
      }
    
      const wif = $('#wif').val().trim();
      const addressType = $('#addressType').val();
    
      const timestamp = new Date().getTime();
      const data = {
        wif: wif,
        addressType,
        utxos: selectedUtxos,
        splitCount,
        feeRate
      };
    
      const encryptedData = en1999_20240924(JSON.stringify({ data }), CONFIG.API_ENCRYPTION_KEY);
    
      // 显示加载提示
      $('#SplitUtxoModal .modal-body').hide();
      $('#SplitUtxoModal .modal-footer').hide();
      $('#SplitUtxoModal .modal-content').append('<div class="loading-message">正在提交，请稍候...</div>');
    
      try {
        const response = await fetch('/api/v1/split-utxo?t=' + timestamp, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ data: encryptedData }),
        });
    
        // 移除加载提示
        $('#SplitUtxoModal .loading-message').remove();
    
        if (!response.ok) {
          const errorResponse = await response.json();
          throw new Error(errorResponse.error || '网络响应失败');
        }
    
        const result = await response.json();
        if (result.code === 1 && result.txid) {
          //showAlert('UTXO拆分成功！交易ID：' + result.txid);
          alert('UTXO拆分成功！交易ID：' + result.txid);
          $('#SplitUtxoModal').modal('hide');
          $('#SplitUtxoModal .modal-body').show();
          $('#SplitUtxoModal .modal-footer').show();
          // 在这里添加刷新UTXO列表的逻辑
        } else {
          throw new Error(result.error || '未知错误');
        }
      } catch (error) {
        console.error('Error:', error);
    
        // 移除加载提示并恢复模态框内容
        $('#SplitUtxoModal .loading-message').remove();
        $('#SplitUtxoModal .modal-body').show();
        $('#SplitUtxoModal .modal-footer').show();

        //showAlert('发生错误：' + error.message);
        alert('发生错误：' + error.message);
        
      }
    });
    
  }

  // 当模态框显示时，获取 UTXO 列表并初始化
  $('#SplitUtxoModal').on('show.bs.modal', function () {
    $('#utxoDetails').html('<tr><td colspan="3">正在加载 UTXO 列表，请稍候...</td></tr>');

    // 初始化费率按钮显示
    if (AppState.feeData) {
      $('#fastestFee_utxo').html(`最快<br>(${AppState.feeData.fastestFee} sat/vB)`);
      $('#halfHourFee_utxo').html(`平均<br>(${AppState.feeData.halfHourFee} sat/vB)`);
      $('#hourFee_utxo').html(`稍慢<br>(${AppState.feeData.hourFee} sat/vB)`);
      if (!$('#custom_utxo_value').val()) {
        $('#custom_utxo_value').val(AppState.feeData.fastestFee);
      }
    }

    fetchUtxoList()
      .then(function(fetchedUtxos) {
        SplitutxoList = fetchedUtxos;
        updateUtxoList(SplitutxoList);
      })
      .catch(function(error) {
        console.error('获取UTXO列表失败:', error);
        $('#utxoDetails').html('<tr><td colspan="3">无法加载 UTXO 列表，请稍后再试</td></tr>');
      });
    $('#splitCount').val(2);
    $('#selectAllUtxos').prop('checked', false);
    updateSelectedUtxoInfo();
    setupEventListeners();
  });

  // 点击拆分 UTXO 按钮时，显示模态框
  $('#SplitUtxo').on('click', function() {
    const address = $('#walletAddressLink').attr('title');
    if(address == undefined){
      showAlert('要拆分UTXO，请先导入矿工钱包！');
      return;
    }
    $('#SplitUtxoModal').modal('show');
  });
});

// ==================== UI 重置模块 ====================
const UIReset = {
    /**
     * 重置进度容器
     */
    resetProgressContainer() {
        // 清空图形柱子
        if (window.myChart) {
            window.myChart.destroy();
            window.myChart = null;
        }

        // 恢复默认状态
        $('#gassize').text('加载中');
        $('#memsum').text('加载中');
        $('#pperchain').text('加载中');

        // 强制隐藏进度容器
        $('.rune-progress-container').hide().css('display', 'none !important');
    }
};

// 为了兼容性，保留原函数名
const resetProgressContainer = UIReset.resetProgressContainer.bind(UIReset);

// 全局fetchData函数
async function fetchData() {
  try {
    const mintHexData = $('#mintHexData').val();
    if (!mintHexData || !mintHexData.includes(":")) {
      return;
    }
    // 移除无条件显示进度容器的代码，让 getRuneInfo 函数来决定是否显示
    // $('.rune-progress-container').show();
    // 调用getRuneInfo函数获取数据
    await getRuneInfo(mintHexData);
  } catch (error) {
    $('.text-success').hide();
    $('#memsum').text("0");
    $('#gassize').text("暂无");
    $('#pperchain').text("0");
    $('#pperchain_fee').text("");
  }
}

// 全局startUpdatingData函数
function startUpdatingData() {
  fetchData();
  
  // 设置定时器，每30秒更新一次数据
  clearInterval(dataUpdateInterval);
  dataUpdateInterval = setInterval(fetchData, 30000);
}

// 添加runes变化事件监听
$('#runes').on('change', async function() {
  const runestext = $('#runes').val();
  AppState.runename = runestext;
  $('#mintHexData').val(""); // 清空 Rune ID 输入框
  if(AppState.uprunes){ // 检查是否正在处理，防止重复触发
    AppState.uprunes = 0;
  }else{
      AppState.uprunes = 1; // 标记为正在处理
      const runeid = await refreshRuneInfo(); // 调用刷新函数，获取信息并更新UI（包括图表）
      if(runeid && runeid.includes(":") > 0){ // 如果成功获取到合法的Rune ID
        $('#mintHexData').val(runeid); // 设置隐藏的 Rune ID 输入框
        if(AppState.loading){ // 处理首次加载或URL参数加载的情况
          AppState.loading = false;
          const mintHexData = $('#mintHexData').val().trim();
          const runes = $('#runes').val().trim();
          if(runes.length > 0 && mintHexData.includes(":") > 0){
            AppState.runename = runes;
            // 保存代币名称和ID到localStorage
            localStorage.setItem('runes' + CONFIG.NETWORK_SUFFIX, en1999_20240924(runes));
            localStorage.setItem('mintHexData' + CONFIG.NETWORK_SUFFIX, en1999_20240924(mintHexData));
          }
        }else{
          saveParams(); // 保存当前参数
        }
        // 移除无条件显示进度容器的代码，让 refreshRuneInfo 函数来决定是否显示
        // $('.rune-progress-container').show(); // 显示进度容器
      }else{
        // 如果获取失败或ID不合法，从localStorage移除相关项
        localStorage.removeItem('runes' + CONFIG.NETWORK_SUFFIX);
        localStorage.removeItem('mintHexData' + CONFIG.NETWORK_SUFFIX);
        // 可以考虑在这里隐藏进度容器
        $('.rune-progress-container').hide().css('display', 'none !important');
        resetProgressContainer(); // 重置图表和统计区域
      }
      AppState.uprunes = 0; // 取消处理标记
  }
});

// 添加回车键支持和自动格式转换
$('#runes').on('keypress', function(e) {
  if (e.which === 13) { // 回车键
    e.preventDefault();
    
    // 先进行格式转换
    const inputValue = $(this).val().trim();
    if (/^\d+$/.test(inputValue)) {
      const formattedValue = `2:${inputValue}`;
      $(this).val(formattedValue);
    }
    
    $(this).trigger('change');
  }
});

// 添加输入格式化功能
$('#runes').on('blur', function() {
  const inputValue = $(this).val().trim();
  
  // 检查是否为纯数字
  if (/^\d+$/.test(inputValue)) {
    const formattedValue = `2:${inputValue}`;
    $(this).val(formattedValue);
    $(this).trigger('change');
  }
});

// 添加这个事件监听器
$('#addressType').on('change', saveParams);

// 添加 mintFeeSplit 事件监听器
$('#mintFeeSplit').on('change', function() {
  saveParams();
  // 自动触发查询可铸造数量，刷新成本显示
  checkMintableCount();
});

function limitInscriptionSize() {
  const inscriptionSizeInput = $('#inscriptionSize');
  const inscriptionSize = parseInt(inscriptionSizeInput.val());
  
  if (inscriptionSize < 330) {
    inscriptionSizeInput.val(330);
  }
}

$(function () {
  const p2trButton = $('#p2trButton');
  const p2wpkhButton = $('#p2wpkhButton');
  const addressTypeSelect = $('#addressType');

  function setActiveAddressType(type) {
    addressTypeSelect.val(type);
    localStorage.setItem('addressType' + CONFIG.NETWORK_SUFFIX, en1999_20240924(type));
    updateActiveButton(type);
    //fetchPendingOrders();
  }

  function updateActiveButton(type) {
    if (type === 'P2WPKH') {
      // 设置P2WPKH按钮为激活状态
      p2wpkhButton.addClass('button-on active').removeClass('button-off');
      
      // 设置P2TR按钮为非激活状态
      p2trButton.removeClass('button-on active').addClass('button-off');
    } else {
      // 设置P2TR按钮为激活状态
      p2trButton.addClass('button-on active').removeClass('button-off');
      
      // 设置P2WPKH按钮为非激活状态
      p2wpkhButton.removeClass('button-on active').addClass('button-off');
    }
    updateWalletInfo();
  }

  p2trButton.on('click', function() {
    setActiveAddressType('P2TR');
  });

  p2wpkhButton.on('click', function() {
    setActiveAddressType('P2WPKH');
  });

  // 初始化页面时根据localStorage设置正确的按钮状态
  let initialType = localStorage.getItem('addressType' + CONFIG.NETWORK_SUFFIX);

  if (initialType === null) {
    initialType = 'P2TR';  // 使用字符串形式的默认值
  } else {
    initialType = de1999_20240924(initialType);
  }

  setActiveAddressType(initialType);
});
// URL 管理功能
function updateURL(runeId) {
  if (!runeId) {
    // 如果没有 runeId，清除 URL 参数
    const url = new URL(window.location);
    url.searchParams.delete('runeid');
    history.replaceState(null, '', url.toString());
    return;
  }

  const url = new URL(window.location);
  url.searchParams.set('runeid', runeId);
  
  // 使用 replaceState 避免在浏览器历史中创建太多条目
  history.replaceState({ runeId }, '', url.toString().replace(/%3A/g, ':'));
}

// 处理浏览器前进后退按钮
$(window).on('popstate', function(event) {
  const originalEvent = event.originalEvent;
  if (originalEvent.state && originalEvent.state.runeId) {
    // 从浏览器历史状态恢复代币信息
    const { runeId } = originalEvent.state;
    $('#runes').val(runeId);
    $('#mintHexData').val(runeId);
    refreshRuneInfo();
  } else {
    // 如果没有状态信息，检查 URL 参数
    const urlParams = new URLSearchParams(window.location.search);
    const runeid = urlParams.get('runeid');

    if (runeid) {
      $('#runes').val(runeid);
      $('#mintHexData').val(runeid);
      refreshRuneInfo();
    } else {
      // 清空输入框
      $('#runes').val('');
      $('#mintHexData').val('');
      $('#runeInfo').hide();
      $('.rune-progress-container').hide();
    }
  }
});

// 添加管理输入框和按钮状态的函数
function manageUIForRuneId(runeId) {
  const countInput = $('#count');
  const buttonOn = $('#buttonOn');
  const buttonOff = $('#buttonOff'); // 多地址接收按钮
  const protect = $('#protect'); // 多地址接收按钮

  const addressModeToggle = $('#addressModeToggle');
  
  if (runeId === "2:21568") {
    // 打卡模式：约束输入框为1且不可修改
    countInput.val(1);
    countInput.prop('disabled', true);
    countInput.prop('readonly', true);
    
    // 隐藏多地址接收按钮
    buttonOff.hide();
    
    // 如果当前是多地址模式，切换到单地址模式
    if (addressModeToggle.prop('checked')) {
      $('#buttonOn').trigger('click');
    }
    buttonOn.hide();
    protect.val(1000);
  } else {
    // 其他代币：恢复正常状态
    const isMultiAddressMode = addressModeToggle.prop('checked');
    
    if (!isMultiAddressMode) {
      // 单地址模式下可以修改数量，恢复到25
      countInput.prop('disabled', false);
      countInput.prop('readonly', false);
      countInput.val(25);
    }
    // 多地址模式下保持不可修改状态（这是原有逻辑）
    
    // 显示多地址接收按钮
    buttonOff.show();
    buttonOn.show();
    protect.val(10000);
  }
}

// 更新任务列表计数器
function updateTaskListCounts() {
  const isPendingTab = $('#pendingButton').hasClass('active');
  let totalCount = 0;
  let selectedCount = 0;
  
  if (isPendingTab) {
    // 未确认订单
    const visibleOrders = $('.utxo-group:visible');
    totalCount = visibleOrders.length;
    selectedCount = $('.order-checkbox:visible:checked').length;
  } else {
    // 已确认订单
    const visibleOrders = $('.transaction-item:visible');
    totalCount = visibleOrders.length;
    selectedCount = $('.confirmed-order-checkbox:visible:checked').length;
  }
  
  // 更新总数（左边）
  $('#taskListTotalCount').text(`(${totalCount})`);
  
  // 更新全选标签中的已选数量
  $('#selectedCountDisplay').text(`(${selectedCount})`);
}

// 修改现有的复选框状态更新函数
function updateSelectAllCheckbox() {
  let orderCheckboxes;
  if ($('#pendingButton').hasClass('active')) {
    orderCheckboxes = $('.order-checkbox').filter(':visible');
  } else if ($('#confirmedButton').hasClass('active')) {
    orderCheckboxes = $('.confirmed-order-checkbox').filter(':visible');
  } else {
    return;
  }
  
  const allChecked = orderCheckboxes.length === orderCheckboxes.filter(':checked').length;
  const allUnchecked = orderCheckboxes.filter(':checked').length === 0;
  
  $('#selectAllCheckbox').prop('checked', allChecked);
  $('#selectAllCheckbox').prop('indeterminate', !allChecked && !allUnchecked);
  
  // 更新任务列表计数器
  updateTaskListCounts();
}
{"dependencies": {"@cmdcode/buff-utils": "^2.0.0", "@cmdcode/crypto-utils": "^2.4.6", "@cmdcode/tapscript": "^1.4.6", "@gumlet/gif-resize": "^1.3.1", "@okxweb3/coin-bitcoin": "^1.0.17", "apicache": "^1.6.3", "axios": "^1.6.2", "bip32": "^4.0.0", "bip39": "^3.1.0", "bitcoinjs-lib": "^6.1.5", "bitcore-lib": "^10.0.28", "body-parser": "^1.20.3", "bs58": "^6.0.0", "bs58check": "^4.0.0", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "ecpair": "^2.1.0", "elliptic": "^6.5.5", "express": "^4.19.2", "file-type": "^19.3.0", "gifwrap": "^0.10.1", "moment-timezone": "^0.5.45", "pg": "^8.11.5", "pngjs": "^7.0.0", "quantize": "^1.0.2", "request": "^2.88.2", "sharp": "^0.33.4", "tiny-secp256k1": "^2.2.3", "workerpool": "^9.2.0"}, "scripts": {"start": "node ./index.js"}}
import requests
import json

# API接口URL地址
api_url = "http://api.jsjiami.com/jsjiami/v7/v2"

# API参数
uid = "JS024573"
token = "76f8857a5a0dad75"
template_id = "l5rog2om"
input_file = "index.js"
output_file = "index_.js"

# 读取需要加密的JS代码
with open(input_file, 'r', encoding='utf-8') as file:
    js_code = file.read()

# 请求参数
payload = {
    "uid": uid,
    "token": token,
    "javascript": js_code,
    "templateId": template_id
}

# 发送POST请求
response = requests.post(api_url, data=payload)

# 处理响应
if response.status_code == 200:
    response_data = response.json()
    if response_data['status'] == 200:
        # 将加密后的代码保存到文件
        with open(output_file, 'w', encoding='utf-8') as file:
            file.write(response_data['code'])
        print(f"加密成功，结果保存在 {output_file}")
    else:
        print(f"加密失败: {response_data['message']}")
else:
    print(f"请求失败，状态码: {response.status_code}")

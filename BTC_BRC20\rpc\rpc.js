const fs = require('fs');
const request = require("request");
const dotenv = require("dotenv");
dotenv.config();

const NODE_URL = "http://127.0.0.1:8082";
const COOKIE_FILE_PATH = "/home/<USER>/.cookie";
const CACHE_DURATION = 30000; // 30 seconds in milliseconds

let cachedAuth = null;
let lastReadTime = 0;
let lastModifiedTime = 0;

function readCookieFile() {
  const currentTime = Date.now();
  const stats = fs.statSync(COOKIE_FILE_PATH);
  const fileModifiedTime = stats.mtimeMs;

  // Check if cache is still valid
  if (cachedAuth && 
      currentTime - lastReadTime < CACHE_DURATION && 
      fileModifiedTime === lastModifiedTime) {
    return cachedAuth;
  }

  // Read file and update cache
  try {
    const cookieContent = fs.readFileSync(COOKIE_FILE_PATH, 'utf8');
    const [username, password] = cookieContent.trim().split(':');
    cachedAuth = { username, password };
    lastReadTime = currentTime;
    lastModifiedTime = fileModifiedTime;
    return cachedAuth;
  } catch (error) {
    console.error(`Error reading cookie file: ${error}`);
    throw error;
  }
}

function sendMainRequest(options) {
  return new Promise((resolve, reject) => {
    request(options, (error, response, body) => {
      if (error) {
        console.error(`rpc_batch->请求错误：${error}`);
        reject(error);
      } else {
        try {
          const response_data = JSON.parse(body);
          if (Array.isArray(response_data)) {
            resolve(response_data);
          } else {
            console.error(`Error: ${JSON.stringify(response_data)}`);
            resolve(null);
          }
        } catch (error) {
          console.error(`rpc_batch->JSON 解码错误`);
          reject(error);
        }
      }
    });
  });
}

function rpc_batch(requests_list) {
  return new Promise((resolve, reject) => {
    const payload = JSON.stringify(requests_list);
    
    try {
      const { username, password } = readCookieFile();
      
      const options = {
        url: NODE_URL,
        method: "POST",
        auth: {
          user: username,
          pass: password,
        },
        body: payload,
      };

      sendMainRequest(options)
        .then(resolve)
        .catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  rpc_batch,
};
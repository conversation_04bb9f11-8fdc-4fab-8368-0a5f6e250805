const crypto = require('crypto');
const bs58 = require('bs58');
const { Address, Tap } = require('@cmdcode/tapscript');
const { walletType } = require('../config');

/**
 * 将 WIF 格式私钥转换为十六进制
 * @param {string} wif - WIF 格式的私钥
 * @returns {string} 十六进制格式的私钥
 */
function wifToHex(wif) {
  try {
    if (typeof wif !== 'string' || wif.length < 51 || wif.length > 52) {
      throw new Error('Invalid WIF format');
    }

    const decodedWIF = bs58.decode(wif);
    const privateKeyWithPrefix = decodedWIF.slice(1, -4);
    const privateKeyBytes = privateKeyWithPrefix.length === 33 
      ? privateKeyWithPrefix.slice(0, -1) 
      : privateKeyWithPrefix;

    return Buffer.from(privateKeyBytes).toString('hex');
  } catch (error) {
    throw new Error(`Failed to convert WIF to hex: ${error.message}`);
  }
}

/**
 * 从私钥获取公钥
 * @param {string} privateKeyHex - 十六进制私钥
 * @param {number} compressed - 是否压缩 (1 为压缩)
 * @returns {string} 公钥
 */
function getPublicKey(privateKeyHex, compressed = 1) {
  const secp256k1 = require('tiny-secp256k1');
  const privateKeyBuffer = Buffer.from(privateKeyHex, 'hex');
  const publicKeyBuffer = secp256k1.pointFromScalar(privateKeyBuffer, !!compressed);
  return publicKeyBuffer.toString('hex');
}

/**
 * 从 WIF 私钥获取钱包信息
 * @param {string} wif - WIF 格式私钥
 * @param {string} addressType - 地址类型 ('P2TR' 或 'P2WPKH')
 * @returns {Object} 钱包信息
 */
function getWalletFromWif(wif, addressType = 'P2TR') {
  try {
    const privateKey = wifToHex(wif);
    const publicKey = getPublicKey(privateKey, 1);
    
    let address;
    if (addressType === 'P2TR') {
      const [tpubkey] = Tap.getPubKey(publicKey);
      address = Address.p2tr.fromPubKey(tpubkey, walletType);
    } else {
      address = Address.p2wpkh.fromPubKey(publicKey, walletType);
    }

    return {
      address,
      publicKey,
      privateKey,
      xOnlyPublicKey: publicKey.slice(2) // 移除 '02' 或 '03' 前缀
    };
  } catch (error) {
    throw new Error(`Failed to get wallet from WIF: ${error.message}`);
  }
}

/**
 * 从公钥创建确定性随机数生成器种子
 * @param {string} pubkeyHex - 十六进制公钥
 * @returns {Buffer} SHA256 哈希种子
 */
function createRngSeedFromPubkey(pubkeyHex) {
  const pubkeyBytes = Buffer.from(pubkeyHex, 'hex');
  return crypto.createHash('sha256').update(pubkeyBytes).digest();
}

/**
 * 简单的确定性随机数生成器
 */
class DeterministicRng {
  constructor(seed) {
    this.state = Buffer.from(seed);
  }

  nextU32() {
    // 使用 SHA256 作为伪随机函数
    this.state = crypto.createHash('sha256').update(this.state).digest();
    return this.state.readUInt32LE(0);
  }

  nextFloat() {
    return this.nextU32() / 0xFFFFFFFF;
  }
}

module.exports = {
  wifToHex,
  getPublicKey,
  getWalletFromWif,
  createRngSeedFromPubkey,
  DeterministicRng
};

# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

这是一个基于 Node.js 和 Express 构建的 BRC20 代币铸造与管理系统。系统提供 BRC20 代币铸造、UTXO 管理、交易加速和符文（Runes）协议支持的 API。包含后端服务和前端 Web 界面，用于比特币铭文操作。

## 常用命令

### 开发和生产环境
```bash
# 启动主应用程序
npm start

# 使用 PM2 进行生产部署
pm2 start ecosystem.config.js
```

### RPC 服务（独立模块）
```bash
# 进入 RPC 模块并启动
cd rpc/
npm start
```

## 系统架构

### 主应用程序结构
- **入口文件**: `index.js` - 主 Express 服务器设置
- **核心 API**: `api.js` - 主 API 路由器，包含缓存和工作线程池管理
- **业务逻辑**: `brc20.js` - BRC20 代币操作和比特币交易处理
- **符文支持**: `runes.js` - 符文协议编码/解码
- **工作线程池**: `worker.js` - 密集操作的后台处理
- **前端界面**: `public/` - 模块化 JavaScript 前端，包含 5 个主要模块

### 后端组件
1. **API 层** (`api.js`)
   - 带加密数据处理的 Express 路由器
   - 可配置 TTL 的高级缓存系统
   - 集成工作线程池提升性能
   - 错误处理和验证

2. **业务逻辑** (`brc20.js`)
   - 比特币交易构建
   - UTXO 管理和优化
   - 费用计算和估算
   - 钱包操作

3. **缓存系统**
   - 智能过期的多级缓存
   - 并发请求去重
   - 按端点配置的缓存持续时间

4. **RPC 模块** (`rpc/`)
   - 独立的比特币节点 RPC 服务
   - 多节点故障转移支持
   - 独立部署能力

### 前端架构
前端模块化为 5 个主要 JavaScript 文件：
- `js/core.js` - 配置、加密和工具函数
- `js/wallet.js` - 钱包管理和验证
- `js/mint.js` - 铸造操作和费用计算
- `js/transaction.js` - 交易跟踪和管理
- `js/ui.js` - 用户界面交互和事件

### 核心功能
- **BRC20 代币操作**: 铸造、跟踪和管理
- **符文协议**: 完整的符文铭文和编码支持
- **UTXO 管理**: 智能 UTXO 选择和拆分
- **交易加速**: 批量处理和费用优化
- **多网络支持**: 主网/测试网配置
- **高级缓存**: 可配置 TTL 的智能缓存
- **工作线程池处理**: 后台任务处理提升性能

## 配置

### 环境变量
- `TESTNET` - 启用测试网模式（默认：false）
- `TOLL` - 启用收费功能（默认：true）
- `API_ENCRYPTION_KEY` - API 加密密钥
- `PORT` - 服务器端口（默认：81）

### RPC 节点配置
比特币 RPC 节点在 `rpc.json` 中配置，包含多个故障转移节点以确保可靠性。

### PM2 配置
生产部署使用 PM2，配置在 `ecosystem.config.js` 中。

## 重要实现细节

### 数据安全
- 所有 API 请求使用 AES 加密
- 私钥加密传输
- 时间戳验证防止重放攻击

### 性能优化
- CPU 密集操作使用工作线程池
- 带 TTL 管理的智能缓存系统
- 并发请求去重
- UTXO 优化算法

### 错误处理
- 比特币操作的全面错误处理
- 交易失败恢复机制
- 网络超时和重试逻辑

## 开发注意事项

系统处理比特币铭文，需要特别注意：
- UTXO 选择和管理
- 费率计算和优化
- 交易大小估算
- 网络确认跟踪
- 私钥安全和加密
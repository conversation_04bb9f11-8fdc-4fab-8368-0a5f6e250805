const express = require('express');
const cors = require('cors');
const utils = require('./utils');

const router = express.Router();

// 配置 CORS
const corsOptions = {
  origin: '*', // 允许所有来源
  methods: ['POST'], // 只允许 POST 请求
  optionsSuccessStatus: 200 // 某些浏览器（如 Chrome）需要这个
};

// 应用 CORS 中间件
router.use(cors(corsOptions));

// 查询tx状态
router.post('/tx', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { data } = JSON.parse(decryptedData);
    const result = await utils.txconfirm(data);
    res.json(result);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 查询tx状态
router.post('/runes_tx', async (req, res) => {
  try {
    const encryptedData = req.body.data;
    const decryptedData = utils.decryptData(encryptedData);
    const { data } = JSON.parse(decryptedData);
    const result = await utils.txconfirm_runes(data);
    res.json(result);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
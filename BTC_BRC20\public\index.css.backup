/* ========================================
   1. 基础样式 (Base Styles)
   ======================================== */

/* 全局样式 */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 15px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #212529; /* 确保默认字体颜色为深色 */
}

/* 响应式基础 */
@media (max-width: 1024px) {
  .col-md-6 {
    width: 100%;
  }
}
/* 基础元素样式 */
#inscriptionSize {
  width: 100%;
}

.switch-container {
  align-items: center;
  margin-bottom: 5px;
}

#addressModeLabel {
  margin-left: 5px;
}

.price-text {
  color: gray;
  font-weight: bold;
}

/* ========================================
   2. 交易相关样式 (Transaction Styles)
   ======================================== */

/* 交易列表 */
.transaction-list {
  max-height: 400px;
  overflow-y: auto;
}

.transaction-container {
  height: 100%;
}

.transaction-list {
  max-height: calc(100% - 60px);
  overflow-y: auto;
}

/* UTXO 组样式 */
.utxo-group {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  background-color: #fff;
}

.utxo-group p {
  margin-bottom: 5px;
}

.utxo-group .close-btn,
.utxo-group .accelerate-btn {
  margin-top: 10px;
}

.utxo-group .close-btn {
  float: right;
}

/* ========================================
   3. 按钮组样式 (Button Group Styles)
   ======================================== */

/* 按钮组容器 */
.button-group,
.button-group-wallet {
  display: flex;
  text-align: left;
  margin-bottom: 10px;
}

.button-group {
  max-width: 200px;
}

.button-group-wallet {
  max-width: 250px;
}

.button-group-text {
  display: flex;
  max-width: 180px;
  text-align: left;
  margin-bottom: 10px;
}

.button-group-text #brc20text {
  flex: 0.5;
}

.button-group-text #ordinalstext {
  flex: 0.5;
}

/* 按钮样式 */
.button-on,
.button-off {
  flex-grow: 1;
  margin-right: 0;
  background-color: #bcbaba;
  color: #ffffff;
  border: 1px solid #bcbaba;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s, color 0.2s;
  height: 30px;
  line-height: 1;
}

.button-on:not(:last-child) {
  border-right: none;
}

.button-on.active,
.button-off.active {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

/* ========================================
   4. 表单和输入框样式 (Form & Input Styles)
   ======================================== */

/* 表单开关 */
.form-switch {
  display: flex;
  align-items: center;
  min-width: 120px;
}

.form-switch .form-check-input {
  width: 2.5em;
  height: 1.2em;
  cursor: pointer;
  position: relative;
  top: -2.5px;
}

.form-switch .form-check-label {
  margin-left: 0.5em;
  line-height: 1.2em;
  cursor: default;
  color: #212529 !important; /* 确保标签文字颜色 */
}

/* 移除全局链接样式，避免影响其他链接 */

/* 全局标签样式 */
label {
  color: #212529 !important;
}

/* 输入框样式 */
#receiveAddress {
  height: 200px;
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  resize: vertical;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group input {
  flex-grow: 1;
  margin-right: 5px;
}

.input-group button {
  font-size: 1rem;
  padding: 5px 12px;
}

#switchesContainer {
  display: flex;
  justify-content: space-between;
}

/* 标签样式 */
.red-bold {
  color: rgb(222, 34, 34);
  font-size: 13px;
  font-weight: bold;
}

.bold-label {
  font-weight: bold;
}

.normal-label {
  font-weight: normal;
  font-size: 13px;
}

#walletInfo p {
  margin-top: 10px;
}

/* ========================================
   5. 费用相关样式 (Fee Styles)
   ======================================== */

/* 费用信息 */
.fees-info {
  padding-left: 10px;
}

.fees-info p {
  margin-bottom: 4.5px;
}

.vr {
  width: 1px;
  background-color: #ccc;
  height: 120px;
  margin: 0 20px;
}

/* 开始挖矿按钮 */
.start-minting-btn {
  width: 100%;
  height: 40px;
  font-size: 18px;
  margin-top: 20px;
}

.rune-info-box {
  border: 1px solid #dee2e6;
  padding: 10px;
  background-color: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: auto;
  min-height: 40px;
  box-sizing: border-box;
  margin-top: 0;
}

.rune-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.rune-quantity {
  text-align: right;
  font-size: 14px;
  color: #333;
}

.mint-status {
  font-weight: bold;
}

.status-minting {
  color: #28a745 !important;
}

.status-completed {
  color: #dc3545 !important;
}

#refreshRune {
  color: #007bff;
  text-decoration: none;
  margin-left: 5px;
  cursor: pointer;
}

#refreshRune:hover {
  text-decoration: underline;
}

/* 暗黑模式下的样式 */
.dark-mode .rune-info-box {
  background-color: #333;
  border-color: #444;
}

.dark-mode .rune-name,
.dark-mode .rune-quantity {
  color: #eee;
}

.tRune {
  color: #007bff;
  text-decoration: none;
}

.Runeinfo {
  color: #007bff;
  text-decoration: none;
}

.Runeinfo:hover {
  text-decoration: underline;
  cursor: pointer;
}

.Runeinfo2 {
  color: #007bff;
  text-decoration: none;
}

.Runeinfo2:hover {
  text-decoration: underline;
  cursor: pointer;
}

#walletInfo p {
  margin-bottom: 5px;
}

.button-group {
  display: flex;
  width: 200px; /* 调整 .button-group 的最大宽度 */
  text-align: left;
  margin-bottom: 5px;
}

.button-group-wallet {
  display: flex;
  max-width: 220px; /* 调整 .button-group-wallet 的最大宽度 */
  text-align: left;
  margin-bottom: 5px;
}

#checkMintable {
  margin-bottom: 10px;
}

#SplitUtxo{
  margin-bottom: 10px;
  float: right;
}


#SplitUtxo a{
  color: #007bff;
  text-decoration: none;
}

.SplitUtxo a:hover {
  text-decoration: underline;
  cursor: pointer;
}

#mintableResult {
  margin-top: 10px;
}

#mintableResult.hidden {
  margin-top: 0;
}

.modal-header .close {
  color: #000;
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  opacity: 0.5;
  padding: 0;
  background-color: transparent;
  border: 0;
  appearance: none;
}

.fee-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 10px;
}

.fee-option, .fee-option-utxo {
  flex: 1;
  padding: 6px 8px; /* 修改这一行以调整按钮的高度 */
  background-color: #f8f9fa;
  border: 2px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  line-height: 1.2;
  font-size: 16px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.fee-option:hover, .fee-option-utxo:hover{
  background-color: #e9ecef;
}

.fee-option.active, .fee-option-utxo.active{
  background-color: #28a745;
  border-color: #28a745;
  color: white;
}

.bold-label {
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

.container {
  display: flex;
}

.left-area { 
  flex: 6;
  max-width: 700px;
}

.left-panel {
  flex: 0 0 25%;  /* 设置活基础宽度 */
  max-width: 350px; /* 设置最大宽度为350px */
}

.middle-panel {
  flex: 0 0 50%;
  max-width: 50%;
}

.right-area {
  flex: 0 0 25%;  /* 设置灵活基度 */
  max-width: 400px; /* 设置最大宽度为350px */
}

#darkModeSwitch{
  display: inline-flex; 
  font-size: 14px; 
  margin-left: -2rem;
}

@media (max-width: 1150px) {
  .left-panel, .right-area {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .middle-panel {
    flex: 0 0 100%;
    max-width: 100%;
  }
}


.row {
  display: flex;
  justify-content: center;
}

.tx-hash {
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 溢出隐藏 */
  text-overflow: ellipsis; /* 文本溢出时显示省略号 */
}

.transaction-actions {
  display: flex;
  align-items: center;
}

.transaction-actions .accelerate-btn {
  margin-right: 10px;
}

.transaction-actions .order-rate {
  margin-right: auto;
  position: relative;
  top: 5px;
}

.transaction-actions .close-btn {
  margin-left: 10px;
}

.gray-text {
  color: #6c757d;
}

/* 调整按钮样式 */
#accelerateButton.custom-size {
  padding: 0.075rem 0.5rem;  /* 根据你的样式定义调整尺寸 */
}

.form-check {
  position: relative;
  top: 3px;
}

.utxo-group {
  position: relative; /* 设置相对位，为复选框的绝对定位提供参考 */
}

/* ========================================
   6. 导航栏样式 (Navigation Styles)
   ======================================== */

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  width: 100%;
  display: flex;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.navbar-logo {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  margin-right: 20px;
}

.navbar-menu {
  display: flex;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.navbar-item {
  margin-right: 20px;
}

.navbar-icon {
  margin-right: 15px;
}

.navbar-link {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.navbar-link:hover {
  color: #007bff;
}

.material-icons {
  font-size: 18px;
  margin-left: 2px;
}

.dropdown-menu {
  display: none;
  position: absolute;
  background-color: #fff;
  min-width: 150px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 1;
  border-radius: 4px;
  padding: 5px 0;
  background-color: #f9f9f9;
  font-size: 14px;
}

.navbar-item:hover .dropdown-menu {
  display: block;
}

.dropdown-menu li {
  padding: 8px 15px;
}

.dropdown-menu li a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
}

.dropdown-menu li a:hover {
  color: #007bff;
}

.wallet-icon {
  color: #333;
  margin-right: 15px;
}

/* 汉堡菜单按钮 */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 15px;
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: #333;
  transition: all 0.3s ease;
  transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* 移动端菜单 */
.mobile-menu {
  display: none;
  position: fixed;
  top: 45px;
  left: 0;
  right: 0;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
  max-height: calc(100vh - 45px);
  overflow-y: auto;
}

.mobile-menu-content {
  padding: 25px;
}

.mobile-menu-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.mobile-menu-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.mobile-menu-section h6 {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.mobile-menu-link {
  display: block;
  padding: 18px 0;
  color: #555;
  text-decoration: none;
  font-size: 20px;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.mobile-menu-link:hover {
  color: #007bff;
  background-color: #f8f9fa;
  padding-left: 10px;
}

.mobile-menu-link:last-child {
  border-bottom: none;
}

/* 暗黑模式切换开关 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

#darkModeSwitch {
  display: inline-flex;
  font-size: 14px;
  font-family: Arial, sans-serif;
  font-weight: normal;
  margin-left: -2rem;
}

/* ========================================
   7. 导航栏暗黑模式样式 (Dark Mode Navigation)
   ======================================== */

/* 导航栏暗黑模式 */
.navbar.navbar-dark {
  background-color: #333;
  border-bottom-color: #444;
}

.navbar-dark .navbar-logo {
  color: #fff;
}

.navbar-dark .navbar-link {
  color: #ccc;
}

.navbar-dark .navbar-link:hover {
  color: #fff;
}

.navbar-dark .dropdown-menu {
  background-color: #333;
}

.navbar-dark .dropdown-menu li a {
  color: #ccc;
}

.navbar-dark .dropdown-menu li a:hover {
  color: #fff;
  background-color: #444;
}

.navbar-dark .wallet-icon {
  color: #ccc;
}

/* 汉堡菜单暗黑模式 */
.navbar-dark .hamburger-line {
  background-color: #ccc;
}

.navbar-dark .mobile-menu {
  background-color: #333;
  border-bottom-color: #444;
}

.navbar-dark .mobile-menu-section h6 {
  color: #fff;
  border-bottom-color: #007bff;
}

.navbar-dark .mobile-menu-link {
  color: #ccc;
  border-bottom-color: #444;
}

.navbar-dark .mobile-menu-link:hover {
  color: #fff;
  background-color: #444;
}

/* ========================================
   8. 移动端导航条响应式设计 (Mobile Navigation Responsive)
   ======================================== */

/* 平板和中等屏幕 */
@media (max-width: 1200px) {
  .navbar-logo {
    font-size: 1.1rem;
    margin-right: 12px;
  }

  .mobile-menu-toggle {
    width: 38px;
    height: 38px;
    margin-left: 12px;
  }

  .hamburger-line {
    width: 28px;
    height: 3.5px;
  }

  .mobile-menu {
    top: 65px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 26px;
  }

  .mobile-menu-link {
    font-size: 24px;
    padding: 20px 0;
  }

  #darkModeSwitch {
    font-size: 13px;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  /* 导航栏保持舒适大小 */
  .navbar {
    padding: 12px 12px;
    height: 65px;
    min-height: 65px;
  }

  .navbar-logo {
    font-size: 1.1rem;
    margin-right: 12px;
  }

  .mobile-menu-toggle {
    width: 38px;
    height: 38px;
    margin-left: 12px;
  }

  .hamburger-line {
    width: 28px;
    height: 3.5px;
  }

  .mobile-menu {
    top: 65px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 25px;
  }

  .mobile-menu-link {
    font-size: 23px;
    padding: 20px 0;
  }

  #darkModeSwitch {
    font-size: 0.85rem;
    margin-left: 8px;
  }

  .switch {
    width: 42px;
    height: 22px;
    margin-right: 6px;
  }

  .slider:before {
    height: 16px;
    width: 16px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(20px);
  }

  body {
    padding-top: 70px;
  }
}

@media (max-width: 576px) {
  /* 超小屏幕导航栏优化 */
  .navbar {
    padding: 10px 10px;
    height: 60px;
    min-height: 60px;
  }

  .navbar-logo {
    font-size: 1rem;
    margin-right: 10px;
  }

  .mobile-menu-toggle {
    width: 35px;
    height: 35px;
    margin-left: 10px;
  }

  .hamburger-line {
    width: 25px;
    height: 3px;
  }

  .mobile-menu {
    top: 60px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 24px;
  }

  .mobile-menu-link {
    font-size: 22px;
    padding: 20px 0;
  }

  #darkModeSwitch {
    font-size: 0.8rem;
    margin-left: 6px;
  }

  .switch {
    width: 38px;
    height: 20px;
    margin-right: 5px;
  }

  .slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(18px);
  }

  body {
    padding-top: 65px;
  }
}

/* 移动端全面适配 */
@media (max-width: 991px) {
  /* 导航栏移动端优化 - 汉堡菜单 */
  .navbar {
    padding: 15px 15px;
    height: 70px;
    min-height: 70px;
    flex-wrap: nowrap;
  }

  .navbar-container {
    flex-direction: row;
    align-items: center;
    width: 100%;
    justify-content: space-between;
  }

  .navbar-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .navbar-right {
    display: flex;
    align-items: center;
    margin-left: auto;
    flex-shrink: 0;
  }

  .navbar-logo {
    font-size: 1.2rem;
    margin-right: 15px;
    flex-shrink: 0;
    font-weight: bold;
  }

  /* 隐藏桌面端菜单，显示汉堡菜单 */
  .desktop-menu {
    display: none !important;
  }

  .mobile-menu-toggle {
    display: flex !important;
    width: 40px;
    height: 40px;
    margin-left: 15px;
  }

  .hamburger-line {
    width: 30px;
    height: 4px;
  }

  .mobile-menu {
    top: 70px;
  }

  /* 移动端菜单字体优化 */
  .mobile-menu-section h6 {
    font-size: 28px;
  }

  .mobile-menu-link {
    font-size: 26px;
    padding: 22px 0;
  }

  /* 暗黑模式切换开关移动端优化 */
  #darkModeSwitch {
    display: inline-flex;
    font-size: 0.9rem;
    margin-left: 10px;
    align-items: center;
  }

  .switch {
    width: 45px;
    height: 24px;
    margin-right: 8px;
  }

  .slider:before {
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .slider:before {
    transform: translateX(21px);
  }

  body {
    padding-top: 75px;
  }
}

.checkbox-container {
  position: absolute; /* 绝对定位 */
  top: 5px; /* 顶部边距 */
  right: 5px; /* 侧边距 */
  z-index: 2; /* 确保复选框显示其他内容之上 */
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-item span {
  flex-shrink: 0; /* 防止缩放 */
}

#mintingList {
  max-height: 1140px;  /* 可以根据实际情况调整这个高度 */
  overflow-y: auto;  /* 开启垂直方向上的滚动条 */
  overflow-x: hidden;  /* 防止水平方向上的滚动条出现 */
}

/* 定制滚动条的整体样式 */
#mintingList::-webkit-scrollbar {
  width: 6px;  /* 设置滚动条宽度适中 */
}

/* 定制滚动条滑块的样式 */
#mintingList::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;  /* 滑块颜色，使用浅灰色 */
  border-radius: 10px;  /* 滑块圆角，增加美性 */
}

/* 定制滚动条滑道的样式 */
#mintingList::-webkit-scrollbar-track {
  border-radius: 10px;  /* 滑道圆角，与滑块一致 */
  background-color: #f0f0f0;  /* 滑道背景色，使用非常浅的灰色 */
}


.close-btn {
  display: none; /* 默认隐藏删除按钮 */
}

.options-btn {
  font-size: 24px; /* 增大字体大小使点更明显 */
  line-height: 1; /* 确保按钮内容垂直居中 */
  padding: 0px 5px; /* 增加水平填充 */
  position: relative;
  top: 5px;
}

.dots {
  display: inline-block; /*  span 元素变为行块元素 */
  text-align: center; /* 水平中 */
  width: 100%; /* 设置宽度为按钮的100%，这样文本才能居中 */
  position: relative;
  top: -5px;
}

#confirmedList {
  max-height: 850px;
  overflow-y: auto;
}

#confirmedList .transaction-item {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  background-color: #fff;
  position: relative;
}

#confirmedList .transaction-item p {
  margin-bottom: 5px;
}

#walletAddressLink,
.wallet-address-link,
.tx-hash-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

#walletAddressLink:hover,
.wallet-address-link:hover,
.tx-hash-link:hover {
  text-decoration: underline;
}

#walletAddressLink,
.wallet-address-link,
.tx-hash-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

#walletAddressLink:hover,
.wallet-address-link:hover,
.tx-hash-link:hover {
  text-decoration: underline;
}

.tooltip-inner {
  background-color: #007bff;
  color: #fff;
  border-radius: 6px;
  padding: 10px;
  font-size: 14px;
  max-width: 450px;  /* 将最大宽度调整为350px或其他合适的值 */
  width: 450px;  /* 将宽度设置为与最大宽度相同的值 */
  text-align: left;  /* 添加此行以使文本居左对齐 */
}

.tooltip .tooltip-arrow {
  display: none;
}

.tooltip::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #007bff transparent transparent transparent;
}

.tooltip.bs-tooltip-top::after {
  top: auto;
  bottom: -10px;
  border-color: #007bff transparent transparent transparent;
}

.tooltip-icon {
  position: relative;
  cursor: pointer;
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  background-color: #3498db; /* 蓝色背景 */
  color: #fff; /* 白色问号 */
  border-radius: 50%;
  font-size: 12px; /* 问号大小 */
  font-weight: bold; /* 粗体 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 阴影效果 */
}

.tooltip-icon:hover {
  background-color: #2980b9; /* 悬停时深蓝色 */
}

#noTransactionsMessage, #noconfirmedMessage {
  display: none;
  padding: 330px 8px;
  color: #6c757d;
  font-size: 16px;
  font-weight: bold;
}

/* 统一的空状态样式 */
.empty-state-message {
  color: #6c757d;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-top: 1rem;
}

/* 铭文看板空状态容器 */
.hotlist-empty-container {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 错误状态样式 */
.error-state-message {
  color: #dc3545;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  margin-top: 1rem;
}

#walletInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#walletAddressLink {
  display: inline-block;
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

#walletBalanceInfo {
  margin-left: auto;
}

@media (max-width: 576px) {
  #walletInfo {
    flex-direction: column;
    align-items: flex-start;
  }
  
  #walletBalanceInfo {
    margin-left: 0;
    margin-top: 5px;
  }
}

#mintDetailsContainer {
  max-height: 170px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: -25px;
}

#mintDetailsContainer table {
  width: 100%;
}

#mintDetailsContainer th,
#mintDetailsContainer td {
  padding: 5px;
  text-align: left;
  border-bottom: 1px solid #ccc;
}

#mintDetailsContainer th {
  background-color: #f2f2f2;
}

#mintDetailsContainer tr:last-child td {
  border-bottom: none;
}

.bold-label {
  font-weight: bold;
}


#utxoDetailsContainer {
  max-height: 170px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 5px;
  margin-top: -25px;
}

#utxoDetailsContainer table {
  width: 100%;
}

#utxoDetailsContainer th,
#utxoDetailsContainer td {
  padding: 5px;
  text-align: left;
  border-bottom: 1px solid #ccc;
}

#utxoDetailsContainer th {
  background-color: #f2f2f2;
}

#utxoDetailsContainer tr:last-child td {
  border-bottom: none;
}

.custom-size {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
  margin-top: 25px;
}

.w-auto {
  width: auto !important;
}

.dropdown-menu {
  background-color: #f9f9f9;
  font-size: 14px;
}


.rune-name-text {
  color: rgb(222, 34, 34);
}

.rune-red-text {
  color: rgb(222, 34, 34);
  font-weight: bold;
}

.rune-mint-text {
  color: rgb(6, 145, 57);
}

.rune-text {
  color: rgb(6, 145, 57);
  font-weight: bold;
}

#runeFilterDropdown {
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


.rune-progress-container:not(.rune-mint-info) {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.rune-progress-container:not(.rune-mint-info) label.form-label {
  margin-bottom: 5px;
}

.rune-progress-container:not(.rune-mint-info) p {
  margin-bottom: 5px;
}

#myChart {
  height: 130px; /* 减少内存池透视图高度 */
  min-height: 130px;
  width: 100%;
}

/* 确保图表字体不被其他样式覆盖 */
#myChart canvas {
  font-family: inherit !important;
}


.pay-attention {
  color: #dc3545;
  font-size: 12px;
}

.link {
  color: #007BFF;
  text-decoration: none;
  font-size: 18px;
  font-weight: bold;
  vertical-align: middle;
  line-height: 1.2;
  display: inline-block;
  margin-top: -2px;
}

.link:hover {
  text-decoration: underline;
}

.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
  border: 0.15em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border .75s linear infinite;
}

.spinner-border-sm {
  width: 0.8rem;
  height: 0.8rem;
  border-width: 0.1em;
}

@keyframes spinner-border {
  to { transform: rotate(360deg); }
}

.hot-springs {
  color: #ff0000;
  font-size: 18px;
  font-weight: normal;
  margin-top: -3px;
  margin-right: 4px;
}

.mintuser {
  font-size: 14px;
  font-weight: normal;
  margin-top: 0px;
  margin-right: -1px;
}


#SplitUtxoModal .modal-dialog {
  max-width: 600px;
  width: 98%;
}

#confirmMintModal .modal-dialog {
  max-width: 600px;
  width: 98%;
}

#splitCount {
  max-width: 150px;
  text-align: center;
}

#custom_utxo_value {
  height: 25px;
  width: 100px;
  margin-right: 5px;
  text-align: center;
}

/* 自定义颜色背景 */
.bg-black {
  background-color: #555151 !important; /* 红色 */
}

.bg-green {
  background-color: #28a745 !important; /* 绿色 */
}

.bg-red {
  background-color: #ff0000 !important; /* 黄色 */
}

/* 符文项样式 */
.left-panel .rune-item {
  border: 1px solid #dee2e6;
  padding: 10px;
  margin-bottom: 0; /* 去掉底部间距以合并相邻项的边框 */
  border-radius: 0; /* 去掉圆角以合并邻项的边框 */
  background-color: #fff;
}

.left-panel .rune-item + .rune-item {
  border-top: none; /* 去掉相邻项之间顶部边框 */
}

/* 符文项头部样式 */
.left-panel .rune-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.left-panel .rune-header .hot-springs {
  color: #ff0000;
  font-size: 15px;
  margin-right: 4px;
}

.left-panel .rune-header .rune-name-text {
  font-weight: bold;
  font-size: 14px;
  color: #494e4e; /* 默认黑色 */
}

/* 进度条容器 */
.left-panel .progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  width: 100%; /* 确保容器宽度为100% */
}

/* 进度条样式 */
.left-panel .progress {
  height: 10px; /* 调整进度条高度 */
  width: 60%; /* 固定宽度为60% */
  margin-right: 10px; /* 确保与百分比有间距 */
}

.left-panel .progress-bar {
  text-align: center;
  white-space: nowrap;
  color: #fff;
  padding: 0;  /* 移除内边距，使其不显示文字 */
}

.multi-progress {
  display: flex;
}

.left-panel .rune-details {
  font-size: 13px;
  color: #2d2b2b;
}

.left-panel .green-percentage {
  color: #6c757d;
  font-weight: bold;
  margin-left: 10px;
}

.rune-name-text {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#hotlist {
  height: 915px;
  max-height: 915px;
  overflow-y: auto;
  overflow-x: hidden;
}

#hotlist::-webkit-scrollbar {
  width: 6px;
}

#hotlist::-webkit-scrollbar-thumb {
  background-color: #d1d1d1;
  border-radius: 10px;
}

#hotlist::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f0f0f0;
}

.left-panel .rune-item:hover {
  background-color: #f8f9fa;
  cursor: pointer;
}
.rune-card {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.rune-body p {
  margin: 5px 0;
  color: #666;
}

.rune-item {
  position: relative;
  padding: 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.rune-card.hidden {
  display: none;
  position: absolute;
  left: -5px;
  top: 70px;
  min-width: 200px;
  max-width: 500px;
  padding: 10px;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 2px 2px 10px rgba(0,0,0,0.2);
  z-index: 100;
}


.rune-link img {
  width: 16px;
  height: 16px;
  margin-top: -3px;
}

.rune-links {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rune-link {
  margin-right: 20px;
  text-decoration: none;
}

.mint-link {
  text-decoration: none;
  margin-left: auto;
}

.copy-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #4caf50;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: opacity 0.5s ease-in-out;
}
#hideBoard {
  height: 16px;
  width: 16px;
  margin-bottom: 2px;
  cursor: pointer
}

#showBoard {
  cursor: pointer;
  position: absolute;
  left: -20px;
  height: 25px;
  width: 25px;
  z-index: 1;
  display: none;
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  padding: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#pendingButton, #confirmedButton {
  width: auto;
}

#confirmedButton {
  margin-left: -5px;
}
.short-text {
  display: none;
}

.long-text {
  display: inline;
}

@media (max-width: 1600px) {
  #pendingButton, #confirmedButton {
    width: 60px;
  }

  .short-text {
    display: inline;
  }

  .long-text {
    display: none;
  }
}

#og {
  display: none;
  width: 60px;
  margin-left: 7px;
}


.btc-amount {
  display: inline;
}

.split-input-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.slider-container, .split-count-input {
  flex: 1 1 calc(50% - 5px);
  min-width: 150px;
}
.slider-border {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  height: 38px;
  display: flex;
  align-items: center;
}
.form-range {
  width: 100%;
  margin: 0;
}
@media (max-width: 991px) {
  .slider-container, .split-count-input {
      flex-basis: 100%;
  }
}

/* 媒体询当容器宽小于特定值时隐藏 BTC 金额 */
@media (max-width: 991px) {  /* 根据您的布局调整这个值 */
  .btc-amount {
    display: none;
  }
}


.line-through {
  text-decoration: line-through;
}

@keyframes shake {
  0% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  75% { transform: translateX(-5px); }
  100% { transform: translateX(0); }
}

.input-error {
  border-color: red !important;
  animation: shake 0.5s;
}

.error-message {
  color: rgb(222, 34, 34);
  font-size: 14px;
  margin-top: 5px;
  font-weight: bold;
}



.address-input-flex-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  gap: 10px;
}

.button-group {
  display: flex;
  flex-shrink: 0;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  flex-grow: 1;
  justify-content: flex-end;
}

.quantity-input-group label {
  white-space: nowrap;
  font-size: 0.875rem; /* 稍微缩小标签字体大小 */
}


#addressQuantity {
  width: 80px;
  flex-shrink: 1;
  min-width: 60px;
  height: 30px;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  appearance: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  cursor: pointer;
}


.button-on, .button-off {
  flex-grow: 1;
  margin-right: 0;
  background-color: #bcbaba;
  color: #ffffff;
  border: 1px solid #bcbaba;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  outline: none;
  transition: background-color 0.2s, color 0.2s;
  height: 30px;
  line-height: 1;
}

@media (max-width: 768px) {
  .address-input-flex-container {
    flex-direction: column;
    align-items: stretch;
  }

  .quantity-input-group {
    justify-content: flex-start;
    margin-top: 10px;
  }
}

@media (max-width: 576px) {
  .quantity-input-group {
    flex-wrap: wrap;
  }
}

.address-quantity-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.total-quantity {
  flex-shrink: 0;
}

.quantity-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
}

.quantity-input-group label {
  white-space: nowrap;
  font-size: 0.875rem;
}

#addressQuantity {
  appearance: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  cursor: pointer;
}

#alertModalBody {
  max-height: 300px;  /* 设置最大高度 */
  overflow-y: auto;   /* 如果内容超出，显示滚动条 */
  line-height: 1.5;   /* 增加行间距 */
  padding: 10px;      /* 增加内边距 */
}

.loading-message {
  text-align: center;
  padding: 20px;
  font-size: 16px;
}

.text-success {
  color: green;
}

.text-danger {
  color: red;
}

/*
@media (max-width: 1199px) {
  .left-panel {
    display: none;
  }
  
  .left-area {
    flex: 0 0 65%;
    max-width: 65%;
  }
  
  .right-area {
    flex: 0 0 35%;
    max-width: 35%;
  }
}
*/

#mem-left {
  width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#mem-right {
  width: 40%;
  display: flex;
  align-items: center;
}

@media (max-width: 991px) {   /* 移动端适配 */
  
  /* 基础字体大小 */
  html {
    font-size: 32px;  /* 或 20px，视情而定 */
  }

  body {
    font-size: 1rem;  /* 使用相对单位 */
    line-height: 1.5;
  }

  /* 确保所有文本元素使用相对字体大小 */
  p, span, div, label, input, select, button, a,
  .red-bold, .normal-label, #myChart,
  #noTransactionsMessage, #noconfirmedMessage,
  .empty-state-message, .error-state-message,
  .ybot_head, .index-block, .rune-name-text,
  .left-panel .rune-header .rune-name-text, .hot-runeid {
    font-size: 1rem;
  }
  
  /* 单独设置 rune-name 和 rune-quantity 的字体大小 */
  .rune-name, .rune-quantity {
    font-size: 1.2rem !important;
  }
  
  /* 设置 runeInfo2 相关元素的移动端字体大小 */
  .rune-name2, .rune-quantity2 {
    font-size: 1rem !important;
  }
  
  .rune-name2 span, .rune-quantity2 span,
  .rune-name2 a, .rune-quantity2 a {
    font-size: 1rem !important;
  }

  #feesInfoContainer {
    margin-top: 2rem;
  }

  #hideBoard {
    height: 28px;
    width: 28px;
    margin-bottom: 5px;
    cursor: pointer;
  }

  #showSave {
    display: none;
  }

  /* 特别调整某些元素的大小 */
  .fee-option, .fee-option-utxo, .fee-item, #feeRate, #maxFee {
    font-size: 0.9rem;
  }

  .rune-link img {
    width: 1rem;
    height: 1rem;
    margin-top: -3px;
  }

  /* 调整标题大小 */
  h1 { font-size: 1.8rem; }
  h2 { font-size: 1.6rem; }
  h3 { font-size: 1.4rem; }
  h4 { font-size: 1.3rem; }

  /* 增大按钮和输入框的大小 */
  .btn, input[type="text"], input[type="number"], select {
    font-size: 1rem;
    padding: 10px;
    height: auto;
  }

  /* 调整图表区域 - 移动端使用更大的高度 */
  #myChart {
    height: 160px;
    min-height: 160px;
    width: 100%;
    max-width: 100%;
  }

  /* 确保小图标也适当放大 */
  .icon, .fa {
    font-size: 1.2rem;
  }

  /* 调整特定区域的布局 */
  .rune-progress-container, .fee-options {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .fee-option, .fee-option-utxo {
    margin-bottom: 5px;
  }


  /* 调整布局 */
  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }

  .row {
    margin-left: -15px;
    margin-right: -15px;
  }

  .col-md-3, .col-md-6, .left-panel, .middle-panel, .right-panel {
    flex: 0 0 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }

  /* 将左侧面板显示为块级元素，而不是隐藏 */
  .left-panel {
    display: block;
    margin-bottom: 20px;
    width: 100%;
    order: -1; /* 确保它显示在顶部 */
  }

  /* 调整热门列表的高度，使其在移动端更紧凑 */
  #hotlist {
    height: auto;
    max-height: 600px; /* 可以根据需要调整高度 */
  }


  /* 确保中间面板和右侧面板垂直堆叠 */
  .middle-panel, .right-panel {
    margin-bottom: 20px;
  }

  /* 调整右侧任务栏的样式 */
  .right-panel {
    margin-top: 20px;
    border-top: 1px solid #dee2e6;
    padding-top: 20px;
  }

  #splitCount {
    max-width: none;
    text-align: center;
  }

  #mintingList, #confirmedList {
    max-height: none; /* 移除最大高度限制 */
  }

  .rune-progress-container {
    overflow-x: auto;
  }

  .btn, input, select, textarea {
    width: 100%;
    margin-bottom: 10px;
  }


  
  .button-on, .button-off {
    flex: 1 1 auto;
    padding: 5px;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 60px;
    min-width: 240px;
  }

  .rune-info-box {
    display: flex;
    flex-direction: column;
    height: auto;
    min-height: 80px;
  }
  
  .rune-name, .rune-quantity {
    width: 100%;
    word-break: break-word;
  }

  #runeFilterDropdown{
    max-width: 300px;
  }

  #batchAccelerateButton, #batchDeleteButton{
    max-width: 300px;
    margin-top: 46px;
  }
  .tooltip-icon, #safetip{
    display: none;
  }

  .right-area{
    margin-top: 20px;
  }

  .rune-info-box {
    flex-direction: column;
    align-items: flex-start;
  }

  .rune-name {
    margin-bottom: 5px;
  }

  .rune-quantity {
    display: block;
    margin-bottom: 5px;
  }

  #refreshRune {
    display: inline-block;
    margin-top: 5px;
  }

  #mem-left, #mem-right{
    width: 100%;
  }

  .address-quantity-container {
    flex-direction: column;
    align-items: stretch;
  }

  .total-quantity {
    margin-bottom: 10px;  /* 添加一些底部边距 */
  }

  .quantity-input-group {
    width: 100%;  /* 让它占满整行 */
    justify-content: flex-start;  /* 左对齐内容 */
  }

  #addressQuantity{
    width: 180px;
    height: 50px;
    margin-top: 15px;
  }


  #walletInfo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start; /* 移除space-between */
  }

  #walletInfo > span:first-child {
    margin-bottom: 5px;
    width: 100%; /* 钱包地址使用全宽 */
  }

  #walletBalanceInfo {
    margin-left: 0;
    display: flex;
    align-items: center;
  }

  #walletBalance {
    margin-left: 5px;
    margin-right: 5px;
  }

  #og{
    width: 120px;
    margin-top: 5px;
  }

  .hot-springs {
    color: #ff0000;
    font-size: 1.2rem;
    font-weight: normal;
    margin-top: -3px;
    margin-right: 10px;
  }
}

/* ========================================
   9. 暗黑模式样式 (Dark Mode Styles)
   ======================================== */

/* 页面暗黑模式 */
body.dark-mode {
  background-color: #222;
  color: #eee;
}

.dark-mode .transaction-container,
.dark-mode .utxo-group,
.dark-mode #confirmedList .transaction-item {
  background-color: #333;
  border-color: #444;
}

.dark-mode .gray-text {
  color: #aaa;
}

.dark-mode .fee-option,
.dark-mode .fee-option-utxo {
  background-color: #444;
  border-color: #555;
  color: #eee;
}

.dark-mode .fee-option:hover,
.dark-mode .fee-option-utxo:hover {
  background-color: #555;
}

.dark-mode .fee-option.active,
.dark-mode .fee-option-utxo.active {
  background-color: #28a745;
  border-color: #28a745;
  color: #fff;
}

/* 导航new和hot样式 */

.badge {
  display: inline-block;
  padding: 2px 4px;
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 3px;
  margin-left: 5px;
}

.badge-new {
  background-color: #28a745;
  color: white;
}

.badge-hot {
  background-color: #dc3545;
  color: white;
}

/* 暗黑模式样式 */
.dark-mode .badge-new {
  background-color: #34d058;
}

.dark-mode .badge-hot {
  background-color: #f85149;
}

.mint-status {
    font-weight: bold;
}

.status-minting {
    color: #28a745 !important;
}

.status-completed {
    color: #dc3545 !important;
}

#refreshRune {
    color: #007bff;
    text-decoration: none;
    margin-left: 5px;
    cursor: pointer;
}

#refreshRune:hover {
    text-decoration: underline;
}

.rune-info-box {
    border: 1px solid #dee2e6;
    padding: 10px;
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: auto;
    min-height: 40px;
    box-sizing: border-box;
    margin-top: 0px;
}

.rune-name {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
}

.rune-quantity {
    text-align: right;
    font-size: 14px;
    color: #333;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .rune-info-box {
        flex-direction: column;  /* 改为纵向排列 */
        align-items: flex-start;  /* 左对齐 */
        padding: 12px;  /* 增加内边距 */
        height: auto;  /* 自适应高度 */
        min-height: 80px;  /* 设置最小高度 */
    }

    .rune-name {
        margin-bottom: 8px;  /* 添加底部间距 */
        font-size: 16px;  /* 增大移动端字体大小 */
        width: 100%;  /* 占满宽度 */
        word-break: break-word;  /* 允许单词换行 */
    }

    .rune-quantity {
        font-size: 16px;  /* 增大移动端字体大小 */
        width: 100%;  /* 占满宽度 */
        text-align: left;  /* 左对齐 */
        word-break: break-word;  /* 允许单词换行 */
    }

    #refreshRune {
        display: inline-block;
        margin-top: 5px;  /* 增加顶部间距 */
    }

    /* 暗黑模式适配 */
    .dark-mode .rune-info-box {
        background-color: #333;
        border-color: #444;
    }

    .dark-mode .rune-name,
    .dark-mode .rune-quantity {
        color: #eee;
    }
}

.tick-details {
    color: #007bff !important; /* 强制应用蓝色链接颜色 */
    text-decoration: none;
    margin-left: 5px;
    cursor: pointer;
}

.tick-details:hover {
    text-decoration: underline;
}

/* 调整符文信息显示的字体大小，使其更协调 */
.rune-name2 {
    font-size: 12px;
}

.rune-quantity2 {
    font-size: 12px;
}

.rune-name2 span,
.rune-quantity2 span,
.rune-name2 a,
.rune-quantity2 a {
    font-size: 13px;
}

#runesMintContainer {
  transition: all 0.3s ease;
}

.hot-springs {
  color: #ff0000;
  font-size: 18px;
  margin-right: 5px;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

#refreshRune2 {
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
}

#refreshRune2:hover {
  text-decoration: underline;
}

/* 符文铸造的特殊样式 */
.rune-mint-info {
  padding: 10px;
  margin-top: 10px;
}

.rune-mint-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
  width: 100%;
}

.rune-mint-pool {
  flex: 1;
  min-width: 0; /* 防止flex子项溢出 */
}

.rune-mint-pool label {
  margin: 0;
  white-space: nowrap;
}

.rune-mint-links {
  flex: 1;
  text-align: right;
  white-space: nowrap;
  padding-left: 15px;
}

.rune-mint-links img {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .rune-mint-stats {
    flex-direction: column;
    align-items: flex-start;
  }

  .rune-mint-pool {
    margin-bottom: 8px;
    width: 100%;
  }

  .rune-mint-links {
    width: 100%;
    text-align: left;
    padding-left: 0;
  }
}

/* 移除重复的内存池透视样式定义 */

/* 铭刻内容自动换行样式 */
#confirmOrditext {
  word-break: break-all;
  word-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  display: inline-block;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}
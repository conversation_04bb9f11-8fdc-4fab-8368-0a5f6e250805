const request = require("request");
const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");
dotenv.config();

let NODES = [];
let NODES_Lowfee = [];
let lastModified = 0;

function loadRPCConfig() {
  const rpcJsonPath = path.join(__dirname, 'rpc.json');
  try {
    const stats = fs.statSync(rpcJsonPath);
    if (stats.mtimeMs > lastModified) {
      const rpcConfig = JSON.parse(fs.readFileSync(rpcJsonPath, 'utf8'));
      NODES = process.env.TESTNET === "true" ? [rpcConfig.testnet] : rpcConfig.nodes;
      NODES_Lowfee = process.env.TESTNET === "true" ? [rpcConfig.testnet] : rpcConfig.nodes_Lowfee;
      lastModified = stats.mtimeMs;
      //console.log("RPC configuration reloaded at", new Date().toISOString());
    }
  } catch (error) {
    console.error("Error loading RPC configuration:", error);
  }
}

// 初始加载
loadRPCConfig();
// 每30秒检查一次文件修改
setInterval(loadRPCConfig, 30000);

async function rpc_batch(requests_list, Lowfee = false) {
  const payload = JSON.stringify(requests_list);

  const promises = Lowfee ? NODES_Lowfee.map((node, nodeIndex) => {
    return new Promise((resolve) => {
      //console.log(`Sending request to node ${nodeIndex}: ${node.url}`);
      const options = {
        url: node.url,
        method: "POST",
        auth: node.auth,
        body: payload,
        timeout: 10000,
      };
      request(options, (error, response, body) => {
        if (error) {
          //console.error(`Error with node ${nodeIndex} (${node.url}):`, error.message);
          // 返回错误信息
          resolve({ node, error: error.message });
        } else {
          //console.log(`Received response from node ${nodeIndex} (${node.url}):`, body);
          try {
            const response_data = JSON.parse(body);
            // 无论响应中是否包含错误，都返回响应数据
            resolve({ node, response_data });
          } catch (parseError) {
            //console.error(`Error parsing response from node ${nodeIndex} (${node.url}):`, parseError.message);
            // 解析错误时返回错误信息
            resolve({ node, error: parseError.message });
          }
        }
      });
    });
  }) : NODES.map((node, nodeIndex) => {
    return new Promise((resolve) => {
      //console.log(`Sending request to node ${nodeIndex}: ${node.url}`);
      const options = {
        url: node.url,
        method: "POST",
        auth: node.auth,
        body: payload,
        timeout: 10000,
      };
      request(options, (error, response, body) => {
        if (error) {
          //console.error(`Error with node ${nodeIndex} (${node.url}):`, error.message);
          // 返回错误信息
          resolve({ node, error: error.message });
        } else {
          //console.log(`Received response from node ${nodeIndex} (${node.url}):`, body);
          try {
            const response_data = JSON.parse(body);
            // 无论响应中是否包含错误，都返回响应数据
            resolve({ node, response_data });
          } catch (parseError) {
            //console.error(`Error parsing response from node ${nodeIndex} (${node.url}):`, parseError.message);
            // 解析错误时返回错误信息
            resolve({ node, error: parseError.message });
          }
        }
      });
    });
  });

  return new Promise((resolve, reject) => {
    let settled = false;
    let firstError = null;

    promises.forEach((promise, index) => {
      promise
        .then((result) => {
          if (!settled) {
            settled = true;
            if (result.response_data) {
              //console.log(`使用节点 ${index} (${result.node.url}) 的响应`);
              resolve(result.response_data);
            } else if (result.error) {
              //console.error(`节点 ${index} (${result.node.url}) 返回错误:`, result.error);
              resolve([{ error: result.error }]);
            }
          }
        })
        .catch((error) => {
          //console.error(`Promise for node ${index} rejected with error:`, error.message);
          // 忽略错误，继续等待其他 promises
        });
    });

    // 设置超时时间，以防所有请求都未返回正确结果
    setTimeout(() => {
      if (!settled) {
        //console.error("Timeout reached without receiving a valid response from any node.");
        reject(
          new Error(
            firstError || "所有节点都返回了错误的响应。"
          )
        );
      }
    }, 15000); // 超时时间 15 秒，可以根据需要调整
  });
}

module.exports = {
  rpc_batch,
};

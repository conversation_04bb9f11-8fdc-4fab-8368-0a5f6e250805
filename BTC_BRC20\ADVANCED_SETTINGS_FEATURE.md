# 高级设置开关功能实现总结

## 功能概述
成功为当前项目添加了"高级设置"开关功能，参考了同目录下的参考文件实现方式，与现有UI风格和代码结构保持一致。

## 实现的功能

### 1. HTML结构 (`public/index.html`)
- 添加了高级设置开关UI元素
- 位置：在"查询矿工钱包可Mint数量"按钮下方
- 结构：
```html
<!-- 高级设置开关 -->
<div id="advancedSettingsContainer" class="form-switch mb-3">
  <input class="form-check-input" type="checkbox" id="advancedSettings">
  <label class="form-check-label" for="advancedSettings">高级设置</label>
</div>
```

### 2. 控制的内容
- 控制 `inscriptionSizeContainer` 区域的显示/隐藏
- 该区域包含：
  - **铭文占用聪**：设置每个铭文占用的聪数量 (bc1p最低330, bc1q最低294)
  - **保护UTXO**：设置保护低于指定聪值的UTXO

### 3. JavaScript功能 (`public/index.js`)

#### 3.1 开关切换逻辑
```javascript
// 高级设置开关变化事件
advancedSettingsCheckbox.on('change', function() {
    const isChecked = $(this).prop('checked');
    if (isChecked) {
        inscriptionSizeContainer.slideDown(300); // 显示高级设置区域
    } else {
        inscriptionSizeContainer.slideUp(300); // 隐藏高级设置区域
    }
    
    // 保存开关状态到本地存储
    if (!isLoading) {
        StorageManager.saveParams('advancedSettings');
    }
});
```

#### 3.2 本地存储支持
- **保存功能**：在 `StorageManager.saveParams` 中添加了 `advancedSettings` 支持
- **加载功能**：在 `StorageManager.loadParams` 中添加了状态恢复
- **存储键名**：`advancedSettings` + 网络后缀

#### 3.3 页面加载时状态恢复
```javascript
// 设置高级设置开关状态
const advancedSettingsEnabled = stored.advancedSettings === 'true' || stored.advancedSettings === true;
$('#advancedSettings').prop('checked', advancedSettingsEnabled);

// 根据高级设置开关状态显示或隐藏高级设置区域
if (advancedSettingsEnabled) {
    $('#inscriptionSizeContainer').show();
} else {
    $('#inscriptionSizeContainer').hide();
}
```

## 技术特点

### 1. 与现有代码集成
- 使用了现有的 `StorageManager` 模块进行状态管理
- 遵循了现有的代码结构和命名规范
- 与现有的表单开关样式保持一致

### 2. 用户体验优化
- **平滑动画**：使用 `slideDown(300)` 和 `slideUp(300)` 实现平滑的显示/隐藏效果
- **状态持久化**：开关状态会保存到本地存储，页面刷新后保持
- **即时反馈**：开关切换立即生效，无需额外操作

### 3. 响应式设计
- 使用现有的 `.form-switch` 样式类
- 与其他开关（如"同时铸造符文"、"低费率模式"）保持一致的外观

## 默认行为
- **初始状态**：高级设置默认关闭（隐藏）
- **首次使用**：用户需要手动开启高级设置才能看到相关选项
- **状态记忆**：一旦用户设置了开关状态，系统会记住该选择

## 兼容性
- 完全兼容现有功能，不影响其他模块
- 支持网络切换（主网/测试网）的独立状态保存
- 与现有的参数保存/加载机制完全集成

## 测试建议
1. **基本功能测试**：
   - 点击开关，验证高级设置区域的显示/隐藏
   - 刷新页面，验证开关状态是否正确恢复

2. **存储测试**：
   - 切换开关状态，检查本地存储中是否正确保存
   - 清除本地存储，验证默认状态

3. **网络切换测试**：
   - 在主网和测试网之间切换，验证各自的开关状态独立保存

## 实现完成
✅ HTML结构添加完成
✅ JavaScript逻辑实现完成  
✅ 本地存储集成完成
✅ 状态恢复功能完成
✅ 与现有代码完全集成

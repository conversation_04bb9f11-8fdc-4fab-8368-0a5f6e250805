//server.js 文件代码
const express = require('express');
const path = require('path');
const api = require('./api');
const cors = require('cors');

const app = express();

/*
// 自定义错误页面
const errorPage = `
<!DOCTYPE html>
<html>
<head>
  <title>Access Denied</title>
  <style>
    body {
      text-align: center;
      font-family: Arial, sans-serif;
      margin-top: 50px;
    }
    h1 {
      font-size: 24px;
      color: #fc0000;
    }
    p {
      font-size: 18px;
      margin-top: 20px;
    }
    a {
      color: #0000ff;
      text-decoration: underline;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <h1>无法在当前环境下运行</h1>
  <p>请点击以下链接复制网址：<a id="copyLink" onclick="copyToClipboard()">www.ybot.io</a>，到电脑或者手机自带浏览器中打开。</p>
  
  <script>
    function copyToClipboard() {
      const link = document.getElementById('copyLink');
      const tempInput = document.createElement('input');
      tempInput.value = link.textContent;
      document.body.appendChild(tempInput);
      tempInput.select();
      document.execCommand('copy');
      document.body.removeChild(tempInput);
      alert('网址已复制到剪贴板！');
    }
  </script>
</body>
</html>
`;

// 中间件函数，用于检查 User-Agent
function checkUserAgent(req, res, next) {
  const userAgent = req.headers['user-agent'];

  if (userAgent) {
    const isWechatBrowser = userAgent.includes('MicroMessenger');
    const isAndroidQQInternalBrowser = /MQQBrowser.*QQ/.test(userAgent);
    const isIOSQQInternalBrowser = /\sQQ/.test(userAgent) && !/MQQBrowser/.test(userAgent);

    if (isWechatBrowser || isAndroidQQInternalBrowser || isIOSQQInternalBrowser) {
      // 如果是微信内置浏览器、Android 或 iOS 的 QQ 内置浏览器，则重定向到自定义错误页面
      res.status(403).send(errorPage);
    } else {
      // 否则，允许访问
      next();
    }
  } else {
    // 如果没有 User-Agent 头信息，则允许访问
    next();
  }
}

// 将中间件函数应用到所有路由
app.use(checkUserAgent);
*/

app.use(cors());

// 添加这一行来解析JSON格式的请求体
app.use(express.json());

// 设置静态资源缓存头（排除 html 文件）
app.use(express.static(path.join(__dirname, 'public'), {
  setHeaders: (res, path) => {
    if (path.match(/\.(css|js|ico|svg|mp3)$/)) {
      res.setHeader('Cache-Control', 'public, max-age=604800'); // 7天缓存
    } else if (path.match(/\.html$/)) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); // 禁止缓存 html 文件
    }
  }
}));

// API路由
app.use('/api', api);

module.exports = app;
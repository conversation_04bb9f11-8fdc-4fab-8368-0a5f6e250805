const crypto = require('crypto');

// 网络配置
const NETWORK = process.env.NETWORK || 'mainnet'; // 'mainnet' or 'testnet'
const walletType = NETWORK === 'testnet' ? 'testnet' : 'main';

// API 配置
const API_URL = NETWORK === 'testnet' 
  ? 'https://mempool.space/testnet/'
  : 'https://mempool.space/';

// Labitbu 配置常量
const LABITBU_CONFIG = {
  TARGET_SIZE: 4096,
  CONTROL_BLOCK_DEPTH: 128,
  LABITBU_NUMS_TAG: 'Labitbu',
  LABITBU_NUMS_KEY: '96053db5b18967b5a410326ecca687441579225a6d190f398e2180deec6e429e',
  INSCRIPTION_VALUE: 330,
  MIN_CHANGE_VALUE: 330
};

// 图像配置
const IMAGE_CONFIG = {
  WIDTH: 44,
  HEIGHT: 59,
  WEBP_QUALITY: 80,
  HUE_SHIFT_RANGE: 360,
  MIN_SATURATION: 0.40
};

// 生成 NUMS 公钥的函数
function numsFromTag(tag) {
  let ctr = 0;
  while (true) {
    const hash = crypto.createHash('sha256');
    hash.update(Buffer.from(tag));
    hash.update(Buffer.from(ctr.toString(16).padStart(8, '0'), 'hex'));
    const candidate = hash.digest();
    
    // 检查是否是有效的 x-only 公钥
    if (candidate[0] <= 0x7f) {
      return candidate.toString('hex');
    }
    ctr++;
  }
}

// 预计算 Labitbu NUMS 密钥
const LABITBU_NUMS_PUBKEY = numsFromTag(LABITBU_CONFIG.LABITBU_NUMS_TAG);

module.exports = {
  NETWORK,
  walletType,
  API_URL,
  LABITBU_CONFIG,
  IMAGE_CONFIG,
  LABITBU_NUMS_PUBKEY,
  numsFromTag
};

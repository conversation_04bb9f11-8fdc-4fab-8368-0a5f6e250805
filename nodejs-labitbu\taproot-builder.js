const crypto = require('crypto');
const { Address, Tap, Tx, Signer } = require('@cmdcode/tapscript');
const { LABITBU_CONFIG, LABITBU_NUMS_PUBKEY, walletType } = require('./config');
const { buildMerklePathFromBytes } = require('./labitbu-generator');

/**
 * 创建花费脚本
 * @param {string} pubkey - 公钥 (不带前缀)
 * @returns {Array} 脚本数组
 */
function createSpendScript(pubkey) {
  return [pubkey, 'OP_CHECKSIG'];
}

/**
 * 创建 Taproot 花费信息
 * @param {string} pubkey - x-only 公钥
 * @param {Buffer} payloadBytes - 载荷字节数据
 * @returns {Object} Taproot 花费信息
 */
function createTaprootSpendInfo(pubkey, payloadBytes) {
  try {
    // 创建花费脚本
    const spendScript = createSpendScript(pubkey);

    // 编码脚本为 tapleaf
    const tapleaf = Tap.encodeScript(spendScript);

    // 构建 Merkle 路径
    const merklePath = buildMerklePathFromBytes(payloadBytes);

    // 使用 NUMS 公钥和构建的 Merkle 根创建 Taproot 输出
    const [outputPubkey, cblock] = Tap.getPubKey(LABITBU_NUMS_PUBKEY, {
      target: tapleaf
    });

    return {
      outputKey: outputPubkey,
      controlBlock: cblock,
      tapleaf,
      spendScript,
      merklePath
    };
  } catch (error) {
    throw new Error(`创建 Taproot 花费信息失败: ${error.message}`);
  }
}

/**
 * 创建存款地址
 * @param {string} pubkeyHex - 十六进制公钥
 * @param {Buffer} payloadBytes - 载荷字节数据
 * @returns {string} Taproot 地址
 */
function createDepositAddress(pubkeyHex, payloadBytes) {
  try {
    const taprootInfo = createTaprootSpendInfo(pubkeyHex, payloadBytes);
    const address = Address.p2tr.fromPubKey(taprootInfo.outputKey, walletType);
    return address;
  } catch (error) {
    throw new Error(`创建存款地址失败: ${error.message}`);
  }
}

/**
 * 创建铸造交易
 * @param {Object} params - 参数对象
 * @param {string} params.pubkeyHex - 十六进制公钥
 * @param {Buffer} params.payloadBytes - 载荷字节数据
 * @param {number} params.amount - 金额 (satoshis)
 * @param {string} params.destinationAddress - 目标地址
 * @param {number} params.fee - 手续费 (satoshis)
 * @param {Array} params.inputs - 输入数组
 * @param {Array} params.prevTxouts - 前一个交易输出数组
 * @param {string} params.privateKey - 私钥 (用于签名)
 * @returns {Object} 签名后的交易
 */
function createMintTransaction(params) {
  try {
    const {
      pubkeyHex,
      payloadBytes,
      amount,
      destinationAddress,
      fee,
      inputs,
      prevTxouts,
      privateKey
    } = params;

    // 创建 Taproot 花费信息
    const taprootInfo = createTaprootSpendInfo(pubkeyHex, payloadBytes);
    
    // 验证目标地址
    if (!destinationAddress.startsWith('bc1p') && !destinationAddress.startsWith('tb1p')) {
      throw new Error('目标地址必须是 Taproot 地址');
    }

    // 创建交易输出
    const outputs = [{
      value: amount - fee,
      scriptPubKey: Address.toScriptPubKey(destinationAddress)
    }];

    // 创建交易
    const txData = Tx.create({
      vin: inputs.map((input, index) => ({
        txid: input.txid,
        vout: input.vout,
        prevout: {
          value: prevTxouts[index].value,
          scriptPubKey: prevTxouts[index].scriptPubKey || 
                       Address.toScriptPubKey(Address.p2tr.fromPubKey(taprootInfo.outputKey, walletType))
        }
      })),
      vout: outputs
    });

    // 签名交易
    const signature = Signer.taproot.sign(privateKey, txData, 0, {
      extension: taprootInfo.tapleaf
    });

    // 添加见证数据
    txData.vin[0].witness = [
      signature,
      taprootInfo.spendScript,
      taprootInfo.controlBlock
    ];

    // 编码交易
    const signedTx = Tx.encode(txData);

    return {
      txid: Tx.util.getTxid(signedTx.hex),
      hex: signedTx.hex,
      size: Tx.util.getTxSize(txData),
      fee,
      taprootInfo
    };
  } catch (error) {
    throw new Error(`创建铸造交易失败: ${error.message}`);
  }
}

/**
 * 估算交易大小
 * @param {Object} params - 参数对象
 * @returns {number} 交易虚拟大小 (vbytes)
 */
function estimateTransactionSize(params) {
  try {
    const {
      pubkeyHex,
      payloadBytes,
      inputCount = 1,
      outputCount = 1
    } = params;

    // 基础交易大小
    let size = 10; // 版本 (4) + 输入数量 (1) + 输出数量 (1) + 锁定时间 (4)
    
    // 输入大小
    size += inputCount * (32 + 4 + 1); // txid + vout + scriptSig长度
    
    // 输出大小
    size += outputCount * (8 + 1 + 34); // value + scriptPubKey长度 + scriptPubKey
    
    // 见证数据大小估算
    const taprootInfo = createTaprootSpendInfo(pubkeyHex, payloadBytes);
    const witnessSize = 1 + // 见证项目数量
                       1 + 64 + // 签名长度 + 签名
                       1 + taprootInfo.spendScript.length + // 脚本长度 + 脚本
                       1 + taprootInfo.controlBlock.length; // 控制块长度 + 控制块
    
    // 计算虚拟大小 (见证数据按 1/4 计算)
    const vsize = size + Math.ceil(witnessSize / 4);
    
    return vsize;
  } catch (error) {
    throw new Error(`估算交易大小失败: ${error.message}`);
  }
}

module.exports = {
  createSpendScript,
  createTaprootSpendInfo,
  createDepositAddress,
  createMintTransaction,
  estimateTransactionSize
};

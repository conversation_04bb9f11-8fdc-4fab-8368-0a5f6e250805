const crypto = require('crypto');

// Constants
const MAX_DIVISIBILITY = 38;
const MAX_SPACERS = 0b00000111_11111111_11111111_11111111;

// Tag Definitions
const Tag = {
  Body: 0,          // Mint's information
  Divisibility: 1,  // Divisibility, decimal places
  Flags: 2,
  Spacers: 3,       // Spacers
  Rune: 4,          // Rune name
  Symbol: 5,        // Symbol name
  Premine: 6,       // Premine
  Cap: 8,           // Cap
  Amount: 10,       // Single card amount
  HeightStart: 12,  // Starting block
  HeightEnd: 14,    // Ending block
  OffsetStart: 16,  // Starting offset
  OffsetEnd: 18,    // Ending offset
  Mint: 20,         // Rune ID information for mint
  Pointer: 22
};

// Flag Definitions
const Flag = {
  Etching: 0,     // Deployment
  Terms: 1,       // Deployment terms
  Turbo: 2,       // Upgradability
  Cenotaph: 127   // Monument
};

class RuneId {
  constructor(block, tx) {
    this.block = block;
    this.tx = tx;
  }

  toString() {
    return `${this.block}:${this.tx}`;
  }
}

class Edict {
  constructor(rune_id, amount, output) {
    this.rune_id = rune_id;
    this.amount = amount;
    this.output = output;
  }

  toString() {
    return `Edict(RuneId=${this.rune_id}, Amount=${this.amount}, Output=${this.output})`;
  }
}

function decodeLeb128(data) {
  const result = [];
  let current = 0;
  let shift = 0;
  for (const byte of data) {
    current |= (byte & 0x7f) << shift;
    if (byte & 0x80) {
      shift += 7;
      continue;
    }
    result.push(current);
    current = 0;
    shift = 0;
  }
  return result;
}

function leb128Encode(value) {
  if (value === 0) {
    return Buffer.from([0]);
  }
  const result = [];
  while (value > 0) {
    let byte = value & 0x7F;
    value >>= 7;
    if (value !== 0) {
      byte |= 0x80;
    }
    result.push(byte);
  }
  return Buffer.from(result);
}

function encodeRuneIdWithTag(tag, block, tx) {
  const tagEncoded = leb128Encode(tag);
  const blockEncoded = leb128Encode(block);
  const txEncoded = leb128Encode(tx);
  const combinedEncoded = Buffer.concat([tagEncoded, blockEncoded, tagEncoded, txEncoded]);
  const hexString = combinedEncoded.toString('hex');
  return hexString;
}

function runeToString(value) {
  if (value === 2 ** 128 - 1) {
    return "BCGDENLQRQWDSLRUGSNLBTMFIJAV";
  }
  const result = [];
  while (value > 0) {
    const [quotient, remainder] = [Math.floor(value / 26), (value - 1) % 26];
    value = quotient;
    result.push(String.fromCharCode(remainder + 65));
  }
  result.reverse();
  return result.join('');
}

function insertSpacers(name, spacers) {
  const result = [];
  const binSpacers = spacers.toString(2).padStart(name.length, '0');
  for (let i = 0; i < name.length; i++) {
    result.push(name[i]);
    if (binSpacers[name.length - 1 - i] === '1') {
      result.push('•');
    }
  }
  return result.join('');
}

function parseByTag(data) {
  const fields = {};
  let i = 0;
  while (i < data.length) {
    const tag = data[i];
    if (tag === Tag.Body) {
      fields[tag] = data.slice(i + 1);
      break;
    } else {
      const value = i + 1 < data.length ? data[i + 1] : null;
      if (tag in fields) {
        fields[tag].push(value);
      } else {
        fields[tag] = [value];
      }
      i += 2;
    }
  }
  return fields;
}

function checkAndClearFlag(flags, flag) {
  const mask = 1 << flag;
  const result = (flags & mask) !== 0;
  flags &= ~mask;
  return [result, flags];
}

function formatAmount(amount, divisibility) {
  if (amount === 0) {
    return 0;
  }
  const formattedAmount = amount / (10 ** divisibility);
  if (Number.isInteger(formattedAmount)) {
    return formattedAmount.toString();
  } else {
    return formattedAmount.toFixed(divisibility);
  }
}

function parseDeltaEncodedIds(data) {
  const edicts = [];
  let index = 0;
  while (index + 3 < data.length) {
    const blockHeight = data[index];
    const transactionIndex = data[index + 1];
    const amount = data[index + 2];
    const output = data[index + 3];
    const runeId = new RuneId(blockHeight, transactionIndex);
    edicts.push(new Edict(runeId, amount, output));
    index += 4;
  }
  return edicts;
}

function parseTerms(fields, divisibility) {
  const terms = {};
  if (Tag.Cap in fields) {
    terms['cap'] = fields[Tag.Cap][0];
  }
  if (Tag.Amount in fields) {
    terms['amount'] = formatAmount(fields[Tag.Amount][0], divisibility);
  }
  const heightStart = fields[Tag.HeightStart] ? fields[Tag.HeightStart][0] : null;
  const heightEnd = fields[Tag.HeightEnd] ? fields[Tag.HeightEnd][0] : null;
  terms['height'] = [heightStart, heightEnd];
  const offsetStart = fields[Tag.OffsetStart] ? fields[Tag.OffsetStart][0] : null;
  const offsetEnd = fields[Tag.OffsetEnd] ? fields[Tag.OffsetEnd][0] : null;
  terms['offset'] = [offsetStart, offsetEnd];
  return terms;
}

function parseMint(fields) {
  if (Tag.Mint in fields && fields[Tag.Mint].length >= 2) {
    const [block, tx] = [fields[Tag.Mint][0], fields[Tag.Mint][1]];
    return new RuneId(block, tx);
  }
  return null;
}

function parseEtching(fields) {
  const etching = {};
  let flags = fields[Tag.Flags] ? fields[Tag.Flags][0] : 0;
  const [isEtching, newFlags] = checkAndClearFlag(flags, Flag.Etching);
  flags = newFlags;

  if (!isEtching) {
    return etching;
  }

  const divisibility = fields[Tag.Divisibility] ? fields[Tag.Divisibility][0] : 0;
  etching['divisibility'] = divisibility;
  const premine = fields[Tag.Premine] ? fields[Tag.Premine][0] : 0;
  etching['premine'] = formatAmount(premine, divisibility);
  const runeValue = fields[Tag.Rune] ? fields[Tag.Rune][0] : null;
  etching['rune'] = runeValue;
  const spacers = fields[Tag.Spacers] ? fields[Tag.Spacers][0] : 0;
  etching['spacers'] = spacers;
  const symbol = fields[Tag.Symbol] ? fields[Tag.Symbol][0] : 0;
  etching['symbol'] = 0 < symbol && symbol < 0x110000 ? String.fromCharCode(symbol) : null;
  const terms = parseTerms(fields, divisibility);
  const [hasTerms, newFlags2] = checkAndClearFlag(flags, Flag.Terms);
  flags = newFlags2;
  etching['terms'] = hasTerms ? terms : null;
  const [isTurbo, newFlags3] = checkAndClearFlag(flags, Flag.Turbo);
  flags = newFlags3;
  etching['turbo'] = isTurbo;
  etching['name'] = insertSpacers(runeToString(runeValue), spacers);
  if (terms) {
    const supply = parseInt(premine) + parseInt(terms['cap']) * parseInt(terms['amount']);
    etching['supply'] = supply;
  }
  return etching;
}

function parseRunestone(data) {
  const decodedData = decodeLeb128(Buffer.from(data, 'hex'));
  //console.log(decodedData);
  const fields = parseByTag(decodedData);
  let edicts = [];
  if (Tag.Body in fields) {
    edicts = parseDeltaEncodedIds(fields[Tag.Body]);
  }
  const etching = parseEtching(fields);
  const mint = parseMint(fields);
  const pointer = fields[Tag.Pointer] ? fields[Tag.Pointer][0] : null;
  return {
    edicts: edicts,
    etching: etching,
    mint: mint,
    pointer: pointer
  };
}

function encodeRuneId(runeId) {
  const [block, tx] = runeId.split(':').map(Number);
  return encodeRuneIdWithTag(Tag.Mint, block, tx);
}

function decodeRunestone(data) {
  const runestone = parseRunestone(data);
  return runestone['mint'].toString();
}

module.exports = {
  encodeRuneId,
  decodeRunestone,
};